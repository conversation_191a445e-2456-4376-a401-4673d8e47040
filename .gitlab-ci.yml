---
variables:
  ANSIBLE_FORCE_COLOR: "true"
  ANSIBLE_CONFIG: ./ansible.cfg
  PLAYBOOK_HOSTS:
    value: "some hostname(-s)"
    description: "Set host name(-s) to run base_server.yml on"

stages:
  - test
  - check_hosts_changes
  - cleanup

include:
  - project: 'infrastructure/unified-security-pipeline'
    ref: main
    file: '.gitlab-ci-module.yml'

yamllint:
  stage: test
  image: registry.gitlab.com/pipeline-components/yamllint:latest
  script:
    - yamllint .

ansible-lint:
  stage: test
  image: registry.gitlab.com/pipeline-components/ansible-lint:latest
  script:
    - ansible-lint --show-relpath .
    - ansible-lint --force-color --offline -p roles/

shellcheck:
  stage: test
  image: koalaman/shellcheck-alpine
  script:
    - find . -name *.sh -type f &&  find . -name *.sh -type f | xargs shellcheck || echo "didnt find sh files"

detect_removed_hosts:
  stage: check_hosts_changes
  image: ubuntu:latest
  script:
    - apt update && apt install -y git openssh-client openssl jq ansible
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -p 2202 gitlab.sl.local >> ~/.ssh/known_hosts
    - >
      openssl s_client -connect ipa-ca.sl.local:443 -showcerts </dev/null 2>/dev/null |
      sed -e '/-----BEGIN/,/-----END/!d' >> /etc/ssl/certs/ca-certificates.crt
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Deployer"
    - git fetch origin main
    - |
      PREV_REV=$(git rev-parse HEAD~1)
      CURRENT_REV=$(git rev-parse HEAD)
      if git diff --name-only $PREV_REV $CURRENT_REV | grep -q 'inventories/hosts'; then
        echo "inventories/hosts modified"
      else
        echo "No changes in inventories/hosts"
        exit 0
      fi
    - git diff $PREV_REV $CURRENT_REV -- inventories/hosts > inventory_hosts.diff
    - cat inventory_hosts.diff
    - |
      if [ -s inventory_hosts.diff ]; then
        echo "Changes detected in inventory_hosts.diff"
        removed_lines=$(awk '/^-/ && !/^--/ {print substr($0, 2)}' inventory_hosts.diff)
        echo "Removed lines:"
        echo "$removed_lines"
        added_lines=$(awk '/^\+/ && !/^\+\+/ {print substr($0, 2)}' inventory_hosts.diff)
        echo "Added lines:"
        echo "$added_lines"
        if [ -z "$removed_lines" ]; then
          echo "No removed lines detected, skipping verification"
          touch confirmed_removed_hosts.txt
        else
          echo "$removed_lines" | awk '/ansible_host/' > removed_lines.txt
          echo "$added_lines" > added_lines.txt
          echo "Verifying removed hosts with ansible inventory"
          ansible-inventory --list | jq -r 'keys[]' > current_hosts.txt
          comm -23 <(sort removed_lines.txt) <(sort current_hosts.txt) > confirmed_removed_hosts.txt
          echo "Confirmed removed hosts:"
          cat confirmed_removed_hosts.txt
        fi
      else
        echo "No changes detected in inventory_hosts.diff"
      fi
    - exit 0
  artifacts:
    paths:
      - confirmed_removed_hosts.txt
  only:
    - main


remove_hosts:
  stage: cleanup
  image: cytopia/ansible:latest
  script:
    - apk add --no-cache openssh-client openssl
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - echo "$ANSIBLE_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
    - echo "$ANSIBLE_VAULT_PASSWORD" > ~/.vault_pass.txt
    - export ANSIBLE_VAULT_PASSWORD_FILE=~/.vault_pass.txt
    - pip3 install proxmoxer requests pyzabbix
    - ansible-galaxy collection install community.zabbix
    - ansible-galaxy collection install community.general
    - >
      openssl s_client -connect ipa-ca.sl.local:443 -showcerts </dev/null 2>/dev/null |
      sed -e '/-----BEGIN/,/-----END/!d' >> /etc/ssl/certs/ca-certificates.crt
    - >
      if [ -f confirmed_removed_hosts.txt ] && [ -s confirmed_removed_hosts.txt ]; then
        while IFS= read -r line; do
          remove_host_name=$(echo $line | awk '{print $1}')
          echo "Removing host: $remove_host_name"
          ansible-playbook -i inventories/hosts playbook/remove_host.yml --extra-vars "remove_host_name=${remove_host_name}"
        done < confirmed_removed_hosts.txt
      else
        echo "No hosts to remove."
      fi
  dependencies:
    - detect_removed_hosts
  only:
    - main
