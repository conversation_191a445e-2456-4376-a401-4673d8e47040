---
- name: Make state netplan / Set netplan_cur_date variable
  ansible.builtin.set_fact:
    netplan_cur_date: "{{ now(utc=true, fmt='%Y-%m-%d-%H-%M-%S') }}"

- name: Make state netplan / Create temporary directory for netplan state
  ansible.builtin.file:
    path: "{{ netplan_state_directory_prefix }}{{ netplan_cur_date }}{{ netplan_state_directory_suffix }}"
    state: directory
    mode: '600'
  changed_when: false

- name: Make state netplan / Copy content of /etc/netplan ot temporary directory for netplan state
  ansible.builtin.copy:
    src: "{{ netplan_config_directory }}"
    dest: "{{ netplan_state_directory_prefix }}{{ netplan_cur_date }}{{ netplan_state_directory_suffix }}"
    mode: "600"
    remote_src: true

- name: Clear netplan configuration directory except goal configuration file _phase 1/2_
  ansible.builtin.find:
    paths: "{{ netplan_config_directory }}"
    file_type: file
    recurse: true
    excludes:
      - "{{ netplan_config_file }}"
  register: found_files

- name: Clear netplan configuration directory except goal configuration file _phase 2/2_
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: absent
  loop: "{{ found_files['files'] }}"
  loop_control:
    label: "{{ item.path }}"

- name: Print configuration variables
  ansible.builtin.debug:
    msg:
      - "netplan_nic: {{ netplan_nic }}"
      - "netplan_bonds: {{ netplan_bonds | default('NOT_DEFINED') }}"

- name: Template netplan config
  ansible.builtin.template:
    src: "{{ netplan_config_file }}.j2"
    dest: "{{ netplan_config_directory }}/{{ netplan_config_file }}"
    mode: "600"

- name: Apply netplan configuration
  ansible.builtin.command: netplan apply --state {{ netplan_state_directory_prefix }}{{ netplan_cur_date }} --debug
  register: netplan_apply_output
  changed_when: true

- name: Print output of execution applaying configuration
  ansible.builtin.debug:
    var: netplan_apply_output
