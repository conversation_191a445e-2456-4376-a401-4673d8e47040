---
netplan_config_file: 00-config.yaml
netplan_config_directory: /etc/netplan

netplan_default_gateway: "{{ network_default_gateway }}"
netplan_network_size: "{{ network_size }}"

netplan_state_directory_prefix: /tmp/netplan_old_state_
netplan_state_directory_suffix: /etc

netplan_bond_mii_monitor_interval: 1000
netplan_bond_up_delay: 3000
netplan_bond_down_delay: 2000
netplan_bond_fail_over_mac_policy: follow

netplan_bonds:
  bond0:
    dhcp4: false
    macaddress: "{{ mac_address }}"
    addresses:
      - "{{ ansible_host }}/{{ netplan_network_size }}"
    interfaces:
      - "{{ netplan_nic['primary_nic'] }}"
      - "{{ netplan_nic['secondary_nic'] }}"
    mtu: "{{ netplan_mtu }}"
    routes:
      - to: default
        via: "{{ netplan_default_gateway }}"
    nameservers:
      search:
        - "{{ ipa_domain }}"
      addresses:
        - "{{ ipa_vip }}"
        - "{{ hostvars['hz-prod-ldap-01']['ansible_host'] }}"
        - "{{ hostvars['hz-prod-ldap-02']['ansible_host'] }}"
        - "{{ hostvars['hz-prod-ldap-03']['ansible_host'] }}"
    parameters:
      mode: active-backup
      primary: "{{ netplan_nic['primary_nic'] }}"
      mii-monitor-interval: "{{ netplan_bond_mii_monitor_interval }}"
      up-delay: "{{ netplan_bond_up_delay }}"
      down-delay: "{{ netplan_bond_down_delay }}"
      fail-over-mac-policy: "{{ netplan_bond_fail_over_mac_policy }}"
