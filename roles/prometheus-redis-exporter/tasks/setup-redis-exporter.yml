---
- name: Create the Node Exporter group
  ansible.builtin.group:
    name: "{{ redis_exporter_service_group }}"
    state: present
    system: true

- name: Create the Node Exporter user
  ansible.builtin.user:
    name: "{{ redis_exporter_service_username }}"
    groups: "{{ redis_exporter_service_group }}"
    append: true
    shell: /bin/bash
  when: redis_exporter_manage_user

- name: Check if the version of Node Exporter already exists
  ansible.builtin.stat:
    path: "/opt/redis_exporter-v{{ redis_exporter_version }}.linux-amd64"
  register: redis_exporter_install_path

- name: Download Node Exporter
  ansible.builtin.get_url:
    url: "{{ redis_exporter_download_url }}"
    dest: "/tmp/redis_exporter-{{ redis_exporter_version }}.tar.gz"
    mode: 0644
  when: not redis_exporter_install_path.stat.exists

- name: Extract Node Exporter into the install directory
  ansible.builtin.unarchive:
    src: "/tmp/redis_exporter-{{ redis_exporter_version }}.tar.gz"
    dest: "/opt/"
    copy: false
    owner: "{{ redis_exporter_service_username }}"
    group: "{{ redis_exporter_service_group }}"
  when: not redis_exporter_install_path.stat.exists

- name: Create a symlink for /opt/redis_exporter
  ansible.builtin.file:
    src: "/opt/redis_exporter-v{{ redis_exporter_version }}.linux-amd64"
    dest: /opt/redis_exporter
    owner: "{{ redis_exporter_service_username }}"
    group: "{{ redis_exporter_service_group }}"
    state: link
  when: not redis_exporter_install_path.stat.exists

- name: Copy the Node Exporter systemd service file
  ansible.builtin.template:
    src: redis_exporter.service.j2
    dest: /etc/systemd/system/redis_exporter.service
    owner: root
    group: root
    mode: 0644
  notify:
    - Reload systemd
    - Restart redis_exporter

- name: Ensure Node Exporter is started and enabled on boot
  ansible.builtin.service:
    name: redis_exporter
    state: started
    enabled: true
