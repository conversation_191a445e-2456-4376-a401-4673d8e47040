[Unit]
Description=Prometheus Redis Exporter
After=network.target

[Service]
Type=simple
User={{ redis_exporter_service_username }}
Group={{ redis_exporter_service_group }}
ExecStart=/opt/redis_exporter/redis_exporter {% for enabled in redis_exporter_enabled_collectors %}--collector.{{ enabled }} {% endfor %} {% for disabled in redis_exporter_disabled_collectors %}--no-collector.{{ disabled }} {% endfor %} {% for flag, flag_value in redis_exporter_config_flags.items() %}--{{ flag }}={{ flag_value }} {% endfor %} {% if redis_requirepass is defined %}--redis.password={{ redis_requirepass }} {% endif %} 

SyslogIdentifier=redis_exporter
Restart=always

[Install]
WantedBy=multi-user.target
