---
# Openresty
openresty_ver: '1.21.4.1'
openresty_mirror: https://openresty.org/download
openresty_build_dir: /usr/local/src/openresty
openresty_install_dir: /etc/openresty
openresty_timeout_seconds: 600
openresty_log_dir: /var/log/openresty
openresty_sites_dir: /etc/openresty/nginx/sites

openresty_name: openresty-{{ openresty_ver }}
openresty_tgz: '{{ openresty_name }}.tar.gz'
openresty_tmp_tgz: /tmp/{{ openresty_tgz }}
openresty_url: '{{ openresty_mirror }}/{{ openresty_tgz }}'
openresty_build_subdir: '{{ openresty_build_dir }}/{{ openresty_name }}'


openresty_pkgs:
  - wget
  - libreadline-dev
  - libncurses5-dev
  - libpcre3-dev
  - libssl-dev
  - libldap2-dev
  - zlib1g-dev
  - perl
  - make
  - build-essential

# Keepalived
openresty_keepalived_vip:
openresty_keepalived_etc_dir: /etc/keepalived
openresty_keepalived_auth_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          30636463336231323735343266333134633333326135323162656561326635336336323239636634
          3230393663316233623437666331313936613135666664660a306634346461376436633035383736
          61306236303261303066656465666265613835363633343765326330326234333631396563666135
          3831373934343961360a313862373433386333666665666134653031383434656563633238323866
          39366364323761613031343963366538353661633438396332306166613937633036
