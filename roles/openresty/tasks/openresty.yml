---
- name: Install os pkg deps
  become: true
  become_user: root
  ansible.builtin.package:
    name: '{{ openresty_pkgs }}'
    state: present
  tags: openresty

- name: Downloading {{ openresty_url }}
  become: true
  become_user: root
  ansible.builtin.get_url:
    url: '{{ openresty_url }}'
    dest: '{{ openresty_tmp_tgz }}'
    mode: 0644
    timeout: '{{ openresty_timeout_seconds }}'
  tags: openresty

- name: Mkdir {{ openresty_build_dir }}
  become: true
  become_user: root
  ansible.builtin.file:
    path: '{{ openresty_build_dir }}'
    state: directory
    mode: '0755'
  tags: openresty

- name: Unarchive openresty
  become: true
  become_user: root
  ansible.builtin.unarchive:
    remote_src: true
    src: '{{ openresty_tmp_tgz }}'
    dest: '{{ openresty_build_dir }}'
    creates: '{{ openresty_build_subdir }}'
  tags: openresty

- name: Configure openresty
  become: true
  become_user: root
  ansible.builtin.command: >-
    ./configure
    --prefix={{ openresty_install_dir }}
    --with-pcre-jit
    --with-ipv6
  args:
    chdir: '{{ openresty_build_subdir }}'
    creates: '{{ openresty_build_dir }}/{{ openresty_name }}/Makefile'
  changed_when: false
  tags: openresty

- name: Make build install
  become: true
  become_user: root
  ansible.builtin.command: make build install
  args:
    chdir: '{{ openresty_build_subdir }}'
    creates: '{{ openresty_install_dir }}/nginx/sbin/nginx'
  changed_when: false
  tags: openresty

- name: Cleanup
  become: true
  become_user: root
  with_items:
    - '{{ openresty_build_dir }}'
    - '/tmp/{{ openresty_tgz }}'
  ansible.builtin.file:
    path: '{{ item }}'
    state: absent
  tags: openresty

- name: Copy nginx conf
  ansible.builtin.template:
    src: nginx.conf.j2
    dest: '{{ openresty_install_dir }}/nginx/conf/nginx.conf'
    mode: '0644'
  tags: openresty

- name: Mkdir {{ openresty_sites_dir }}
  become: true
  become_user: root
  ansible.builtin.file:
    path: '{{ openresty_sites_dir }}'
    state: directory
    mode: '0755'
  tags: openresty

- name: Copy default nginx site conf
  ansible.builtin.template:
    src: default.conf.j2
    dest: '{{ openresty_sites_dir }}/default.conf'
    mode: '0644'
  tags: openresty
  notify: Restart openresty

- name: Mkdir {{ openresty_log_dir }}
  become: true
  become_user: root
  ansible.builtin.file:
    path: '{{ openresty_log_dir }}'
    state: directory
    mode: '0755'
    owner: www-data
    group: www-data
  tags: openresty

- name: Copy service openresty
  ansible.builtin.template:
    src: openresty.service.j2
    dest: '/etc/systemd/system/openresty.service'
    mode: '0644'
  tags: openresty

- name: Starting service openresty
  ansible.builtin.systemd:
    name: openresty
    state: started
    enabled: true
  tags: openresty
