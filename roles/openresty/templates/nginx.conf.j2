user www-data;
worker_processes  auto;
pid /run/openresty.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    ssl_protocols TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
    ssl_prefer_server_ciphers on;
    access_log /var/log/openresty/access.log;
    error_log /var/log/openresty/error.log;
    gzip  on;
    gzip_disable "msie6";
    include ../sites/*;
}
