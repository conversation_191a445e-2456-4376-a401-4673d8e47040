[Unit] 
Description=Openresty
After=network.target

[Service] 
Type=forking
PIDFile=/run/openresty.pid
ExecStartPre={{ openresty_install_dir }}/bin/openresty -t -q -g 'daemon on; master_process on;'
ExecStart={{ openresty_install_dir }}/bin/openresty -g 'daemon on; master_process on;'
ExecReload={{ openresty_install_dir }}/bin/openresty -g 'daemon on; master_process on;' -s reload
ExecStop=-/sbin/start-stop-daemon --quiet --stop --retry QUIT/5 --pidfile /run/openresty.pid
TimeoutStopSec=5
KillMode=mixed

[Install] 
WantedBy=multi-user.target
