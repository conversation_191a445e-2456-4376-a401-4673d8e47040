{% if '-01' in inventory_hostname %}
global_defs {
       router_id {{ inventory_hostname }}
}
 
vrrp_script chk_nginx {
       script "{{ openresty_keepalived_etc_dir }}/openresty_check.sh"
               interval 2
               weight 5
               rise 1
               fall 5
}
 
vrrp_instance VI_1 {
    state BACKUP
    interface {{ ansible_default_ipv4.interface }} 
    virtual_router_id 51
    mcast_src_ip {{ ansible_ssh_host }}
    priority 100
    advert_int 1
    nopreempt
    authentication {
        auth_type PASS
        auth_pass {{ openresty_keepalived_auth_pass }}
    }

    virtual_ipaddress {
        {{ openresty_keepalived_vip }}
    }
 
    track_script {
               chk_nginx
    }
}
{% endif %}
{% if '-02' in inventory_hostname %}
global_defs {
   router_id {{ inventory_hostname }}
}
 
vrrp_script chk_nginx {
    script "{{ openresty_keepalived_etc_dir }}/openresty_check.sh"
    interval 2
    weight 5
    rise 1
    fall 1
}
 
vrrp_instance VI_1 {
    state BACKUP
    interface {{ ansible_default_ipv4.interface }}
    virtual_router_id 51
    mcast_src_ip {{ ansible_ssh_host }}
    priority 90
    advert_int 1
    nopreempt
    authentication {
        auth_type PASS
        auth_pass {{ openresty_keepalived_auth_pass }}
    }
    virtual_ipaddress {
        {{ openresty_keepalived_vip }}
    }
 
    track_script {
       chk_nginx
    }
}
{% endif %}
