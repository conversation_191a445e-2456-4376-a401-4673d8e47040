server {
    # Listen on port 80.
    listen 80 default_server;
    listen [::]:80 default_server;
    # The document root.
    root /etc/openresty/nginx/html;
    # Add index.php if you are using PHP.
    index index.html index.htm;
    # The server name, which isn't relevant in this case, because we only have one.
    server_name _;
    # When we try to access this site...
    location / {
    # ... first attempt to serve request as file, then as a directory,
    # then fall back to displaying a 404.
    try_files $uri $uri/ =404;
    }
    # Redirect server error pages to the static page /50x.html.
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
    root /etc/openresty/nginx/html;
    }
}
