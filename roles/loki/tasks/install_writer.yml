---
- name: Create loki writer dir
  ansible.builtin.file:
    path: "{{ loki_home_dir }}"
    state: directory
    mode: '0755'

- name: Create loki writer config
  ansible.builtin.template:
    src: loki.yaml.j2
    dest: '{{ loki_home_dir }}/loki.yaml'
    mode: '0644'

- name: Create writer container
  community.docker.docker_container:
    image: "grafana/loki:{{ loki_writer_image_ver }}"
    name: loki-writer
    state: started
    restart: true
    restart_policy: always
    network_mode: host
    volumes:
      - '{{ loki_home_dir }}:/etc/loki/'
    command:
      - -config.file=/etc/loki/loki.yaml
      - -target=write
