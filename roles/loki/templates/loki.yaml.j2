auth_enabled: false

http_prefix:

server:
  http_listen_address: {{ ansible_ssh_host }}
  grpc_listen_address: {{ ansible_ssh_host }}
  http_listen_port: {{ loki_http_listen_port }}
  grpc_listen_port: {{ loki_grpc_listen_port }}
  log_level: {{ loki_log_level }}

common:
  storage:
    {{ loki_storage_type }}:
      endpoint: https://{{ loki_storage_url }}
      region: {{ loki_region_s3_region }}
      insecure: false
      sse_encryption: false
      bucketnames: {{ loki_storage_bucket }}
      access_key_id: {{ loki_storage_user }}
      secret_access_key: {{ loki_storage_key }}
      s3forcepathstyle: true
      http_config:
        idle_conn_timeout: 90s
        response_header_timeout: 0s
        insecure_skip_verify: true

memberlist:
  join_members: ["{{ groups['loki_readers'] | map('extract', hostvars, ['ansible_host']) | join('", "') }}", "{{ groups['loki_writers'] | map('extract', hostvars, ['ansible_host']) | join('", "') }}"]
  dead_node_reclaim_time: 30s
  gossip_to_dead_nodes_time: 15s
  left_ingesters_timeout: 30s
  bind_addr: ['{{ ansible_ssh_host }}']
  bind_port: {{ loki_member_bind_port }}
  gossip_interval: 2s

ingester:
  lifecycler:
    join_after: 10s
    observe_period: 5s
    ring:
      replication_factor: 1
      kvstore:
        store: memberlist
    final_sleep: 0s
  chunk_idle_period: 1m
  wal:
    enabled: true
    dir: {{ loki_wal_dir }}
  max_chunk_age: 1m
  chunk_retain_period: 30s
  chunk_encoding: snappy
  chunk_target_size: 1.572864e+06
  chunk_block_size: 262144
  flush_op_timeout: 10s

schema_config:
  configs:
  - from: 2020-08-01
    store: boltdb-shipper
    object_store: {{ loki_storage_type }}
    schema: v11
    index:
      prefix: {{ loki_index_prefix }}
      period: 24h

storage_config:
  boltdb_shipper:
    shared_store: {{ loki_storage_type }}
    active_index_directory: /tmp/index
    cache_location: /tmp/boltdb-cache


limits_config:
  max_cache_freshness_per_query: '10m'
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 30m
  ingestion_rate_mb: 10
  ingestion_burst_size_mb: 20
  # parallelize queries in 15min intervals
  split_queries_by_interval: 15m

chunk_store_config:
  max_look_back_period: 336h

table_manager:
  retention_deletes_enabled: true
  retention_period: 336h

query_range:
  # make queries more cache-able by aligning them with their step intervals
  align_queries_with_step: true
  max_retries: 5
  parallelise_shardable_queries: true
  cache_results: true

frontend:
  log_queries_longer_than: 5s
  compress_responses: true
  max_outstanding_per_tenant: 4096

query_scheduler:
  max_outstanding_requests_per_tenant: 4096

querier:
  query_ingesters_within: 2h
  max_concurrent: 2048

compactor:
  working_directory: /tmp/compactor
  shared_store: {{ loki_storage_type }}

analytics:
  reporting_enabled: false
