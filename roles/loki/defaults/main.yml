---

loki_version: 2.6.1
loki_home_dir: /opt/loki
loki_wal_dir: /loki/wal

loki_http_listen_port: 3100
loki_grpc_listen_port: 9095
loki_member_bind_port: 7946

loki_log_level: info

# s3
loki_storage_type: s3
loki_storage_url: 'nas-01.sl.local:9000'
loki_storage_user: s3_loki
loki_storage_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          31393936343861386431663662306438633338303539373035616136643862616434383532666566
          3864613934663737316565653536303733666338363861320a363338343266623434346662363263
          66373838333664633665373235356636383337636230396562303339643862366536326439313830
          6366353561356466650a393433353861366139393863326162633337656533316264303663303461
          6434
loki_storage_bucket: loki
loki_index_prefix: 'index_'
loki_region_s3_region: 'eu-central-1'

# reader
loki_reader_image_ver: '{{ loki_version }}'

# writer
loki_writer_image_ver: '{{ loki_version }}'
