---
# defaults file for roles/rabbitmq-ha-cluster
rabbitmq_config_file: rabbitmq.conf
rabbitmq_config_advanced_file: advanced.config

rabbitmq_ubuntu_repo: "deb https://dl.cloudsmith.io/public/rabbitmq/rabbitmq-server/deb/ubuntu {{ ansible_distribution_release }} main"
rabbitmq_ubuntu_repo_key: "https://dl.cloudsmith.io/public/rabbitmq/rabbitmq-server/gpg.9F4587F226208342.key"
rabbitmq_ubuntu_team_key: "0x0A9AF2115F4687BD29803A206B73A36E6026DFCA"

rabbitmq_ubuntu_erlang_from_rabbit: true
rabbitmq_ubuntu_erlang_repo: "deb https://dl.cloudsmith.io/public/rabbitmq/rabbitmq-erlang/deb/ubuntu {{ ansible_distribution_release }} main"
rabbitmq_ubuntu_erlang_repo_key: "https://dl.cloudsmith.io/public/rabbitmq/rabbitmq-erlang/gpg.E495BB49CC4BBE5B.key"

# current version if not defined
rabbitmq_ubuntu_version_defined: true
rabbitmq_ubuntu_version: 3.12.14-1
rabbitmq_ubuntu_erlang_version: 25.3

rabbitmq_config:
  plugins:
    - rabbitmq_management
    - rabbitmq_shovel
    - rabbitmq_shovel_management
    - rabbitmq_top
    - rabbitmq_auth_backend_ldap
    - rabbitmq_auth_backend_cache
    - rabbitmq_prometheus

rabbitmq_user: admin.rabbitmq.{{ ldap_rabbitmq_env }}
rabbitmq_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  34626132626463343766336633306336613161323662316162316534396262353130343233333063
  3366636431316634623561373961393835613433613636610a386137376430613963376166666638
  35393734303731653262633537383339666234386633633864326362326564343632346263336133
  6366646139313161370a386231656463643364323736306334396335636336616366356363323734
  3831

# config
rabbitmq_listen_tcp_port: 5673
rabbitmq_loopback_users: "true"
rabbitmq_disk_free_limit: 2.0
rabbitmq_cluster_partition_handling: autoheal
rabbitmq_auth_ldap_log: network
rabbitmq_heartbeat: 1800
rabbitmq_exporter_image: kbudde/rabbitmq-exporter:latest
rabbitmq_exporter_user: rabbitmq.exporter
rabbitmq_exporter_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  61323362633634323264383065393930643363626636333738373437333734303038306434643338
  6432333038336330373634663631663232643964646266650a393963613266336166636133633365
  35633963313033386639343536633737653862386234626234323664313363383534303539613433
  3362613061653563330a613961623835393439326665663830303536303539393765303438323935
  64393135663036623130616663383630323166633264313361663538303637336136
rabbitmq_ha_cluster_tcp_listen_options_backlog: 4096
rabbitmq_ha_cluster_tcp_listen_options_keepalive: "true"

rabbitmq_exporter_config:
  - rabbitmq_custom_exporter.json
  - rabbitmq_exporter.json

rabbitmq_ldap_groups:
  - "{{ ldap_rabbitmq_env }}-rabbit-access-all"
  - "{{ ldap_rabbitmq_env }}-rabbit-access-r"
  - "{{ ldap_rabbitmq_env }}-rabbit-admins"
  - "{{ ldap_rabbitmq_env }}-rabbit-monitoring"

rabbitmq_auth_cache_ttl: 14400
rabbitmq_ldap_conn_pool_size: 256
rabbitmq_ldap_idle_timeout: 300000

# Keepalived
rabbitmq_keepalived_vip:
rabbitmq_keepalived_etc_dir: /etc/keepalived
rabbitmq_keepalived_auth_pass: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  65613936636437316530613438323938383263333561336464313262376538646261353465326133
  6232653635356332633633306238643264303736623866630a653935643063613636306633366537
  65633432653934316538666638303362666438653961313835323162366363396261636662623333
  3936333737663361350a306531343430626633363235653233663431343062303136643761353038
  3766

# HAproxy
rabbitmq_haproxy_version: 3.0

rabbitmq_sysctl_conf:
  net.ipv4.ip_nonlocal_bind: 1

# UFW
rabbitmq_ufw_port_os: 22,2202,9100,10050,9102,7301,7500,7600,15672,5672,5673,15692,25672,9419,4369
rabbitmq_ufw_port_vip: 5672
