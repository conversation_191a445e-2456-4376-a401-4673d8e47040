---
# handlers file for roles/rabbitmq-ha-cluster
- name: Restart rabbitmq-server
  ansible.builtin.systemd:
    name: rabbitmq-server
    state: restarted
    enabled: true

- name: Restart consul-agent
  ansible.builtin.systemd:
    name: consul-agent
    state: restarted
    enabled: true
    daemon_reload: true

- name: Restart keepalived
  ansible.builtin.systemd:
    name: keepalived
    state: restarted
    enabled: true
    daemon_reload: true

- name: Restart haproxy
  ansible.builtin.systemd:
    name: haproxy
    state: restarted
    enabled: true
    daemon_reload: true
