---
- name: Create user in rabbitmq
  community.rabbitmq.rabbitmq_user:
    name: "{{ rabbitmq_user }}"
    password: "{{ rabbitmq_password }}"
    configure_priv: ".*"
    read_priv: ".*"
    write_priv: ".*"
    tags: "administrator"
    state: present
  run_once: true

- name: Add admin user to ldap but don't reset password if already exists
  community.general.ipa_user:
    name: "{{ rabbitmq_user }}"
    state: present
    givenname: "{{ rabbitmq_user }}"
    sn: "{{ rabbitmq_user }}"
    password: "{{ rabbitmq_password }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    update_password: on_create
  when: inventory_hostname == ansible_play_hosts[0]

- name: Adding admin to admin ldap group
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-admins"
    state: present
    append: true
    user: "{{ rabbitmq_user }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  when: inventory_hostname == ansible_play_hosts[0]
