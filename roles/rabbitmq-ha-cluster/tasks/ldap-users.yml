---
- name: Add user but don't reset password if already exists
  community.general.ipa_user:
    name: "{{ item['username'] }}"
    state: present
    givenname: "{{ item['username'] }}"
    sn: "{{ item['username'] }}"
    password: "{{ item['password'] }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    update_password: on_create
  with_items: "{{ rabbitmq_extra_vhosts }}"
