---
- name: Systctl configure
  ansible.posix.sysctl:
    name: "{{ item.key }}"
    value: "{{ item.value }}"
    state: present
    reload: true
  with_dict: "{{ rabbitmq_sysctl_conf }}"

- name: Add haproxy repository
  ansible.builtin.apt_repository:
    repo: ppa:vbernat/haproxy-{{ rabbitmq_haproxy_version }}

- name: Install HAProxy package
  ansible.builtin.apt:
    name: haproxy
    state: present
    update_cache: true

- name: Create haproxy conf
  ansible.builtin.template:
    src: haproxy.cfg.j2
    dest: "/etc/haproxy/haproxy.cfg"
    mode: 0644
  notify: Restart haproxy

- name: Starting haproxy service
  ansible.builtin.systemd:
    name: haproxy
    state: started
    enabled: true
    daemon_reload: true
