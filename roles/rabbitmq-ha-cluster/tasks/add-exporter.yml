---
- name: Create a rabbitmq-exporter docker container
  community.docker.docker_container:
    name: rabbitmq-exporter
    image: "{{ rabbitmq_exporter_image }}"
    restart: true
    restart_policy: always
    ports:
      - 9419:9419
    env:
      RABBIT_URL: "http://{{ ansible_ssh_host }}:15672"
      RABBIT_USER: "{{ rabbitmq_exporter_user }}"
      RABBIT_PASSWORD: "{{ rabbitmq_exporter_password }}"
      PUBLISH_PORT: "9419"

- name: Add exporter config for consul
  ansible.builtin.template:
    src: "{{ item }}.j2"
    dest: "/opt/consul-agent/consul.d/{{ item }}"
    mode: 0644
    owner: "consul"
    group: "consul"
  with_items: "{{ rabbitmq_exporter_config }}"
  notify: Restart consul-agent

- name: Add user to ldap but don't reset password if already exists
  community.general.ipa_user:
    name: "{{ rabbitmq_exporter_user }}"
    state: present
    givenname: "{{ rabbitmq_exporter_user }}"
    sn: "{{ rabbitmq_exporter_user }}"
    password: "{{ rabbitmq_exporter_password }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    update_password: on_create
  when: inventory_hostname == ansible_play_hosts[0]

- name: Adding user to ldap monitoring group
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-monitoring"
    state: present
    append: true
    user: "{{ rabbitmq_exporter_user }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  when: inventory_hostname == ansible_play_hosts[0]

- name: Adding user to ldap read vhost groups
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-access-r-{{ item['name'] }}"
    state: present
    append: true
    user: "{{ rabbitmq_exporter_user }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  when: inventory_hostname == ansible_play_hosts[0]
  with_items: "{{ rabbitmq_extra_vhosts }}"

- name: Adding user to ldap access vhost groups
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-access-{{ item['name'] }}"
    state: present
    append: true
    user: "{{ rabbitmq_exporter_user }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  when: inventory_hostname == ansible_play_hosts[0]
  with_items: "{{ rabbitmq_extra_vhosts }}"
