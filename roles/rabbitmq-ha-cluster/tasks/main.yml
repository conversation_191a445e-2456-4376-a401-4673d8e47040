---
# tasks file for roles/rabbitmq-ha-cluster
- name: Check all playing hosts have exactly one group included 'rabbitmq' in name
  ansible.builtin.assert:
    that: group_names | select('search', 'rabbitmq') | list | length == 1
    fail_msg: Host must have exactly one group with 'rabbitmq' in name
    success_msg: "Ok: Host have exactly one group with 'rabbitmq' in name"
  tags: always

- name: Set cluster name
  ansible.builtin.set_fact:
    cluster_name: "{{ (group_names | select('search', 'rabbitmq'))[0] }}"
  tags: always

- name: Install RabbitMQ
  ansible.builtin.import_tasks: install-rmq.yml
  tags: install-rmq

- name: Configure
  ansible.builtin.import_tasks: config.yml
  tags: configure

- name: Setup cluster
  ansible.builtin.import_tasks: rabbitmq-clustering.yml
  tags: setup-cluster

- name: Configure vhost
  ansible.builtin.import_tasks: rabbitmq-vhosts.yml
  when: rabbitmq_extra_vhosts is defined and inventory_hostname == ansible_play_hosts[0]
  tags: config-vhost

- name: Add ldap users
  ansible.builtin.import_tasks: ldap-users.yml
  when: rabbitmq_extra_vhosts is defined and inventory_hostname == ansible_play_hosts[0]
  tags: ldap-user

- name: Add ldap groups
  ansible.builtin.import_tasks: ldap-groups.yml
  when: rabbitmq_extra_vhosts is defined and inventory_hostname == ansible_play_hosts[0]
  tags: ldap-group

- name: Add admin user
  ansible.builtin.import_tasks: create-user.yml
  tags: admin-user

- name: Configure ufw
  ansible.builtin.import_tasks: ufw.yml
  when: rabbitmq_setup_ufw is defined
  tags: ufw

- name: Setup haproxy
  ansible.builtin.import_tasks: install-haproxy.yml
  tags: haproxy

- name: Install Keepalived
  ansible.builtin.import_tasks: install-keepalived.yml
  tags: keepalived

- name: Add rabbitmq exporter
  ansible.builtin.import_tasks: add-exporter.yml
  tags: exporter
