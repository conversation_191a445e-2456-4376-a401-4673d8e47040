---
- name: Check cluster state
  ansible.builtin.command: rabbitmqctl cluster_status --formatter json
  changed_when: false
  register: cluster_status

- name: Join to first node
  when:
    - (cluster_status.stdout|from_json).listeners | length < 3
    - inventory_hostname != ansible_play_hosts[0]
  block:
    - name: Stop rmq app
      ansible.builtin.command: rabbitmqctl stop_app
      changed_when: false
    - name: Join node to first
      ansible.builtin.command: "rabbitmqctl join_cluster rabbit@{{ ansible_play_hosts[0] }}"
      changed_when: false
    - name: Start rmq app
      ansible.builtin.command: rabbitmqctl start_app
      changed_when: false
