---
- name: Adding default groups
  community.general.ipa_group:
    name: "{{ item }}"
    state: present
    append: true
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  with_items: "{{ rabbitmq_ldap_groups }}"

- name: Adding configure vhost groups
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-access-c-{{ item['name'] }}"
    state: present
    append: true
    user: "{{ item['username'] }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  with_items: "{{ rabbitmq_extra_vhosts }}"

- name: Adding read vhost groups
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-access-r-{{ item['name'] }}"
    state: present
    append: true
    user: "{{ item['username'] }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  with_items: "{{ rabbitmq_extra_vhosts }}"

- name: Adding write vhost groups
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-access-w-{{ item['name'] }}"
    state: present
    append: true
    user: "{{ item['username'] }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  with_items: "{{ rabbitmq_extra_vhosts }}"

- name: Adding access vhost groups
  community.general.ipa_group:
    name: "{{ ldap_rabbitmq_env }}-rabbit-access-{{ item['name'] }}"
    state: present
    append: true
    user: "{{ item['username'] }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
  with_items: "{{ rabbitmq_extra_vhosts }}"
