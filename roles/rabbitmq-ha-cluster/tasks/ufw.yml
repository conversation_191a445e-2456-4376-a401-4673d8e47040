---
- name: UFW tuning for OS
  community.general.ufw:
    port: "{{ rabbitmq_ufw_port_os }}"
    proto: tcp
    rule: allow
    state: enabled
  tags: ufw
- name: UFW tuning for vip ip
  community.general.ufw:
    port: "{{ rabbitmq_ufw_port_vip }}"
    to_ip: "{{ rabbitmq_keepalived_vip }}"
    proto: tcp
    rule: allow
    state: enabled
- name: UFW service enabled and restarted
  ansible.builtin.systemd:
    enabled: true
    name: ufw
    state: restarted
