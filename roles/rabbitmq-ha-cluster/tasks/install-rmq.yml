---
- name: Adding RabbitMQ repo public GPG key to the apt repo
  ansible.builtin.apt_key:
    url: "{{ rabbitmq_ubuntu_repo_key }}"
    state: present
  become: true
  register: result
  until: result is successful

- name: Adding RabbitMQ team public GPG key to the apt repo
  ansible.builtin.apt_key:
    keyserver: keys.openpgp.org
    id: "{{ rabbitmq_ubuntu_team_key }}"
    state: present
  become: true
  register: result
  until: result is successful

- name: Adding RabbitMQ repo
  ansible.builtin.apt_repository:
    repo: "{{ rabbitmq_ubuntu_repo }}"
    state: present
  become: true
  register: result
  until: result is successful

- name: Adding RabbitMQ relang repo public GPG key to the apt repo
  ansible.builtin.apt_key:
    url: "{{ rabbitmq_ubuntu_erlang_repo_key }}"
    state: present
  become: true
  register: result
  until: result is successful

- name: Add Rabbitmq erlang repo
  ansible.builtin.apt_repository:
    repo: "{{ rabbitmq_ubuntu_erlang_repo }}"
    state: present
  become: true
  when: rabbitmq_ubuntu_erlang_from_rabbit

- name: Fix erlang version
  ansible.builtin.copy:
    content: |
      Package: erlang* esl-erlang
      Pin: version 1:{{ rabbitmq_ubuntu_erlang_version }}*
      Pin-Priority: 501
    dest: /etc/apt/preferences.d/erlang
    mode: 0644
  become: true
  when: rabbitmq_ubuntu_erlang_from_rabbit

- name: Installing RabbitMQ server
  ansible.builtin.apt:
    name:
      - >-
        rabbitmq-server{{
        (rabbitmq_ubuntu_version_defined and rabbitmq_ubuntu_version is defined)
        | ternary(['=', rabbitmq_ubuntu_version] | join(''), '')
        }}
    state: present
    update_cache: true
  become: true
  register: result
  until: result is successful

- name: Ensuring that the RabbitMQ service is running
  ansible.builtin.systemd:
    name: rabbitmq-server
    state: started
    enabled: true
  become: true
