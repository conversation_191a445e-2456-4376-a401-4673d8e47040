---
- name: Configuring RabbitMQ
  ansible.builtin.template:
    src: "{{ rabbitmq_config_file }}.j2"
    dest: "/etc/rabbitmq/{{ rabbitmq_config_file }}"
    backup: true
    mode: 0644
    owner: rabbitmq
    group: rabbitmq
  notify: "Restart rabbitmq-server"

- name: <PERSON><PERSON> ldap config
  ansible.builtin.template:
    src: "{{ rabbitmq_config_advanced_file }}.j2"
    dest: /etc/rabbitmq/{{ rabbitmq_config_advanced_file }}
    backup: true
    owner: rabbitmq
    group: rabbitmq
    mode: 0644
  notify: "Restart rabbitmq-server"

- name: RabbitMQ setup plugins
  ansible.builtin.copy:
    content: "[{% for plugin in rabbitmq_config.plugins %}
               {{ plugin }}{{ ',' if not loop.last else '' }}
               {% endfor %}]."
    dest: /etc/rabbitmq/enabled_plugins
    backup: true
    owner: rabbitmq
    group: rabbitmq
    mode: 0644
  notify: "Restart rabbitmq-server"

- name: Cluster cookie
  ansible.builtin.copy:
    content: "{{ rabbitmq_cookie }}"
    dest: /var/lib/rabbitmq/.erlang.cookie
    owner: rabbitmq
    group: rabbitmq
    mode: 0600

- name: Cluster nodes in /etc/hosts
  ansible.builtin.lineinfile:
    dest: /etc/hosts
    line: >-
      {{ item.ip }} {{ item.hostname }} {{ item.hostnamefull }}
    state: present
  loop:
    - ip: "{{ hostvars[groups[cluster_name][0]]['ansible_host'] }}"
      hostname: "{{ hostvars[groups[cluster_name][0]]['inventory_hostname'] }}"
      hostnamefull: "{{ hostvars[groups[cluster_name][0]]['inventory_hostname'] }}.{{ ipa_domain }}"
    - ip: "{{ hostvars[groups[cluster_name][1]]['ansible_host'] }}"
      hostname: "{{ hostvars[groups[cluster_name][1]]['inventory_hostname'] }}"
      hostnamefull: "{{ hostvars[groups[cluster_name][1]]['inventory_hostname'] }}.{{ ipa_domain }}"
    - ip: "{{ hostvars[groups[cluster_name][2]]['ansible_host'] }}"
      hostname: "{{ hostvars[groups[cluster_name][2]]['inventory_hostname'] }}"
      hostnamefull: "{{ hostvars[groups[cluster_name][2]]['inventory_hostname'] }}.{{ ipa_domain }}"

- name: Restart RabbitMQ
  ansible.builtin.systemd:
    name: rabbitmq-server
    state: restarted
