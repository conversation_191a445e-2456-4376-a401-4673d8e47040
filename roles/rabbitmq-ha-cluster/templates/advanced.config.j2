[
  {rabbitmq_auth_backend_ldap, [
    {servers, ["{{ ipa_server_hostname }}.sl.local", "{{ ipa_replica_hostname }}.sl.local"]},
    {port,    389},
    {user_dn_pattern,       "uid=${username},cn=users,cn=accounts,dc=sl,dc=local"},
    {vhost_access_query,
          {'or', [
                  {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-all,cn=groups,cn=accounts,dc=sl,dc=local"},
                  {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"},
                  {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-c-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"},
                  {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-r-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"},
                  {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-w-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"},
                  {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-r,cn=groups,cn=accounts,dc=sl,dc=local"}
        ]}},
    {resource_access_query,
        {for, [
          {permission, configure, {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-c-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"}},
          {permission, write, {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-w-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"}},
          {permission, read, {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-access-r-${vhost},cn=groups,cn=accounts,dc=sl,dc=local"}}
          ]
        }
},
    {tag_queries, [
        {administrator,     {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-admins,cn=groups,cn=accounts,dc=sl,dc=local"}},
        {monitoring,        {in_group, "cn={{ ldap_rabbitmq_env }}-rabbit-monitoring,cn=groups,cn=accounts,dc=sl,dc=local"}},
        {management,        {constant, true}}
        ]},
        {log, false}
  ]}
].
