vrrp_script chk_haproxy {
  script "{{ rabbitmq_keepalived_etc_dir }}/haproxy_check.sh"
  interval 2
}

vrrp_instance VI_1 {
  interface ens18

  state BACKUP

  virtual_router_id 202
  
  priority {% if '-01' in inventory_hostname %}250{% elif '-02' in inventory_hostname %}200{% else %}100{% endif %}
  
  unicast_src_ip {{ hostvars[inventory_hostname]['ansible_host'] }}
  unicast_peer {
{% for i in groups[cluster_name] %}
{% if inventory_hostname != i %}
    {{ hostvars[i]['ansible_host'] }}
{% endif %}
{% endfor %}
  }

  authentication {
    auth_type PASS
    auth_pass {{ rabbitmq_keepalived_auth_pass }}
  }

  virtual_ipaddress {
    {{ rabbitmq_keepalived_vip }}{% if rabbitmq_keepalived_vip_mask is defined %}/{{ rabbitmq_keepalived_vip_mask }}{% endif %} {% if rabbitmq_keepalived_vip_brd is defined %}brd {{ rabbitmq_keepalived_vip_brd }} dev {% if rabbitmq_external_interface is defined %}{{ rabbitmq_external_interface }}{% else %}{{ hostvars[inventory_hostname]['ansible_default_ipv4']['interface'] }}{% endif %}{% endif %}

  }

  {% if rabbitmq_keepalived_virtual_routes is defined %}
  virtual_routes {
    0.0.0.0/0 via {{ rabbitmq_keepalived_vip_gateway }} dev {{ rabbitmq_external_interface }}
  }
  {% endif %}

  track_script {
    chk_haproxy
  }
}
