global
  maxconn 10000

defaults
  log global
  mode tcp
  timeout connect 3600s
  timeout client 3600s
  timeout server 3600s
  retries 2

listen rabbitmq
  bind {{ rabbitmq_keepalived_vip }}:5672
  balance roundrobin
  option tcpka
  option tcp-check
  server {{ hostvars[groups[cluster_name][0]]['inventory_hostname'] }}.sl.local {{ hostvars[groups[cluster_name][0]]['ansible_host'] }}:{{ rabbitmq_listen_tcp_port }} check inter 5s rise 2 fall 3
  server {{ hostvars[groups[cluster_name][1]]['inventory_hostname'] }}.sl.local {{ hostvars[groups[cluster_name][1]]['ansible_host'] }}:{{ rabbitmq_listen_tcp_port }} check inter 5s rise 2 fall 3
  server {{ hostvars[groups[cluster_name][2]]['inventory_hostname'] }}.sl.local {{ hostvars[groups[cluster_name][2]]['ansible_host'] }}:{{ rabbitmq_listen_tcp_port }} check inter 5s rise 2 fall 3
