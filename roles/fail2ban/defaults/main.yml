---
# defaults file for roles/fail2ban
fail2ban_loglevel: INFO
fail2ban_logtarget: /var/log/fail2ban.log

fail2ban_ignoreself: "true"
fail2ban_ignoreips:
  - "127.0.0.1/8"

# In seconds
fail2ban_bantime: 600
fail2ban_findtime: 600

fail2ban_maxretry: 5
# fail2ban_destemail: root@localhost
# fail2ban_sender: root@{{ ansible_fqdn }}

fail2ban_configuration: []
#  - option: loglevel
#    value: "INFO"
#    section: Definition

fail2ban_jail_configuration: []
#  - option: ignoreself
#    value: "true"
#    section: DEFAULT

# Path to directory containing filters to copy in filter.d
# fail2ban_filterd_path:

fail2ban_packages:
  - fail2ban

fail2ban_service: fail2ban

fail2ban_base_configuration:
  - option: loglevel
    value: "{{ fail2ban_loglevel }}"
    section: Definition
  - option: logtarget
    value: "{{ fail2ban_logtarget }}"
    section: Definition
  - option: ignoreip
    value: "{{ fail2ban_ignoreips | join(' ') }}"
    section: DEFAULT

fail2ban_base_jail_configuration:
  - option: ignoreself
    value: "{{ fail2ban_ignoreself }}"
    section: DEFAULT
  - option: bantime
    value: "{{ fail2ban_bantime }}"
    section: DEFAULT
  - option: findtime
    value: "{{ fail2ban_findtime }}"
    section: DEFAULT
  - option: maxretry
    value: "{{ fail2ban_maxretry }}"
    section: DEFAULT
#  - option: destemail
#    value: "{{ fail2ban_destemail }}"
#    section: DEFAULT
#  - option: sender
#    value: "{{ fail2ban_sender }}"
#    section: DEFAULT
