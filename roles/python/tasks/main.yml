---
# tasks file for roles/python
- name: Add Python repository
  ansible.builtin.apt_repository:
    repo: ppa:deadsnakes/ppa
    state: present
  tags: python

- name: Install python packages
  ansible.builtin.apt:
    name: "{{ item }}"
    update_cache: true
    allow_downgrade: true
  with_items: "{{ python_packages }}"
  tags: python

- name: Update requests on Ubuntu 24.04
  ansible.builtin.pip:
    name:
      - requests
    extra_args: "--upgrade --break-system-packages"
  tags: python
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'

- name: Update requests
  ansible.builtin.pip:
    name:
      - requests
    extra_args: "--upgrade"
  tags: python
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'

- name: Remove OpenSSL python module
  ansible.builtin.file:
    path: /usr/lib/python3/dist-packages/OpenSSL
    state: absent
  tags: python

- name: Install python libs on Ubuntu 24.04
  ansible.builtin.pip:
    name: "{{ item }}"
    extra_args: "--break-system-packages"
  with_items: "{{ python_libs }}"
  tags: python
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'

- name: Install python libs
  ansible.builtin.pip:
    name: "{{ item }}"
  with_items: "{{ python_libs }}"
  tags: python
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'
