---
- name: Install client "Ubuntu"
  ansible.builtin.include_tasks: install-client-ubuntu.yml
  when: ansible_distribution == "Ubuntu"
- name: Install client "Debian"
  ansible.builtin.include_tasks: install-client-debian.yml
  when: ansible_distribution == "Debian"
- name: Install client "CentOS"
  ansible.builtin.include_tasks: install-client-rhel.yml
  when: ansible_distribution == "Centos" or ansible_distribution == "RedHat"
