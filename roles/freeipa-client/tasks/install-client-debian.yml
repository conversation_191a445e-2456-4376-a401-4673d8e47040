---
- name: Install the ipa-client package
  ansible.builtin.apt:
    name: freeipa-client
    state: present
  become: true

- name: Join IPA Domain
  ansible.builtin.command: ipa-client-install --mkhomedir --ssh-trust-dns --force-join \
    --enable-dns-updates --no-ntp --domain={{ ipa_domain }} \
    --server={{ ipa_server_hostname }}.sl.local --realm={{ ipa_realm }} \
    --principal=admin --password={{ admin_password }} -U
  register: command_result
  changed_when: false
  failed_when:
    - "'IPA client is already configured on this system' not in command_result.stderr"
    - "'Client configuration complete' not in command_result.stderr"
    - "'The ipa-client-install command was successful' not in command_result.stderr"
