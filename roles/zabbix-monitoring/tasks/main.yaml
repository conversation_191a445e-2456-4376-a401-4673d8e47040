---
- name: Create discovery rules
  become: false
  community.zabbix.zabbix_discovery_rule:
    name: "roles discovery {{ item }}"
    state: present
    iprange: "{{ network_default_gateway }}/{{ network_size }}"
    dchecks:
      - type: <PERSON><PERSON>bi<PERSON>
        key: "agent.hostname"
        ports: 10050
        uniq: true
        host_source: "discovery"
      - type: <PERSON><PERSON><PERSON><PERSON>
        key: "docker.info"
        ports: 10050
      - type: Zabbix
        key: "system.hw.chassis"
        ports: 10050
      - type: Zabbix
        key: "system.uname"
        ports: 10050
      - type: Zabbix
        key: "vfs.file.contents[/sys/module/zfs/version]"
        ports: 10050
    proxy: "{{ item }}"
    status: "enabled"
  vars:
    ansible_network_os: community.zabbix.zabbix
    ansible_connection: httpapi
    ansible_httpapi_port: 443
    ansible_httpapi_use_ssl: true
    ansible_httpapi_validate_certs: false
    ansible_zabbix_url_path: ''
    ansible_user: "{{ zabbix_user_ansible }}"
    ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
  delegate_to: "{{ zabbix_server_web }}"
  run_once: true
  loop:
    - hz-infra-zabbix-proxy-02.sl.local
    - hz-infra-zabbix-proxy-03.sl.local
