---
- name: Run vault_ldap_sync container
  community.docker.docker_container:
    image: "{{ vaultwarden_ldap_image }}"
    name: vault_ldap_sync
    state: started
    pull: true
    restart: true
    restart_policy: always
    env:
      VAULTWARDEN_LDAP_VAULTWARDEN_URL: "{{ vault_vaultwarden_url }}"
      VAULTWARDEN_LDAP_VAULTWARDEN_ADMIN_TOKEN: "{{ vaultwarden_token }}"
      VAULTWARDEN_LDAP_HOST: "{{ vault_ldap_host }}"
      VAULTWARDEN_LDAP_SSL: "True"
      VAULTWARDEN_LDAP_BIND_DN: "{{ vault_ldap_bind_dn }}"
      VAULTWARDEN_LDAP_SEARCH_BASE_DN: "{{ vault_ldap_search_base_dn }}"
      VAULTWARDEN_LDAP_BIND_PASSWORD: "{{ vault_vault_ldap_bind_password }}"
      VAULTWARDEN_LDAP_SEARCH_FILTER: "{{ vault_ldap_search_filter }}"
      VAULTWARDEN_LDAP_SYNC_INTERVAL_SECONDS: "{{ vault_ldap_sync_interval_seconds }}"
      VAULTWARDEN_LDAP_SCHEME: "{{ vault_ldap_scheme }}"
      VAULTWARDEN_LDAP_PORT: "{{ vault_ldap_port }}"
      VAULTWARDEN_LDAP_SSL_VERIFY: "False"
