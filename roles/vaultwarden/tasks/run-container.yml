---
- name: Run vaultwarden container
  community.docker.docker_container:
    image: "{{ vaultwarden_vault_image }}"
    name: vaultwarden
    state: started
    pull: true
    restart: true
    restart_policy: always
    ports:
      - "8443:80"
    volumes:
      - /app/vaultwarden:/data
    env:
      ADMIN_TOKEN: "{{ vaultwarden_token }}"
      DATABASE_URL: postgresql://{{ postgres_user }}:{{ postgres_pass }}@{{ postgres_host }}:{{ postgres_port }}/{{ postgres_db }}
      SHOW_PASSWORD_HINT: "false"
      SMTP_HOST: "{{ smtp_host }}"
      SMTP_FROM: "{{ smtp_from }}"
      SMTP_PORT: "{{ smtp_port }}"
      SMTP_SECURITY: "{{ smtp_security }}"
      SMTP_USERNAME: "{{ smtp_username }}"
      SMTP_PASSWORD: "{{ smtp_password }}"
      DOMAIN: "{{ vaultwarden_domain_url }}"
      INVITATIONS_ALLOWED: "true"
      SIGNUPS_ALLOWED: "false"
