Job {
  Name = "{{ item.name }}-{{ inventory_hostname }}"
  Type = Backup
  Client = "{{ item.client }}"
  Schedule = "{{ item.schedule }}"
  Storage = "File"
  FileSet = "mysql"
  Messages = Standard
  Pool = Full

  Priority = 10
  Level = Full

  RunScript {
    FailJobOnError = Yes
    RunsOnClient = Yes
    RunsWhen = Before
    Command = "sh -c 'find /backup/dump -maxdepth 1 -type f -mtime +30 -delete; /bin/mysqldump  --single-transaction --skip-lock-tables --flush-logs --master-data=2 --triggers --routines --events {{ item.dbname }} | zstd -o /backup/dump/dump-{{ item.dbname }}-%d-`date +%x | tr / -`.sql.zstd'"
  }
}
