Job {
  Name = "{{ item.name }}-{{ inventory_hostname }}"
  Type = Backup
  Client = "{{ inventory_hostname }}"
  Schedule = "{{ item.schedule }}"
  Storage = "File"
  FileSet = "postgres-pgbackrest"
  Messages = Standard
  Pool = Full

  Priority = 10
  Level = Full

  RunScript {
    FailJobOnError = Yes
    RunsOnClient = Yes
    RunsWhen = Before
    Command = "su postgres -c '/bin/pgbackrest --stanza={{ item.cluster_stanza }}{% if item.backup_full is defined %} --type=full{% endif %} --log-level-file=info backup'"
  }
{% if bareos_backup_nfs_mnt_old_dir is defined %}
  RunScript {
    FailJobOnError = Yes
    RunsOnClient = Yes
    RunsWhen = After
    Command = "su postgres -c '/bin/pgbackrest --config /etc/pgbackrest-old.conf --stanza={{ item.cluster_stanza }} --log-level-file=info expire'"
  }
{% endif %}

}
