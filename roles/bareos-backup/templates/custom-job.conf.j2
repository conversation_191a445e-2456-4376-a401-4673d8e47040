Job {
  Name = "{{ item.name }}-{{ inventory_hostname }}"
  Type = Backup
  Client = "{{ item.client }}"
  Schedule = "{{ item.schedule }}"
  Storage = "File"
  FileSet = "{{ item.fileset }}"
  Messages = Standard
  Pool = {{ item.pool }}

  Priority = 10
  Level = Full

  RunScript {
    FailJobOnError = Yes
    RunsOnClient = Yes
    RunsWhen = Before
    Command = "{{ item.runafter }}"
  }

{% if item.runbefore is defined %}
  RunScript {
    RunsOnSuccess = Yes
    RunsOnClient = Yes
    RunsWhen = After
    Command = "{{ item.runbefore }}"
  }
{% endif %}

}
