---
- name: NFS utility Debian or Ubuntu
  ansible.builtin.apt:
    name:
      - nfs-common
    state: present
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"
  tags: NFS

- name: Mount a temporary NFS volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}"
    path: /backuptmp
    opts: "vers={{ nfs_mount_version }},rw,sync,soft"
    state: mounted
    fstype: nfs
  become: true
  tags: NFS

- name: Create a directory in NFS backup if it does not exist
  ansible.builtin.file:
    path: "/backuptmp/{{ inventory_hostname }}"
    state: directory
    mode: 0755
  tags: NFS

- name: Unmmount an temporary NFS volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}"
    path: /backuptmp
    opts: "vers={{ nfs_mount_version }},rw,sync,soft"
    state: absent
    fstype: nfs
  become: true
  tags: NFS

- name: Mount an NFS backup volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}/{{ inventory_hostname }}"
    path: "{{ bareos_backup_destinition }}"
    opts: "vers={{ nfs_mount_version }},rw,sync,soft"
    state: mounted
    fstype: nfs
  become: true
  tags: NFS

- name: Mount an NFS old backup volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source_old }}/{{ inventory_hostname }}"
    path: "{{ bareos_backup_destinition_old }}"
    opts: "vers={{ nfs_mount_version }},rw,sync,soft"
    state: mounted
    fstype: nfs
  become: true
  when: bareos_backup_nfs_mnt_old_dir is defined
  tags: NFS

- name: Create a dump directory
  ansible.builtin.file:
    path: "/backup/dump"
    state: directory
    mode: 0755
  tags: NFS
