---
- name: Set schedule parameters
  ansible.builtin.set_fact:
    schedule_time: "{{ bareos_schedule_mappings[item.schedule].time }}"
    schedule_days: "{{ bareos_schedule_mappings[item.schedule].days }}"
    description: "Bareos backup {{ item.name }}"
  loop: >
    {{
      vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
    }}
  loop_control:
    loop_var: item

- name: Get existing tasks from TaskShed
  ansible.builtin.uri:
    url: "{{ taskshed_url_tasks }}"
    method: GET
    return_content: true
  register: taskshed_tasks

- name: Gather list of pgcluster hosts
  ansible.builtin.set_fact:
    pgcluster_hosts: "{{ groups[item.cluster_stanza] | select('match', '.*-db-0[1-3]$') | list }}"
  when: item.cluster_stanza is defined and item.cluster_stanza.endswith('_pgcluster')
  loop: >
    {{
      vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
    }}
  loop_control:
    loop_var: item

- name: Ensure backup tasks in TaskShed
  when: vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | list | length > 0
  block:
    - name: Check if task exists in TaskShed and get its ID
      ansible.builtin.set_fact:
        task_info: "{{ taskshed_tasks.json | selectattr('name', 'equalto', item.name ~ '-' ~ inventory_hostname) | list | first | default({}) }}"
      loop: >
        {{
          vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
        }}
      loop_control:
        loop_var: item

    - name: Set task_exists flag
      ansible.builtin.set_fact:
        task_exists: "{{ task_info | list | length > 0 }}"
      loop: >
        {{
          vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
        }}
      loop_control:
        loop_var: item

    - name: Set task_id if task exists
      ansible.builtin.set_fact:
        task_id: "{{ task_info.id }}"
      when: task_exists
      loop: >
        {{
          vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
        }}
      loop_control:
        loop_var: item

    - name: Create task in TaskShed
      ansible.builtin.uri:
        url: "{{ taskshed_url_tasks }}"
        method: POST
        body_format: json
        body:
          name: "{{ item.name }}-{{ inventory_hostname }}"
          service: "Bareos"
          time: "{{ bareos_schedule_mappings[item.schedule].time }}"
          days_of_week: "{{ bareos_schedule_mappings[item.schedule].days }}"
          is_recurring: true
          description: "Bareos backup {{ item.name }}"
          hosts: "{{ inventory_hostname }}"
        status_code: 200
      when: not task_exists and
            (
              (item.cluster_stanza is defined and item.cluster_stanza.endswith('_pgcluster')
              and inventory_hostname.endswith('-03') and pgcluster_hosts | length == 3) or
              (item.cluster_stanza is not defined or not item.cluster_stanza.endswith('_pgcluster'))
            )
      loop: >
        {{
          vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
        }}
      loop_control:
        loop_var: item

    - name: Update task in TaskShed
      ansible.builtin.uri:
        url: "{{ taskshed_url_tasks }}/{{ task_id }}"
        method: PUT
        body_format: json
        body:
          name: "{{ item.name }}-{{ inventory_hostname }}"
          service: "Bareos"
          time: "{{ bareos_schedule_mappings[item.schedule].time }}"
          days_of_week: "{{ bareos_schedule_mappings[item.schedule].days }}"
          is_recurring: true
          description: "Bareos backup {{ item.name }}"
          hosts: "{{ inventory_hostname }}"
        status_code: 200
      when: task_exists and
            (
              (item.cluster_stanza is defined and item.cluster_stanza.endswith('_pgcluster')
              and inventory_hostname.endswith('-03') and pgcluster_hosts | length == 3) or
              (item.cluster_stanza is not defined or not item.cluster_stanza.endswith('_pgcluster'))
            )
      loop: >
        {{
          vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_') | map(attribute='value') | list | flatten
        }}
      loop_control:
        loop_var: item

- name: Added backup jobs for full mysqldump
  ansible.builtin.template:
    dest: '{{ bareos_backup_job_dir }}/{{ item.name }}-{{ inventory_hostname }}.conf'
    src: mysql.conf.j2
    mode: "0644"
    owner: '{{ bareos_file_owner }}'
    group: '{{ bareos_file_group }}'
  loop: "{{ hostvars[inventory_hostname]['bareos_backup_task_full_mysql'] }}"
  become: true
  when: bareos_backup_task_full_mysql is defined
  delegate_to: "{{ groups.bareos[0] }}"
  tags:
    - backup
    - backup-mysql

- name: Added backup jobs for pgbackrest
  ansible.builtin.template:
    dest: '{{ bareos_backup_job_dir }}/{{ item.name }}-{{ inventory_hostname }}.conf'
    src: pgbackrest.conf.j2
    mode: "0644"
    owner: '{{ bareos_file_owner }}'
    group: '{{ bareos_file_group }}'
  loop: >
    {{
      vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_postgres') | map(attribute='value') | list | flatten
    }}
  become: true
  when: bareos_backup_task_postgres is defined and inventory_hostname.endswith('-03')
  delegate_to: "{{ groups.bareos[0] }}"
  tags:
    - backup
    - backup-pgbackrest

- name: Added backup jobs for clickhouse
  ansible.builtin.template:
    dest: '{{ bareos_backup_job_dir }}/{{ item.name }}-{{ inventory_hostname }}.conf'
    src: clickhouse.conf.j2
    mode: "0644"
    owner: '{{ bareos_file_owner }}'
    group: '{{ bareos_file_group }}'
  loop: >
    {{
      vars | dict2items | selectattr('key', 'match', '^bareos_backup_task_clickhouse') | map(attribute='value') | list | flatten
    }}
  become: true
  when: bareos_backup_task_clickhouse is defined
  delegate_to: "{{ groups.bareos[0] }}"
  tags: backup

- name: Added custom backup jobs
  ansible.builtin.template:
    backup: true
    dest: '{{ bareos_backup_job_dir }}/{{ item.name }}-{{ inventory_hostname }}.conf'
    src: custom-job.conf.j2
    mode: "0644"
    owner: '{{ bareos_file_owner }}'
    group: '{{ bareos_file_group }}'
  loop: "{{ hostvars[inventory_hostname]['bareos_backup_task_custom_backup_job'] }}"
  become: true
  when: bareos_backup_task_custom_backup_job is defined
  delegate_to: "{{ groups.bareos[0] }}"
  tags:
    - backup
    - backup-custom

- name: Added custom backup fileset
  ansible.builtin.template:
    dest: '{{ bareos_backup_job_fileset }}/{{ item.name }}.conf'
    src: custom-fileset.conf.j2
    mode: "0644"
    owner: '{{ bareos_file_owner }}'
    group: '{{ bareos_file_group }}'
  loop: >
    {{
      vars | dict2items | selectattr('key', 'match', '^bareos_custom_backup_fileset') | map(attribute='value') | list | flatten
    }}
  become: true
  when: bareos_custom_backup_fileset is defined
  delegate_to: "{{ groups.bareos[0] }}"
  tags: backup

- name: Added custom backup pool
  ansible.builtin.template:
    dest: '{{ bareos_backup_job_pool }}/{{ item.name }}.conf'
    src: custom-pool.conf.j2
    mode: "0644"
    owner: '{{ bareos_file_owner }}'
    group: '{{ bareos_file_group }}'
  loop: >
    {{
      vars | dict2items | selectattr('key', 'match', '^bareos_custom_backup_pool') | map(attribute='value') | list | flatten
    }}
  become: true
  when: bareos_custom_backup_pool is defined
  delegate_to: "{{ groups.bareos[0] }}"
  tags:
    - backup
    - backup-pool

- name: Stop a director container
  community.docker.docker_container:
    name: '{{ bareos_dir_docker_name }}'
    state: stopped
  delegate_to: "{{ groups.bareos[0] }}"

- name: Start a director container
  community.docker.docker_container:
    name: '{{ bareos_dir_docker_name }}'
    state: started
  delegate_to: "{{ groups.bareos[0] }}"
