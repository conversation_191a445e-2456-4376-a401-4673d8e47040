Bareos Backup jobs
=========

Backup jobs examples.
------------

 bareos_backup_mysql:
  - name: Mysql-{{ inventory_hostname }}
  - client: "{{ inventory_hostname }}"
  - schedule: Nightly_every_Day
  - dbname: sellerlogic_com
     
 bareos_backup_task_postgres:
  - name: PG-Daily-{{ inventory_hostname }}
   - client: "{{ inventory_hostname }}"
   - schedule: Nightly_Mon_Fri
   - cluster_stanza: prod_infra_pgcluster
  - name: PG-Weekly-{{ inventory_hostname }}
   - client: "{{ inventory_hostname }}"
   - schedule: Nightly_once_on_Week
   - cluster_stanza: prod_infra_pgcluster
   - backup_full: true

Schedules
--------------

  - Nightly_every_Day    | daily at 23:00
  - Nightly_once_on_Week | sun at 23:00
  - Nightly_Mon_Fri      | mon-fri at 23:00


Custom backup jobs examples.
------------

bareos_backup_task_custom_backup_job:
  - name: Nextcloud
   - client: "{{ inventory_hostname }}"
   - schedule: Nightly_every_Day
   - fileset: nextcloud
   - pool: Nextcloud
   - runafter: "sh -c 'sudo -u www-data php /var/www/html/next/occ maintenance:mode --on ; rm /backup/dump/dump.sql.zstd 2>/dev/null ; /bin/mysqldump  --single-transaction --skip-lock-tables --triggers --routines --events --all-databases | zstd -o /backup/dump/dump.sql.zstd ; sudo -u www-data php /var/www/html/next/occ maintenance:mode --off'"
   - runbefore: "rm /backup/dump/dump.sql.zstd"

Custom backup fileset examples.
------------

bareos_custom_backup_fileset:
  - name: nextcloud
   - compression: lz4
   - signature: MD5
   - file: /backup/dump

Custom backup pool examples.
------------

bareos_custom_backup_pool:
  - name: Nextcloud
   - pool_type: Backup
   - label_format: Nextcloud
   - storage: NAS-2
   - volume_retention: "40 days"
   - maximum_volumes: "50"
