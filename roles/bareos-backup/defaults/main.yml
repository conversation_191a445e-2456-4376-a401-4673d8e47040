---
# defaults file for roles/bareos
bareos_backup_nfs_ip: ************
bareos_backup_nfs_mnt_dir: /mnt/datapool/backup
bareos_backup_nfs_source: "{{ bareos_backup_nfs_ip }}:{{ bareos_backup_nfs_mnt_dir }}"
bareos_backup_destinition: "/backup"
bareos_dir_docker_name: "bareos_bareos-dir_1"
bareos_backup_job_dir: /data/bareos/config/director/bareos-dir.d/job
bareos_backup_job_fileset: /data/bareos/config/director/bareos-dir.d/fileset
bareos_backup_job_pool: /data/bareos/config/director/bareos-dir.d/pool
bareos_file_owner: systemd-network
bareos_file_group: root


## Examples
# bareos_backup_mysql:
#   - name: Mysql-{{ inventory_hostname }}
#     client: "{{ inventory_hostname }}"
#     schedule: Nightly_every_Day
#     dbname: sellerlogic_com

# bareos_backup_task_postgres:
#   - name: PG-Daily-{{ inventory_hostname }}
#     client: "{{ inventory_hostname }}"
#     schedule: Nightly_Mon_Fri
#     cluster_stanza: prod_infra_pgcluster
#   - name: PG-Weekly-{{ inventory_hostname }}
#     client: "{{ inventory_hostname }}"
#     schedule: Nightly_once_on_Week
#     cluster_stanza: prod_infra_pgcluster
#     backup_full: true
