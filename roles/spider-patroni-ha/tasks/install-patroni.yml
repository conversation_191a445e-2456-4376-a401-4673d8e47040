---
- name: Install Patroni with deps consul
  ansible.builtin.command: "python3.8 -m pip install psycopg2 patroni[consul]"
  changed_when: false

- name: Install patroni packages
  ansible.builtin.pip:
    name: "{{ item }}"
  loop: "{{ pip_packages }}"

- name: Create directory for DATA postgresql
  ansible.builtin.file:
    path: "{{ path_data_psql }}"
    state: directory
    owner: postgres
    group: postgres
    mode: 0700

- name: Create directory for LOGGING postgresql
  ansible.builtin.file:
    path: "{{ path_log_dir }}"
    state: directory
    owner: postgres
    group: postgres
    mode: 0700

- name: Touch a file for LOGGING
  ansible.builtin.file:
    path: "{{ path_log_dir }}/{{ name_log_file }}"
    state: touch
    owner: postgres
    group: postgres
    mode: 0600

- name: Create dir for patroni YAML file
  ansible.builtin.file:
    path: "{{ patroni_yml_path }}"
    state: directory
    owner: postgres
    group: postgres
    mode: 0755

- name: Create template YAML file for create name_cluster
  ansible.builtin.template:
    src: postgres.yml.j2
    dest: "{{ patroni_yml_path }}/postgres.yml"
    mode: 0644

- name: Copy service patroni
  ansible.builtin.template:
    src: patroni.service.j2
    dest: "/etc/systemd/system/patroni.service"
    mode: 0644

- name: Starting service patroni with create HA cluster PostgreSQL
  ansible.builtin.service:
    name: patroni
    state: started
    enabled: true
