---
- name: Create consul-template directory structure
  ansible.builtin.file:
    state: directory
    path: "{{ item }}"
    owner: "consul"
    group: "consul"
    mode: 0755
  with_items:
    - "{{ consul_tpl_dirs }}"

- name: Check archive stat
  ansible.builtin.stat:
    path: "{{ consul_tpl_area }}/{{ consul_tpl_archive_file }}"
  register: consul_tpl_archive_stat

- name: Download consul-template binary
  ansible.builtin.get_url:
    url: "{{ consul_tpl_download_url }}"
    dest: "{{ consul_tpl_area }}"
    mode: 0755
  when: consul_tpl_archive_stat.stat.exists == "False"

- name: Unzip the downloaded package
  ansible.builtin.unarchive:
    src: "{{ consul_tpl_area }}/{{ consul_tpl_archive_file }}"
    dest: "/usr/local/bin/"
    owner: "root"
    group: "root"
    copy: false

- name: Copy consul-template systemd service configuration
  ansible.builtin.template:
    src: "{{ consul_tpl_name_service }}.service.j2"
    dest: "/etc/systemd/system/{{ consul_tpl_name_service }}.service"
    mode: 0755

- name: Consul-template config file
  ansible.builtin.template:
    src: "{{ consul_tpl_name_service }}.cfg.j2"
    dest: "{{ consul_tpl_home }}/config/{{ consul_tpl_name_service }}.cfg"
    mode: 0755

- name: Consul-template config file
  ansible.builtin.template:
    src: haproxy.ctmpl.j2
    dest: "{{ consul_tpl_home }}/templates/haproxy.ctmpl"
    mode: 0755

- name: Start consul sevice
  ansible.builtin.systemd:
    name: "{{ consul_tpl_name_service }}"
    state: started
    enabled: true

- name: Restart haproxy after generate cfg
  ansible.builtin.systemd:
    name: haproxy
    state: restarted
