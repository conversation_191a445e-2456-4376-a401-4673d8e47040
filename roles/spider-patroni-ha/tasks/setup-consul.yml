---
- name: Consul | Ensure Consul group
  ansible.builtin.group:
    name: "consul"

- name: Consul | Ensure Consul user
  ansible.builtin.user:
    name: "consul"
    group: "consul"
    createhome: false

- name: Check archive stat
  ansible.builtin.stat:
    path: "{{ consul_area }}/{{ consul_archive_file }}"
  register: consul_archive_stat

- name: Download consul binary
  ansible.builtin.get_url:
    url: "{{ consul_download_url }}"
    dest: "{{ consul_area }}"
    mode: 0750
  when: consul_archive_stat.stat.exists == "False"

- name: Unzip the downloaded package
  ansible.builtin.unarchive:
    src: "{{ consul_area }}/{{ consul_archive_file }}"
    dest: "/usr/local/bin/"
    owner: "root"
    group: "root"
    copy: false

- name: Create a directory if it does not exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: 0750
  with_items:
    - "{{ consul_require_dirs }}"

- name: Update consul permissions
  ansible.builtin.file:
    path: "{{ consul_data_dir }}"
    state: directory
    owner: consul
    group: consul
    mode: 0750

- name: "Generate uniq key for consul config"
  ansible.builtin.command: "/usr/local/bin/consul keygen"
  register: consul_uniq_key
  delegate_to: '{{ groups.patroni_consul_spider_db_prod[0] }}'
  run_once: true
  changed_when: false

- name: "Add uniq key for consul to dummy host"
  ansible.builtin.add_host:
    name: "CONSUL_DUMMY_HOLDER"
    consul_uniq_key: "{{ consul_uniq_key.stdout }}"
  run_once: true

- name: Copy config for consul
  ansible.builtin.template:
    src: config.json.j2
    dest: "{{ consul_common_dir }}/config.json"
    mode: 0644

- name: Create service for consul
  ansible.builtin.template:
    src: consul.service.j2
    dest: "/etc/systemd/system/consul.service"
    mode: 0644

- name: Enable and start consul
  ansible.builtin.systemd:
    name: consul
    state: started
    enabled: true
