---
- name: Update apt cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600
- name: Install system packages (on Debian)
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
  loop: "{{ system_packages }}"

- name: Install system packages
  block:
    - name: Add repository apt-key
      ansible.builtin.apt_key:
        url: "{{ item.key }}"
        state: present
      loop: "{{ apt_repository_keys }}"
      when: apt_repository_keys | length > 0

    - name: Add repository
      ansible.builtin.apt_repository:
        repo: "{{ item.repo }}"
        state: present
        update_cache: true
      loop: "{{ apt_repository }}"
      when: apt_repository | length > 0

- name: Check if ufw package is installed.
  ansible.builtin.command: systemctl status ufw
  register: ufw_installed
  failed_when: false
  changed_when: false
  check_mode: false

- name: Disable the ufw firewall if configured
  ansible.builtin.systemd:
    name: ufw
    state: stopped
    enabled: false
  when:
    - ufw_installed.rc == 0
- name: Cluster nodes in /etc/hosts
  ansible.builtin.template:
    src: hosts.j2
    dest: /etc/hosts
    mode: 0655
