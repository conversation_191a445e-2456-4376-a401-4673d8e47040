---
- name: Preparation node to deploy patroni
  ansible.builtin.import_tasks: node-preparation.yml
  when: "'patroni_spider_db_prod' in group_names"
- name: Setup consul node
  ansible.builtin.import_tasks: setup-consul.yml
  when: "'patroni_consul_spider_db_prod' in group_names"
- name: Setup postgresql node
  ansible.builtin.import_tasks: install-postgresql.yml
  when: "'patroni_spider_db_prod' in group_names"
- name: Setup patroni node
  ansible.builtin.import_tasks: install-patroni.yml
  when: "'patroni_spider_db_prod' in group_names"
- name: Setup haproxy
  ansible.builtin.import_tasks: install-haproxy.yml
  when: "'patroni_haproxy_spider_db_prod' in group_names"
- name: Setup keepalived
  ansible.builtin.import_tasks: install-keepalived.yml
  when: "'patroni_keepalived_spider_db_prod' in group_names"
- name: Setup haproxy templates
  ansible.builtin.import_tasks: install-consul-template.yml
  when: "'patroni_haproxy_spider_db_prod' in group_names"
