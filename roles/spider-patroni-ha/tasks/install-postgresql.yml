---
- name: Install PostgreSQL
  block:
    - name: Ensure postgresql database-cluster manager package
      ansible.builtin.package:
        name: postgresql-common
        state: present

    - name: Disable initializing of a default postgresql cluster
      ansible.builtin.replace:
        path: /etc/postgresql-common/createcluster.conf
        replace: create_main_cluster = false
        regexp: ^#?create_main_cluster.*$

    - name: Disable log rotation with logrotate for postgresql
      ansible.builtin.file:
        dest: /etc/logrotate.d/postgresql-common
        state: absent

- name: Install PostgreSQL packages
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
  loop: "{{ postgresql_packages }}"
