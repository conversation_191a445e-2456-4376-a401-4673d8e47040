{
    "bootstrap_expect": 3,
    "bind_addr": "{{ ansible_default_ipv4.address }}",
    "client_addr": "0.0.0.0",
    "datacenter": "Us-Central",
    "data_dir": "{{ consul_data_dir }}",
    "domain": "consul",
    "enable_script_checks": true,
    "dns_config": {
        "enable_truncate": true,
        "only_passing": true
    },
    "enable_syslog": true,
    "encrypt": "{{ hostvars['CONSUL_DUMMY_HOLDER']['consul_uniq_key'] }}",
    "leave_on_terminate": true,
    "log_level": "INFO",
    "rejoin_after_leave": true,
    "server": true,
    "start_join": [
        {% for i in groups['patroni_consul_spider_db_prod'] %}
        "{{ hostvars[i]['ansible_default_ipv4']['address'] }}"
        {% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    "ui": true
}
