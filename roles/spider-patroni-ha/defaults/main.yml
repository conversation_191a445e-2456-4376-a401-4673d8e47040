---
# Global vars
patroni_name_cluster: spider
patroni_path_bin: "/usr/local/bin"
patroni_yml_path: "/etc/patroni"
path_data_psql: "/data/patroni"
path_log_dir: "/var/log/postgresql"
name_log_file: "postgresql.log"

# Setting yaml file
# General recommendation to set the shared_buffers is as follows.
#
#    Below 2GB memory, set the value of shared_buffers to 20% of total system memory.
#
#    Below 32GB memory, set the value of shared_buffers to 25% of total system memory.
#
#    Above 32GB memory, set the value of shared_buffers to 8GB

patroni_sb: 750MB

pip_packages:
   - patroni
   - psycopg2
   - kazoo

python_ver: 38
required_pack:
   - "python{{ python_ver }}"
   - "python{{ python_ver }}-devel"
   - gcc
   - unzip
consul_name_service: consul
consul_version: "1.11.2"
consul_release: "consul_{{ consul_version }}_linux_amd64"
consul_archive_file: "{{ consul_release }}.zip"
consul_area: "/tmp"
consul_download_url: "https://releases.hashicorp.com/{{ consul_name_service }}/{{ consul_version }}/{{ consul_archive_file }}"

consul_data_dir: "/var/consul"
consul_common_dir: "/etc/consul.d"
consul_require_dirs:
   - "{{ consul_common_dir }}/scripts"
   - "{{ consul_data_dir }}"
consul_tpl_name_service: consul-template
consul_tpl_version: "0.27.2"
consul_tpl_release: "consul-template_{{ consul_tpl_version }}_linux_amd64"
consul_tpl_archive_file: "{{ consul_tpl_release }}.zip"
consul_tpl_area: "/tmp"
consul_tpl_download_url: "https://releases.hashicorp.com/{{ consul_tpl_name_service }}/{{ consul_tpl_version }}/{{ consul_tpl_archive_file }}"

consul_tpl_home: "/opt/{{ consul_tpl_name_service }}"
consul_log_file: "/var/log/{{ consul_tpl_name_service }}"
consul_tpl_dirs:
   - "{{ consul_tpl_home }}/templates"
   - "{{ consul_tpl_home }}/config"
   - "{{ consul_log_file }}"
postgres_ver: 14
postgres_bin: "/usr/pgsql-{{ postgres_ver }}/bin"


postgres_pack:
   # - "postgresql{{ postgres_ver }}"
   - "postgresql{{ postgres_ver }}-server"
   - "postgresql{{ postgres_ver }}-contrib"
   - "postgresql{{ postgres_ver }}-devel"
#   - python-psycopg2
