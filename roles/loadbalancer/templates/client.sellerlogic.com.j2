server {
        server_name client.sellerlogic.com www.client.sellerlogic.com;
        access_log /var/log/nginx/client.sellerlogic.com.access.log;
        return 301 https://app.sellerlogic.com$request_uri;
        listen 116.202.136.54:80;
}
server {
        server_name client.sellerlogic.com www.client.sellerlogic.com;
        ssl_certificate "/etc/nginx/ssl/sellerlogic.com.crtca";
        ssl_certificate_key "/etc/nginx/ssl/sellerlogic.com.key";
        ssl_dhparam /etc/nginx/ssl/dhparam.pem;
        access_log /var/log/nginx/https-client.sellerlogic.com.access.log;
        listen 116.202.136.54:443 ssl;
        return 301 https://app.sellerlogic.com$request_uri;
}