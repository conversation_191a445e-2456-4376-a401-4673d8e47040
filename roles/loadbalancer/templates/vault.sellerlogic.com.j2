server {
        server_name vault.sellerlogic.com www.vault.sellerlogic.com;
        access_log /var/log/nginx/client.sellerlogic.com.access.log;
        return 301 https://vault.sellerlogic.com$request_uri;
        listen *************:80;
}
server {
        server_name vault.sellerlogic.com www.vault.sellerlogic.com;
        ssl_certificate "/etc/nginx/ssl/sellerlogic.com.crtca";
        ssl_certificate_key "/etc/nginx/ssl/sellerlogic.com.key";
        ssl_dhparam /etc/nginx/ssl/dhparam.pem;
        access_log /var/log/nginx/https-vault.sellerlogic.com.access.log;
        location / {
                proxy_pass http://************:80;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }
        listen *************:443 ssl;
        
}