---
- name: Copy template to nginx
  ansible.builtin.template:
    dest: '{{ item.dest }}'
    src: '{{ item.src }}'
    mode: 0644
  loop:
    - {src: 'templates/client.sellerlogic.com.j2', dest: '/etc/nginx/sites-available/client.sellerlogic.com.conf'}
    - {src: 'templates/vault.sellerlogic.com.j2', dest: '/etc/nginx/sites-available/vault.sellerlogic.com.conf'}
- name: Linked to enable
  ansible.builtin.file:
    src: '{{ item.src }}'
    dest: '{{ item.dest }}'
    state: link
  loop:
    - {src: '/etc/nginx/sites-available/client.sellerlogic.com.conf', dest: '/etc/nginx/sites-enabled/client.sellerlogic.com.conf'}
    - {src: '/etc/nginx/sites-available/vault.sellerlogic.com.conf', dest: '/etc/nginx/sites-enabled/vault.sellerlogic.com.conf'}
- name: Activate site
  ansible.builtin.service:
    name: nginx
    state: reloaded
