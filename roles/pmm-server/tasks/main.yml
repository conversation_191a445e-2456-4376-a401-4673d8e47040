---
- name: Created filesystem
  community.general.filesystem:
    fstype: ext4
    dev: "{{ item }}"
  loop:
    - /dev/vdb
- name: Create folder to data
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - '{{ pmm_data_path }}'
- name: Mount and format disk
  ansible.posix.mount:
    path: "{{ item.path }}"
    src: "{{ item.src }}"
    fstype: ext4
    state: mounted
  loop:
    - {path: '{{ pmm_data_path }}', src: '/dev/vdb'}
- name: Create container
  community.docker.docker_container:
    image: "percona/pmm-server:{{ pmm_version }}"
    name: pmm-server
    state: started
    tty: true
    interactive: true
    restart: true
    restart_policy: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - '{{ pmm_data_path }}:/srv'
