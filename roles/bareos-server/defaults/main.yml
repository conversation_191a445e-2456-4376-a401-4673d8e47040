---
# defaults file for roles/bareos
bareos_version: 23

bareos_image_bareos_director: "harbor.sl.local/infrastructure/system_images/bareos-director:{{ bareos_version }}"
bareos_image_bareos_sd: "harbor.sl.local/infrastructure/system_images/bareos-storage:{{ bareos_version }}"
bareos_image_bareos_fd: "harbor.sl.local/infrastructure/system_images/bareos-client:{{ bareos_version }}"
bareos_image_bareos_webui: "harbor.sl.local/infrastructure/system_images/bareos-webui:{{ bareos_version }}"
bareos_image_bareos_api: "harbor.sl.local/infrastructure/system_images/bareos-api:{{ bareos_version }}"
bareos_image_smtpd: "devture/exim-relay:4.97-r0-0"

# if need reinit db | should be 'true' if bareos db does not exist
bareos_db_init: "true"

bareos_db_update: "false"
bareos_db_host: *************
bareos_db_port: 5432
bareos_db_name: bareos
bareos_db_user: bareos
bareos_db_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66316565353463383030623532313837343934646263303065626163316639626431653762383262
  6531383933396261383931346636643666363335656361300a633566643561636361336364666261
  61353863323765623261616363393066626337323337653161346265653962626532336562323534
  3666356236623532650a333333363561373763646463313165666539613934643964393939316531
  37353732666166353761303330376461616539373639646331333039363930383532
bareos_db_admin_user: bareos
bareos_db_admin_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66316565353463383030623532313837343934646263303065626163316639626431653762383262
  6531383933396261383931346636643666363335656361300a633566643561636361336364666261
  61353863323765623261616363393066626337323337653161346265653962626532336562323534
  3666356236623532650a333333363561373763646463313165666539613934643964393939316531
  37353732666166353761303330376461616539373639646331333039363930383532

bareos_fd_host: bareos-fd
bareos_fd_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66316565353463383030623532313837343934646263303065626163316639626431653762383262
  6531383933396261383931346636643666363335656361300a633566643561636361336364666261
  61353863323765623261616363393066626337323337653161346265653962626532336562323534
  3666356236623532650a333333363561373763646463313165666539613934643964393939316531
  37353732666166353761303330376461616539373639646331333039363930383532

bareos_web_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  35316139393161363035306466316264633937363162323961613335316163333435396665636463
  6436666462333830373335663831383866343264353163320a656365396465643161353563376231
  65343737306331323964633236356461323266393435396161373766633163356236303265613761
  3330656537303736320a663134383063366535313630626561383264386539623737633763653466
  39366364303739306532326536623966613232616463653034336332383837646637

bareos_admin_mail: <EMAIL>

bareos_dir_host: bareos-dir

# smtp
bareos_smtp_host: email-smtp.eu-west-1.amazonaws.com
bareos_smtp_port: 587
bareos_smtp_username: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  62303830366632333838623037383039633232343436373336356538356663343066636131643433
  3163363066636138323830313033386535383039373639610a323332393362653762633035383138
  66623930626262656231646231386364386535333539636337633939336463303635343834383065
  3432363635613464390a373334386231353236343136623063643065656232633935383735313530
  39643436646537303830326161326534363637643938636631343261666535343830
bareos_smtp_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  39616665616334643432656565373032363362353865633631396134343764663039666562363532
  6663323137323930626631366361333238383533643735610a396432623562366533623564306162
  63383266643861326536323166336437366665383764373537316532383834363833313038613836
  6639396235386333650a303032303065623062626462306532666531336131333336613734616536
  66303766353831343935316663616162386362633162623436383933353531353964643833343163
  3034343131666538363162313865626439343364313166646138
bareos_smtp_domain: sellerlogic.com
bareos_smtpd_host: "smtpd.{{ bareos_smtp_domain }}"

# bareos-sd
bareos_ubuntu_repo: "https://download.bareos.org/current/xUbuntu_{{ ansible_distribution_version }}"
bareos_ubuntu_gpg_key: "https://download.bareos.org/current/xUbuntu_{{ ansible_distribution_version }}/Release.key"

bareos_sd_host: *************
bareos_sd_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66316565353463383030623532313837343934646263303065626163316639626431653762383262
  6531383933396261383931346636643666363335656361300a633566643561636361336364666261
  61353863323765623261616363393066626337323337653161346265653962626532336562323534
  3666356236623532650a333333363561373763646463313165666539613934643964393939316531
  37353732666166353761303330376461616539373639646331333039363930383532

bareos_sd_directory: /etc/bareos/bareos-sd.d

bareos_sd_configs:
  - FileStorage.conf
  - NULL.conf
  - AWS.conf

bareos_sd_owner: systemd-network
bareos_sd_group: root

# bareos-dir
bareos_dir_docker_name: "bareos_bareos-dir_1"

bareos_fqdn: bareos-dir.sl.local

bareos_dir_directory: /data/bareos/config/director/bareos-dir.d

bareos_dir_sd_config:
  - File.conf
  - AWS.conf
  - NAS.conf
  - NAS-2.conf

bareos_dir_schedule_config: "{{ bareos_schedule_mappings.keys() }}"

bareos_dir_pool_config:
  - postgres-nas.conf
  - mysql-nas.conf
  - clickhouse-nas.conf
  - postgres-nas-2.conf
  - mysql-nas-2.conf
  - clickhouse-nas-2.conf
  - aws.conf

bareos_dir_messages_config:
  - Standard.conf

bareos_dir_fileset_config:
  - postgres.conf
  - mysql.conf
  - clickhouse.conf

# Global
bareos_concurrent_jobs: 20
bareos_host: "{{ ansible_host }}"
bareos_dir_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66316565353463383030623532313837343934646263303065626163316639626431653762383262
  6531383933396261383931346636643666363335656361300a633566643561636361336364666261
  61353863323765623261616363393066626337323337653161346265653962626532336562323534
  3666356236623532650a333333363561373763646463313165666539613934643964393939316531
  37353732666166353761303330376461616539373639646331333039363930383532

bareos_file_owner: systemd-network
bareos_file_group: root

## S3
bareos_s3_storage_name: bareos
bareos_s3_host: nas-01.sl.local:9000
bareos_access_key: bareos
bareos_secret_key: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  39306436303733313234336436336430356238353065663030306662363236336564623036633461
  6135303135663633316331353663316266303836323734360a623162376438653036633337356438
  36393634306532353930623863356334636335313264663562636534356339333730633532376136
  6538353236376237350a633032343766613765353936633939633137633964346536656330393263
  31373863383836393165633864646466633064623065323762643737643131363666
bareos_aws_auth_sign_version: 4
bareos_aws_region: eu-central-1

## SD package
bareos_package:
  - bareos-storage
  - bareos-storage-droplet

# prometheus
bareos_exporter_port: 9625
bareos_exporter_db_type: postgres
bareos_exporter_db_host: "{{ bareos_db_host }}"
bareos_exporter_db_port: 5432
bareos_exporter_db_user: "{{ bareos_db_user }}"
bareos_exporter_db_pass: "{{ bareos_db_password }}"
bareos_exporter_db_name: "{{ bareos_db_name }}"
bareos_exporter_ssl_mode: disable
bareos_exporter_endpoint: /metrics
bareos_exporter_job_days: 7
bareos_exporter_wait_for_db: 0
bareos_exporter_dir: /opt/bareos_exporter
bareos_exporter_docker_image: vierbergenlars/bareos_exporter:v0.6.0
bareos_exporter_group: root
bareos_exporter_owner: root
