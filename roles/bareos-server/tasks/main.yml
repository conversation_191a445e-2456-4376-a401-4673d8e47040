---
# tasks file for roles/bareos
- name: Create containers
  ansible.builtin.import_tasks: create-container.yml
  tags: containers

- name: Install bareos-sd
  ansible.builtin.include_tasks: bareos-sd.yml
  tags: bareos-sd

- name: Configure director
  ansible.builtin.include_tasks: configure-dir.yml
  tags:
    - configure-dir
    - dir-storage-res

- name: Add prometheus exporter
  ansible.builtin.import_tasks: prometheus-exporter.yml
  tags: exporter
