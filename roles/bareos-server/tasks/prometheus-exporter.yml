---
- name: Create exporter directory
  become: true
  ansible.builtin.file:
    path: "{{ bareos_exporter_dir }}"
    state: directory
    mode: "755"
    owner: "{{ bareos_exporter_owner }}"
    group: "{{ bareos_exporter_group }}"

- name: Copy env
  ansible.builtin.template:
    src: "exporter/env.j2"
    dest: "{{ bareos_exporter_dir }}/.env"
    mode: "0644"
    owner: "{{ bareos_exporter_owner }}"
    group: "{{ bareos_exporter_group }}"

- name: Create exporter container
  community.docker.docker_container:
    name: bareos_exporter
    image: "{{ bareos_exporter_docker_image }}"
    state: started
    restart: true
    restart_policy: always
    env_file: "{{ bareos_exporter_dir }}/.env"
    ports:
      - 9625:9625
