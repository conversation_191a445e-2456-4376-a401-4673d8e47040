---
# tasks file for roles/bareos
- name: Create container
  community.docker.docker_compose:
    project_name: bareos
    state: present
    definition:
      version: "3"
      services:
        bareos-dir:
          image: "{{ bareos_image_bareos_director }}"
          restart: always
          hostname: bareos-dir.sellerlogic.com
          volumes:
            - /data/bareos/config/director:/etc/bareos
            - /data/bareos/data/director:/var/lib/bareos
          environment:
            - DB_INIT={{ bareos_db_init }}
            - DB_UPDATE={{ bareos_db_update }}
            - DB_HOST={{ bareos_db_host }}
            - DB_PORT={{ bareos_db_port }}
            - DB_NAME={{ bareos_db_name }}
            - DB_USER={{ bareos_db_user }}
            - DB_PASSWORD={{ bareos_db_password }}
            - DB_ADMIN_USER={{ bareos_db_admin_user }}
            - DB_ADMIN_PASSWORD={{ bareos_db_admin_password }}
            - BAREOS_SD_HOST={{ bareos_sd_host }}
            - <PERSON>RE<PERSON>_SD_PASSWORD={{ bareos_sd_password }}
            - BAREOS_FD_HOST={{ bareos_fd_host }}
            - BAREOS_FD_PASSWORD={{ bareos_fd_password }}
            - BAREOS_WEBUI_PASSWORD={{ bareos_web_password }}
            - SMTP_HOST={{ ansible_ssh_host }}:8025
            - ADMIN_MAIL={{ bareos_admin_mail }}
            # - WEBHOOK_NOTIFICATION={{ bareos_webhook_notification }}
            # - WEBHOOK_TYPE={{ bareos_webhook_type }}
            # - WEBHOOK_URL={{ bareos_webhook_url }}
            # - WEBHOOK_CHAT_ID={{ bareos_webhook_chat_id }} only for telegram
          links:
            # - bareos-sd
            - bareos-fd
        # bareos-sd:
        #   image: "{{ bareos_image_bareos_sd }}"
        #   restart: always
        #   ports:
        #     - 9103:9103
        #   volumes:
        #     - /data/bareos/config/storage:/etc/bareos
        #     - /data/bareos/data/storage:/var/lib/bareos/archive
        #   environment:
        #     - BAREOS_SD_PASSWORD={{ bareos_sd_password }}
        bareos-fd:
          image: "{{ bareos_image_bareos_fd }}"
          restart: always
          volumes:
            - /data/bareos/config/client:/etc/bareos
            - /data/bareos/data/director:/var/lib/bareos-director
          environment:
            - BAREOS_FD_PASSWORD={{ bareos_fd_password }}
            - FORCE_ROOT=false
        bareos-webui:
          image: "{{ bareos_image_bareos_webui }}"
          hostname: bareos-webui.sl.local
          restart: always
          ports:
            - 8080:80
          environment:
            - BAREOS_DIR_HOST={{ bareos_dir_host }}
            - SERVER_STATS=yes
          volumes:
            - /data/bareos/config/webui:/etc/bareos-webui
          links:
            - bareos-dir
        bareos-api:
          image: "{{ bareos_image_bareos_api }}"
          restart: always
          ports:
            - 8000:8000
          environment:
            - BAREOS_DIR_HOST={{ bareos_dir_host }}
          links:
            - bareos-dir
        smtpd:
          image: "{{ bareos_image_smtpd }}"
          user: 100:101
          restart: always
          hostname: "{{ bareos_smtpd_host }}"
          ports:
            - 8025:8025
          environment:
            - HOSTNAME="{{ bareos_smtpd_host }}"
            - SMARTHOST={{ bareos_smtp_host }}::{{ bareos_smtp_port }}
            - SMTP_USERNAME={{ bareos_smtp_username }}
            - SMTP_PASSWORD={{ bareos_smtp_password }}
            - SMTP_USERDOMAIN={{ bareos_smtp_domain }}
