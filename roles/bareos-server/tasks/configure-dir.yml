---
- name: Configure director
  tags:
    - configure-dir
  block:
    - name: Copy sd config to dir
      ansible.builtin.template:
        src: "dir/storage/{{ item }}.j2"
        dest: "{{ bareos_dir_directory }}/storage/{{ item }}"
        mode: "0644"
        owner: "{{ bareos_file_owner }}"
        group: "{{ bareos_file_group }}"
      with_items: "{{ bareos_dir_sd_config }}"
      tags:
        - dir-storage-res

    - name: Copy schedule config to dir
      ansible.builtin.template:
        src: "dir/schedule/schedule.j2"
        dest: "{{ bareos_dir_directory }}/schedule/{{ item }}.conf"
        mode: "0644"
        owner: "{{ bareos_file_owner }}"
        group: "{{ bareos_file_group }}"
      with_items: "{{ bareos_dir_schedule_config }}"
      tags: bareos-schedule

    - name: Copy pool config to dir
      ansible.builtin.template:
        src: "dir/pool/{{ item }}.j2"
        dest: "{{ bareos_dir_directory }}/pool/{{ item }}"
        mode: "0644"
        owner: "{{ bareos_file_owner }}"
        group: "{{ bareos_file_group }}"
      with_items: "{{ bareos_dir_pool_config }}"

    - name: Copy messages config to dir
      ansible.builtin.template:
        src: "messages/{{ item }}.j2"
        dest: "{{ bareos_dir_directory }}/messages/{{ item }}"
        mode: "0644"
        owner: "{{ bareos_file_owner }}"
        group: "{{ bareos_file_group }}"
      with_items: "{{ bareos_dir_messages_config }}"

    - name: Copy fileset config to dir
      ansible.builtin.template:
        src: "dir/fileset/{{ item }}.j2"
        dest: "{{ bareos_dir_directory }}/fileset/{{ item }}"
        mode: "0644"
        owner: "{{ bareos_file_owner }}"
        group: "{{ bareos_file_group }}"
      with_items: "{{ bareos_dir_fileset_config }}"

    - name: Stop a director container
      community.docker.docker_container:
        name: "{{ bareos_dir_docker_name }}"
        state: stopped

    - name: Start a director container
      community.docker.docker_container:
        name: "{{ bareos_dir_docker_name }}"
        state: started
