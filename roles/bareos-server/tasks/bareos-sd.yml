---
- name: Add an Apt signing key for Ubuntu
  ansible.builtin.apt_key:
    url: "{{ bareos_ubuntu_gpg_key }}"
    state: present
  when: ansible_distribution == "Ubuntu"

- name: Add bareos repository into sources list using specified filename on Ubuntu
  ansible.builtin.apt_repository:
    repo: "deb {{ bareos_ubuntu_repo }} /"
    state: present
    filename: bareos
  when: ansible_distribution == "Ubuntu"

- name: Install bareos on Debian or Ubuntu
  ansible.builtin.apt:
    name: "{{ item }}"
    update_cache: true
  with_items: "{{ bareos_package }}"
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"

- name: Start bareos-sd
  ansible.builtin.systemd:
    name: bareos-sd
    state: started
    daemon_reload: true
    enabled: true

- name: Copy device config to sd
  ansible.builtin.template:
    src: "sd/device/{{ item }}.j2"
    dest: "{{ bareos_sd_directory }}/device/{{ item }}"
    mode: "0644"
    owner: "{{ bareos_sd_owner }}"
    group: "{{ bareos_sd_group }}"
  with_items: "{{ bareos_sd_configs }}"
  tags:
    - sd-device

- name: Create droplet directory
  become: true
  ansible.builtin.file:
    path: "{{ bareos_sd_directory }}/device/droplet"
    state: directory
    mode: "755"
    owner: "{{ bareos_sd_owner }}"
    group: "{{ bareos_sd_group }}"

- name: Copy droplet config to sd
  ansible.builtin.template:
    src: "sd/device/droplet/aws.profile.j2"
    dest: "{{ bareos_sd_directory }}/device/droplet/aws.profile"
    mode: "0644"
    owner: "{{ bareos_sd_owner }}"
    group: "{{ bareos_sd_group }}"

- name: Copy bareos-dir config to sd
  ansible.builtin.template:
    src: sd/director/bareos-dir.conf.j2
    dest: "{{ bareos_sd_directory }}/director/bareos-dir.conf"
    mode: "0644"
    owner: "{{ bareos_sd_owner }}"
    group: "{{ bareos_sd_group }}"

- name: Restart bareos-sd
  ansible.builtin.systemd:
    name: bareos-sd
    state: restarted

- name: Stop a director container
  community.docker.docker_container:
    name: "{{ bareos_dir_docker_name }}"
    state: stopped
  delegate_to: "{{ groups.bareos[0] }}"

- name: Start a director container
  community.docker.docker_container:
    name: "{{ bareos_dir_docker_name }}"
    state: started
  delegate_to: "{{ groups.bareos[0] }}"
