Device {
  Name = AWS
  Media Type = AWS
  Archive Device = S3 Object Storage

  Device Options = "profile=/etc/bareos/bareos-sd.d/device/droplet/aws.profile,bucket={{ bareos_s3_storage_name }},chunksize=100M"
  Device Type = droplet
  Label Media = yes                    # lets Bareos label unlabeled media
  Random Access = yes
  Automatic Mount = yes                # when device opened, read it
  Removable Media = no
  Always Open = no
  Description = "S3 device"
  Maximum Concurrent Jobs = 1
}
