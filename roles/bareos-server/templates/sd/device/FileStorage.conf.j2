Device {
  Name = FileStorage
  Media Type = File
  Archive Device = /var/lib/bareos/archive
  LabelMedia = yes;                   # lets Bareos label unlabeled media
  Random Access = yes;
  AutomaticMount = yes;               # when device opened, read it
  RemovableMedia = no;
  AlwaysOpen = no;
  Maximum Concurrent Jobs = 20;
  Description = "File device. A connecting Director must have the same Name and MediaType."
}
