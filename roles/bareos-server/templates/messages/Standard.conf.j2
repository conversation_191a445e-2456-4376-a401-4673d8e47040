Messages {
  Name = Standard
  Description = "Reasonable message delivery -- send most everything to email address and to the console."
  operatorcommand = "/usr/bin/bsmtp -h {{ bareos_host }}:8025 -f \"\(Bareos\) \<%r\>\" -s \"Bareos: Intervention needed for %j\" %r"
  mailcommand = "/usr/bin/bsmtp -h {{ bareos_host }}:8025 -f \"\(Bar<PERSON>s\) \<%r\>\" -s \"Bareos: %t %e of %c %l\" %r"
  operator = <EMAIL> = mount
  Mail On Error = <EMAIL> = all, !skipped, !saved, !audit
  console = all, !skipped, !saved, !audit
  append = "/var/log/bareos/bareos.log" = all, !skipped, !saved, !audit
  catalog = all, !skipped, !saved, !audit
}
