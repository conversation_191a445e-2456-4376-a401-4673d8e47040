---
- name: NFS utility
  ansible.builtin.apt:
    name:
      - nfs-common
    state: present
  tags: NFS

- name: Mount a temporary NFS volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}"
    path: /backuptmp
    opts: vers={{ nfs_mount_version }},rw,sync,soft,intr
    state: mounted
    fstype: nfs
  become: true
  tags: NFS

- name: Create a directory in NFS backup if it does not exist
  ansible.builtin.file:
    path: "/backuptmp/{{ inventory_hostname }}"
    state: directory
    mode: '0755'
  tags: NFS

- name: Unmmount an temporary NFS volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}"
    path: /backuptmp
    opts: vers={{ nfs_mount_version }},rw,sync,soft,intr
    state: absent
    fstype: nfs
  become: true
  tags: NFS

- name: Mount an NFS volume
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}/{{ inventory_hostname }}"
    path: "{{ backup_destinition }}"
    opts: vers={{ nfs_mount_version }},rw,sync,soft,intr
    state: mounted
    fstype: nfs
  become: true
  tags: NFS
