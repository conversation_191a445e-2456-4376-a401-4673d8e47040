---
- name: Added crontab jobs for backup tasks
  ansible.builtin.cron:
    name: "{{ item.name }}"
    state: present
    disabled: false
    cron_file: "{{ item.cron_file }}"
    backup: true
    minute: "{{ item.minute }}"
    hour: "{{ item.hour }}"
    day: "{{ item.day }}"
    month: "{{ item.month }}"
    weekday: "{{ item.weekday }}"
    user: "{{ item.user }}"
    job: "{{ item.job }}"
  loop: "{{ crone_backup }}"
  become: true
  when: crone_backup is defined
  tags: crone
