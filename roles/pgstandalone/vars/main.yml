---
# Repository
pgstandalone_apt_repository_keys:
  - key: "https://www.postgresql.org/media/keys/ACCC4CF8.asc"  # postgresql repository apt key
pgstandalone_apt_repository:
  - repo: "deb http://apt.postgresql.org/pub/repos/apt/ {{ ansible_distribution_release }}-pgdg main"

# Packages (for apt repo)
pgstandalone_system_packages:
  - python3
  - python3-dev
  - python3-psycopg2
  - python3-setuptools
  - python3-pip
  - gcc
  - unzip

pgstandalone_postgresql_packages:
  - postgresql-{{ pgstandalone_postgresql_version }}
  - postgresql-server-dev-{{ pgstandalone_postgresql_version }}
  - postgresql-contrib-{{ pgstandalone_postgresql_version }}

pgstandalone_postgresql_bin_dir: "/usr/lib/postgresql/{{ pgstandalone_postgresql_version }}/bin"

pgstandalone_pgcluster_port: 5432
pgstandalone_pgcluster_prometheus_exporter_host: localhost
pgstandalone_pgcluster_user: postgres

pgstandalone_pgcluster_zbx_monitor_con_limit: 200
