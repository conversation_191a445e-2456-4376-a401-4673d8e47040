version: 5
postgres:
  dsn: postgres://{{postgres_sync_ldap_user}}:{{postgres_sync_ldap_pass}}@{{pgstandalone_postgres_sync_ldap_host}}:{{pgstandalone_postgres_sync_ldap_port_vip}}/postgres
  managed_roles_query: |
    VALUES
      ('public'),
      ('ldap_roles')

    UNION

    SELECT DISTINCT role.rolname
    FROM pg_roles AS role
    JOIN pg_auth_members AS ms ON ms.member = role.oid
    JOIN pg_roles AS parent
      ON parent.rolname = 'ldap_roles' AND parent.oid = ms.roleid
    ORDER BY 1;

  roles_query: |
    SELECT
        role.rolname, array_agg(members.rolname) AS members,
        {options},
        pg_catalog.shobj_description(role.oid, 'pg_authid') as comment
    FROM
        pg_catalog.pg_roles AS role
    LEFT JOIN pg_catalog.pg_auth_members ON roleid = role.oid
    LEFT JOIN pg_catalog.pg_roles AS members ON members.oid = member
    GROUP BY role.rolname, {options}, comment
    ORDER BY 1;

  owners_query: |
    SELECT DISTINCT role.rolname
    FROM pg_catalog.pg_roles AS role
    JOIN pg_catalog.pg_auth_members AS ms ON ms.member = role.oid
    JOIN pg_catalog.pg_roles AS owners
      ON owners.rolname = 'owners' AND owners.oid = ms.roleid
    ORDER BY 1;

  schemas_query: |
    SELECT nspname FROM pg_catalog.pg_namespace
    WHERE nspname NOT LIKE 'pg_%' AND nspname <> 'information_schema'
    ORDER BY 1;

  databases_query: "SELECT datname FROM pg_catalog.pg_database;"

  roles_blacklist_query:
    - postgres
    - "pg_*"
    - "rds_*"
    - zbx_monitor
    - replicator
    - admin
    - {{postgres_sync_ldap_user}}
privileges:
  # Define `ro` privilege group with read-only grants
  ro:
  - __connect__
  - __select_on_tables__
  - __select_on_sequences__
  - __usage_on_schemas__
  - __usage_on_types__

  # `rw` privilege group lists write-only grants
  rw:
  - __temporary__
  - __all_on_tables__
  - __all_on_sequences__

  # `ddl` privilege group lists DDL only grants.
  ddl:
  - __create_on_schemas__

ldap:
  uri: ldaps://hz-prod-ldap-01.sl.local:636
  binddn: {{ldap_bind_dn}}
  password: {{ldap_bind_password}}

sync_map:
- description: "Setup static roles and grants."
  roles:
  - names:
    - ldap_roles
    - readers
    options: NOLOGIN
  - name: writers
    # Grant reading to writers
    parent: readers
    options: NOLOGIN
  - name: owners
    # Grant read/write to owners
    parent: writers
    options: NOLOGIN

  grant:
  - privilege: ro
    role: readers
    schemas: __all__
  - privilege: rw
    role: writers
    schema: __all__
  - privilege: ddl
    role: owners
    schema: __all__

- description: "Query LDAP to create owner."
  ldapsearch:
    base: cn=groups,cn=accounts,dc=sl,dc=local
    filter: "(cn=postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_owner*)"
  role:
    name: '{member.uid}'
    options: LOGIN SUPERUSER
    parent:
    - ldap_roles
    - owners
    comment: "From LDAP group {dn}"

- description: "Query LDAP to create writers."
  ldapsearch:
    base: cn=groups,cn=accounts,dc=sl,dc=local
    filter: "(cn=postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_write*)"
    on_unexpected_dn: warn
  role:
    name: '{member.uid}'
    options: LOGIN
    parent:
    - ldap_roles
    - writers
    comment: 'From LDAP groupe {dn}'

- description: "Query LDAP to create readers."
  ldapsearch:
    base: cn=groups,cn=accounts,dc=sl,dc=local
    filter: "
      (&
        (cn=postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_read*)
        (objectClass=*)
      )"

  role:
    name: '{member.uid}'
    options: LOGIN
    parent:
    - ldap_roles
    - readers
