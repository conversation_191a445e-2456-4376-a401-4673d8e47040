# File modified by ipa-client-install

# We do not want to break your existing configuration, hence:
#   URI, BASE, TLS_CACERT and SASL_MECH
#   have been added if they were not set.
#   In case any of them were set, a comment has been inserted and
#   "# CONF_NAME modified by IPA" added to the line above.
# To use IPA server with openLDAP tools, please comment out your
# existing configuration for these options and uncomment the
# corresponding lines generated by IPA.


#
# LDAP Defaults
#

# See ldap.conf(5) for details
# This file should be world readable but not world writable.

#BASE   dc=example,dc=com
#URI    ldap://ldap.example.com ldap://ldap-master.example.com:666

#SIZELIMIT      12
#TIMELIMIT      15
#DEREF          never

# TLS certificates (needed for GnuTLS)
# TLS_CACERT modified by IPA
#TLS_CACERT /etc/ipa/ca.crt
TLS_CACERT      /etc/ssl/certs/ca-certificates.crt

URI ldaps://hz-prod-ldap-01.sl.local
BASE dc=sl,dc=local
#SASL_MECH GSSAPI
