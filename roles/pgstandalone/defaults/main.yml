---
# Prometheus exporter vars
pgstandalone_ignore_err: false
pgstandalone_postgres_prom_exporter_version: v0.11.1
pgstandalone_ppostgres_prom_exporter_cpu_period: '100000'
pgstandalone_postgres_prom_exporter_cpu_quota: '50000'
pgstandalone_postgres_prom_exporter_memory: '512m'

# Global vars
pgstandalone_postgresql_version: "15"
pgstandalone_postgresql_major_version: "15"
pgstandalone_postgresql_socket: "/var/run/postgresql"
pgstandalone_postgres_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgstandalone_postgres_shared_buffer: 750MB
pgstandalone_path_data_psql: "/data"
pgstandalone_postgres_data_directory: /data/postgresql
pgstandalone_postgres_max_wal_size: 10GB
pgstandalone_postgres_min_wal_size: 5GB
pgstandalone_postgres_checkpoint_flush_after: 256kB
pgstandalone_path_log_dir: "/var/log/postgresql"
pgstandalone_name_log_file: "postgresql.log"


# Setting yaml file
# General recommendation to set the shared_buffers is as follows.
#
#    Below 2GB memory, set the value of shared_buffers to 20% of total system memory.
#
#    Below 32GB memory, set the value of shared_buffers to 25% of total system memory.
#
#    Above 32GB memory, set the value of shared_buffers to 8GB


pgstandalone_python_ver: 38
pgstandalone_required_pack:
   - "python{{ pgstandalone_python_ver }}"
   - "python{{ pgstandalone_python_ver }}-devel"
   - gcc
   - unzip

# LDAP
pgstandalone_postgres_sync_ldap_host: "{{ ansible_host }}"
pgstandalone_postgres_sync_ldap_port: "5432"
pgstandalone_postgres_sync_ldap_port_vip: "5432"
pgstandalone_postgres_sync_ldap_db: postgres
pgstandalone_postgres_sync_ldap_soket: "{{ pgstandalone_postgresql_socket }}"

pgstandalone_pgldap_work_dir: "/opt/pgldap/"
pgstandalone_pgldap_conf_file: "{{ pgstandalone_pgldap_work_dir }}ldap2pg.yml"

pgstandalone_crone_sync:
   - name: pg-ldap-sync
     cron_file: pg-ldap-sync
     minute: "*/5"
     hour: "*"
     day: "*"
     month: "*"
     weekday: "*"
     user: root
     job: "echo '{{ ldap_bind_password }}' | kinit {{ ldap_bind_user }} && /usr/local/bin/ldap2pg -N -c {{ pgstandalone_pgldap_conf_file }}"

# pgbouncer
pgstandalone_pgcluster_pgbouncer_admin_user: badmin
pgstandalone_pgcluster_pgbouncer_admin_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37343162616132373766333166353663623935646261386338303565613535316163336633323462
          6134326637633966326431663134383738346233356364640a643234623838633366326237316163
          34373564383963363434663537656137636261313530373932613665613566663136623662633934
          3865626235316463620a326566643631313463323661383461643435623635616235623034373339
          3531
pgstandalone_pgcluster_pgbouncer_application_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgstandalone_pgcluster_pgbouncer_application_password: "{{ postgres_app_password }}"
pgstandalone_pgcluster_pgbouncer_listen_port: 6432
pgstandalone_pgcluster_pgbouncer_max_client_conn: 2000
pgstandalone_pgcluster_pgbouncer_default_pool_size: 2000
pgstandalone_pgcluster_pgbouncer_server_idle_timeout: 10
# When server connection is released back to pool:
#   session      - after client disconnects (default)
#   transaction  - after transaction finishes
#   statement    - after statement finishes
pgstandalone_pgcluster_pgbouncer_pool_mode: transaction
pgstandalone_pgbouncer_systemd_override_path: /usr/lib/systemd/system/pgbouncer.service.d/overrides.conf
pgstandalone_pgbouncer_systemd_limitnofile: 64000
