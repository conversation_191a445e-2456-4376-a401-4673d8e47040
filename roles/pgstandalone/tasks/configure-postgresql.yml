---
- name: Create a directory if it does not exist
  ansible.builtin.file:
    group: "{{ item.group }}"
    mode: "{{ item.mode }}"
    owner: "{{ item.owner }}"
    path: "{{ item.path }}"
    state: "{{ item.state }}"
  loop:
    - group: postgres
      mode: '755'
      owner: postgres
      path: /etc/postgresql/{{ pgstandalone_postgresql_major_version }}/main/
      state: directory
    - group: postgres
      mode: '750'
      owner: postgres
      path: "{{ pgstandalone_postgres_data_directory }}"
      state: directory


- name: Create config from template and copy it to destination node
  ansible.builtin.template:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    mode: "{{ item.mode }}"
    owner: postgres
    group: postgres
    backup: true
  loop:
    - {src: postgresql.conf.j2, dest: "/etc/postgresql/{{ pgstandalone_postgresql_major_version }}/main/postgresql.conf", mode: '644'}
    - {src: pg_hba.conf.j2, dest: "/etc/postgresql/{{ pgstandalone_postgresql_major_version }}/main/pg_hba.conf", mode: '640'}
  notify: Restart postgres

- name: Flush handlers
  ansible.builtin.meta: flush_handlers

- name: Install acl package (need for unprivileg user ansible become)
  ansible.builtin.apt:
    name: acl

- name: Add app postgres user to ldap but don't reset password if already exists
  community.general.ipa_user:
    name: "{{ pgstandalone_postgres_app_user }}"
    state: present
    givenname: "{{ pgstandalone_postgres_app_user }}"
    sn: "{{ pgstandalone_postgres_app_user }}"
    password: "{{ postgres_app_password }}"
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    update_password: always

- name: Add groups to FreeIPA
  community.general.ipa_group:
    description: "{{ item.description }}"
    name: "{{ item.name }}"
    state: present
    append: true
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    user: "{{ item.user | default([]) }}"
  loop:
    - name: postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_owner
      description: Access to {{ postgres_db_env }}_{{ postgres_cluster_name }} like owner
      user:
        - "{{ pgstandalone_postgres_app_user }}"
    - name: postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_read
      description: Access to {{ postgres_db_env }}_{{ postgres_cluster_name }} like reader
    - name: postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_write
      description: Access to {{ postgres_db_env }}_{{ postgres_cluster_name }} like write
