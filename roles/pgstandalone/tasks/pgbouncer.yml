---
- name: Install pgbouncer
  ansible.builtin.package:
    name: pgbouncer
    state: present

- name: Create config from template and copy it destination node
  ansible.builtin.template:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    mode: '640'
    backup: true
  loop:
    - {src: pgbouncer.ini.j2, dest: /etc/pgbouncer/pgbouncer.ini}
    - {src: userlist.txt.j2, dest: /etc/pgbouncer/userlist.txt}
  notify: Enable and restart pgbouncer

- name: Set parameters in service
  community.general.ini_file:
    dest: "{{ pgstandalone_pgbouncer_systemd_override_path }}"
    owner: root
    group: root
    mode: 0644
    section: Service
    option: "{{ item.option }}"
    value: "{{ item.value }}"
  loop:
    - option: LimitNOFILE
      value: "{{ pgstandalone_pgbouncer_systemd_limitnofile }}"
  notify: Enable and restart pgbouncer

- name: Force systemd to reread configs
  ansible.builtin.systemd:
    daemon_reload: true
