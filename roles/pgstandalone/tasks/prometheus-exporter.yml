---
- name: Create a postgres-exporter docker container
  community.general.docker_container:
    name: postgres-exporter
    image: quay.io/prometheuscommunity/postgres-exporter:{{ pgstandalone_postgres_prom_exporter_version }}
    network_mode: host
    restart: true
    restart_policy: always
    cpu_period: "{{ pgstandalone_ppostgres_prom_exporter_cpu_period }}"
    cpu_quota: "{{ pgstandalone_ppostgres_prom_exporter_cpu_quota }}"
    memory: "{{ pgstandalone_ppostgres_prom_exporter_memory }}"
    env:
      DATA_SOURCE_NAME: "postgresql://{{ pgstandalone_pgcluster_user }}:{{ postgres_password }}@\
       {{ pgstandalone_pgcluster_prometheus_exporter_host }}:{{ pgstandalone_pgcluster_port }}/{{ pgstandalone_pgcluster_user }}?sslmode=disable"
