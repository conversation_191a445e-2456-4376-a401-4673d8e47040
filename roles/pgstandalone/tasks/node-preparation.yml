---
- name: Create a ext4 filesystems on devices
  community.general.filesystem:
    fstype: ext4
    dev: "{{ item }}"
  loop:
    - /dev/vdb

- name: Create fstab entries and mount filesystems
  ansible.posix.mount:
    backup: true
    boot: true
    fstype: ext4
    opts: noatime
    path: "{{ item.path }}"
    src: "{{ item.dev }}"
    state: "{{ item.state }}"
  loop:
    - {path: /data, dev: /dev/vdb, state: mounted}

- name: Update apt cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600
- name: Install system packages (on Debian)
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
  loop: "{{ pgstandalone_system_packages }}"

- name: Add repository block
  block:
    - name: Add repository apt-key
      ansible.builtin.apt_key:
        url: "{{ item.key }}"
        state: present
      loop: "{{ pgstandalone_apt_repository_keys }}"
      when: pgstandalone_apt_repository_keys | length > 0

    - name: Add repository
      ansible.builtin.apt_repository:
        repo: "{{ item.repo }}"
        state: present
        update_cache: true
      loop: "{{ pgstandalone_apt_repository }}"
      when: pgstandalone_apt_repository | length > 0

- name: Check if ufw package is installed.
  ansible.builtin.command: systemctl status ufw
  register: ufw_installed
  failed_when: false
  changed_when: false
  check_mode: false

- name: Disable the ufw firewall if configured
  ansible.builtin.systemd:
    name: ufw
    state: stopped
    enabled: false
  when:
    - ufw_installed.rc == 0
