---
- name: Ensure postgresql database-cluster manager package
  ansible.builtin.package:
    name: postgresql-common
    state: present
    purge: true


- name: Disable log rotation with logrotate for postgresql
  ansible.builtin.file:
    dest: /etc/logrotate.d/postgresql-common
    state: absent


- name: Install PostgreSQL packages
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    purge: true
  loop: "{{ pgstandalone_postgresql_packages }}"

- name: Set password for postgres user
  become: true
  become_user: postgres
  community.postgresql.postgresql_user:
    db: postgres
    name: "{{ item.name }}"
    password: "{{ item.password }}"
  loop:
    - name: postgres
      password: "{{ postgres_password }}"
