---
- name: Preparation node to deploy
  ansible.builtin.import_tasks: node-preparation.yml
  tags: node

- name: Setup postgresql node
  ansible.builtin.import_tasks: install-postgresql.yml
  tags: postgre

- name: Configure postgresql node
  ansible.builtin.import_tasks: configure-postgresql.yml
  tags: postgre-configure

- name: Setup pgbouncer
  ansible.builtin.import_tasks: pgbouncer.yml
  tags: pgbouncer
  when: pgstandalone_pgcluster_pgbouncer_application_user is defined

- name: Create extention pg_stat_statements
  ansible.builtin.import_tasks: pg-stat-statements.yml
  tags: pg-stat-statements

- name: Setup postgres-exporter for prometheus
  ansible.builtin.import_tasks: prometheus-exporter.yml
  tags: exporter

- name: Zabbix monitoring
  ansible.builtin.import_tasks: zabbix.yml
  tags: zabbix

- name: Ldap preparation
  ansible.builtin.import_tasks: ldap-preparation.yml
  tags:
    - ldap
    - ldap_preparation

- name: Ldap configure sync
  ansible.builtin.import_tasks: ldap-configuration.yml
  tags:
    - ldap
    - ldap_config

- name: Ldap setup cron
  ansible.builtin.import_tasks: ldap-cron.yml
  tags:
    - ldap
    - ldap_cron

- name: Setup pmm client
  ansible.builtin.import_tasks: pmm-agent.yml
  tags: pmm-agent
