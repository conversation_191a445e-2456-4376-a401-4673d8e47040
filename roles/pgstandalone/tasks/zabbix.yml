---
- name: Add postgres user zbx_monitor
  community.postgresql.postgresql_user:
    conn_limit: "{{ pgstandalone_pgcluster_zbx_monitor_con_limit }}"
    name: "{{ pguser_zbx_monitor }}"
    port: "{{ pgstandalone_pgcluster_port }}"
    password: "{{ pguser_zbx_monitor_password }}"
    login_host: "{{ hostvars[inventory_hostname].ansible_host }}"
    login_password: "{{ postgres_password }}"
    login_user: "{{ pgstandalone_pgcluster_user }}"
  run_once: true

- name: "{{ pguser_zbx_monitor }}"
  community.postgresql.postgresql_membership:
    db: "{{ pgstandalone_pgcluster_user }}"
    login_host: "{{ hostvars[inventory_hostname].ansible_host }}"
    login_password: "{{ postgres_password }}"
    login_user: "{{ pgstandalone_pgcluster_user }}"
    port: "{{ pgstandalone_pgcluster_port }}"
    groups:
      - pg_monitor
    target_roles:
      - "{{ pguser_zbx_monitor }}"
  run_once: true

- name: Grant connect privilege to {{ pguser_zbx_monitor }}
  community.postgresql.postgresql_privs:
    database: "{{ pgstandalone_pgcluster_user }}"
    login_host: "{{ hostvars[inventory_hostname].ansible_host }}"
    login_password: "{{ postgres_password }}"
    login_user: "{{ pgstandalone_pgcluster_user }}"
    obj: "{{ pgstandalone_pgcluster_user }}"
    port: "{{ pgstandalone_pgcluster_port }}"
    privs: CONNECT
    roles: "{{ pguser_zbx_monitor }}"
    type: database
  run_once: true

- name: Link template for postgre host in zabbix
  become: false
  community.zabbix.zabbix_host:
    host_name: "{{ item }}"
    link_templates:
      - PostgreSQL by Zabbix agent 2
      - Linux by Zabbix agent
    macros:
      - macro: '{$PG.URI}'
        value: "tcp://{{ pgstandalone_pgcluster_prometheus_exporter_host }}:{{ pgstandalone_pgcluster_port }}"
  vars:
    ansible_network_os: community.zabbix.zabbix
    ansible_connection: httpapi
    ansible_httpapi_port: 443
    ansible_httpapi_use_ssl: true
    ansible_httpapi_validate_certs: false
    ansible_host: "{{ zabbix_server_web }}"
    ansible_zabbix_url_path: ''
    ansible_user: "{{ zabbix_user_ansible }}"
    ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
  run_once: true
  loop: "{{ ansible_play_hosts_all }}"
  delegate_to: localhost
