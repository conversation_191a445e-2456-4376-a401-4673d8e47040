---

- name: Create LDAP config
  ansible.builtin.template:
    src: ldap.conf.j2
    dest: "/etc/ldap/ldap.conf"
    mode: '0644'
    owner: root
    group: root
    backup: true

- name: Install Dependencies
  ansible.builtin.apt:
    name: ['libldap2-dev', 'libsasl2-dev', 'python3-dev', 'python3-pip', 'python3-ldap', 'python3-pkg-resources', 'python3-psycopg2', 'python3-yaml']
    state: latest
    update_cache: true

- name: Install python requirements
  ansible.builtin.pip:
    name:
      - ldap2pg
      - psycopg2-binary

- name: Create work dir
  ansible.builtin.file:
    path: "{{ pgstandalone_pgldap_work_dir }}"
    mode: '0755'
    recurse: false

- name: Create ldap_sync user
  community.postgresql.postgresql_user:
    db: "{{ pgstandalone_postgres_sync_ldap_db }}"
    login_port: "{{ pgstandalone_postgres_sync_ldap_port }}"
    login_unix_socket: "{{ pgstandalone_postgres_sync_ldap_soket }}"
    name: "{{ postgres_sync_ldap_user }}"
    password: "{{ postgres_sync_ldap_pass }}"
    role_attr_flags: SUPERUSER
  become: true
  become_user: postgres
