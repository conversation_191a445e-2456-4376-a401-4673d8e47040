---
- name: Added crontab jobs for sync tasks
  ansible.builtin.cron:
    name: "{{ item.name }}"
    state: present
    disabled: false
    cron_file: "{{ item.cron_file }}"
    backup: true
    minute: "{{ item.minute }}"
    hour: "{{ item.hour }}"
    day: "{{ item.day }}"
    month: "{{ item.month }}"
    weekday: "{{ item.weekday }}"
    user: "{{ item.user }}"
    job: "{{ item.job }}"
  loop: "{{ pgstandalone_crone_sync }}"
  become: true
  when: pgstandalone_crone_sync is defined
