---
- name: Setup PMM Agent
  ansible.builtin.import_role:
    name: pmm-agent

- name: Enroll like postgres agent
  community.docker.docker_container_exec:
    container: pmm-agent
    command: >
            pmm-admin add postgresql --username={{ pmm_client_postgres_user }} --host={{ ansible_ssh_host }}
            --password={{ pmm_client_postgres_user_password }} --query-source=pgstatements
            --port {{ pgstandalone_pgcluster_port }} {{ inventory_hostname }}

- name: Setup PMM Agent
  ansible.builtin.import_role:
    name: consul-agents
