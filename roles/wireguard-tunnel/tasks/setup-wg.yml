---
- name: Install wireguard
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: true
  loop: "{{ wg_packages }}"
- name: Copy config file
  ansible.builtin.template:
    src: wg0.conf.j2
    dest: /etc/wireguard/wg0.conf
    mode: 0755
- name: Enable tunel
  ansible.builtin.service:
    name: wg-quick@wg0
    enabled: true
- name: Start tunel
  ansible.builtin.service:
    name: wg-quick@wg0
    state: started
