---
- name: "Update the my.cnf"
  ansible.builtin.template:
    src: etc_mysql_my.cnf.j2
    dest: /etc/mysql/my.cnf
    owner: root
    mode: 0644
  register: "config_file"
  notify:
    - Restart percona

- name: "Ensure that percona is running and enabled"
  ansible.builtin.service:
    name: "mysql"
    state: "started"
    enabled: "yes"
  register: mysql_service

- name: Check if Audit logging is enabled
  ansible.builtin.shell: 'mysql -BNe ''show plugins'' | grep ACTIVE | cut -f1 | grep -ci audit_log'
  changed_when: false
  ignore_errors: true
  register: percona_mysql_auditlog_enabled
  when: percona_cluster_mysql_audit_log_enabled

- name: Install Audit logging if MySQL is already running
  community.mysql.mysql_query:
    query: INSTALL PLUGIN audit_log SONAME 'audit_log.so'
    login_unix_socket: "{{ percona_cluster_mysql_unix_socket }}"
  when: percona_cluster_mysql_audit_log_enabled and percona_mysql_auditlog_enabled.stdout|int == 0

# This service restart is needed when changing default mysql_datadir, mysql_native_password
# and other settings. So better restart when the my.cnf file changes
# Restart when my.cnf has changed and it has not been restarted by the above task
- name: "Restart mysql to apply changes done in my.cnf file"
  ansible.builtin.service:
    name: "mysql"
    state: "restarted"
  when:
    - config_file.changed
    - mysql_service is defined
    - not mysql_service.changed
