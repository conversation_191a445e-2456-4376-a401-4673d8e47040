---
- name: Create a ext4 filesystems on devices
  community.general.filesystem:
    fstype: ext4
    dev: "{{ item }}"
  loop:
    - /dev/vdb

- name: Create fstab entries and mount filesystems
  ansible.posix.mount:
    backup: true
    boot: true
    fstype: ext4
    opts: noatime
    path: "{{ item.path }}"
    src: "{{ item.dev }}"
    state: "{{ item.state }}"
  loop:
    - {path: /data, dev: /dev/vdb, state: mounted}
