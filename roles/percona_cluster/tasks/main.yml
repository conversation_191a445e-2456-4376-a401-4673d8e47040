---
- name: Prepare node
  ansible.builtin.import_tasks: prepare-node.yml
  tags: prepare
  when: mysql_additional_disk | d(false)

- name: Check Settings
  ansible.builtin.include_tasks: check-settings.yml
  tags: check

- name: Install Percona
  ansible.builtin.include_tasks: install.yml
  tags: install

- name: Configure systemd
  ansible.builtin.include_tasks: systemd-configure.yml
  tags: systemd

- name: Configure MySQL
  ansible.builtin.include_tasks: configure.yml
  tags: configure

- name: Setup Secure
  ansible.builtin.include_tasks: secure.yml
  tags: secure

- name: Databases
  ansible.builtin.include_tasks: databases.yml
  tags: databases

- name: Users
  ansible.builtin.import_tasks: users.yml
  tags: users

- name: Keepalived
  ansible.builtin.import_tasks: keepalived.yml
  tags: keepalived
  when: percona_cluster_mysql_keepalived_setup | d(false)

- name: Setup Exporter
  ansible.builtin.import_tasks: setup-prometheus-exporter.yml
  tags: exporter

- name: Setup PMM-agent
  ansible.builtin.import_tasks: pmm-agent.yml
  tags: pmm-agent
