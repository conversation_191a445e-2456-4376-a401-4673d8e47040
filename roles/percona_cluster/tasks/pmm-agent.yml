---
- name: Setup PMM Agent
  ansible.builtin.import_role:
    name: pmm-agent

- name: "Make sure the MySQL pmm user are present"
  community.mysql.mysql_user:
    name: "{{ pmm_agent_mysql_user }}"
    password: "{{ pmm_agent_mysql_user_password }}"
    priv: "*.*:SELECT,PROCESS,REPLICATION CLIENT,RELOAD,BACKUP_ADMIN"
    state: "present"
    host: '**********/16'
    resource_limits:
      MAX_USER_CONNECTIONS: 10
    login_user: root
    login_password: "{% if '-prod-' in inventory_hostname %}{{ mysql_root_pass_prod }}\
      {% elif '-rc-' in inventory_hostname %}{{ mysql_root_pass_rc }}\
      {% elif '-stage-' in inventory_hostname %}{{ mysql_root_pass_stage }}\
      {% elif '-dev-' in inventory_hostname %}{{ mysql_root_pass_dev }}\
      {% elif '-test-' in inventory_hostname %}{{ mysql_root_pass_test }}{% endif %}"
    login_unix_socket: "/var/run/mysqld/mysqld.sock"
# no_log: true

- name: Enroll like mysql agent
  community.docker.docker_container_exec:
    container: pmm-agent
    command: >
      pmm-admin add mysql
      --username={{ pmm_agent_mysql_user }}
      --host={{ ansible_ssh_host }}
      --password={{ pmm_agent_mysql_user_password }}
      --query-source={{ percona_cluster_mysql_pmm_agent_query_source }}
      --port 3306 {{ inventory_hostname }}
      {{ '--skip-connection-check' if pmm_agent_skip_connection_check }}
      {{ '--size-slow-logs=-1TiB' if percona_cluster_mysql_pmm_agent_query_source == 'slowlog' }}
  register: result
