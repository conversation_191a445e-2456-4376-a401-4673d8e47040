---
- name: "Copy .my.cnf file into the root home folder"
  ansible.builtin.template:
    src: root-my-cnf.j2
    dest: /root/.my.cnf
    owner: root
    group: root
    mode: 0600

- name: "Set the root password"
  community.mysql.mysql_user:
    name: root
    host: "{{ item }}"
    password: "{{ mysql_root_password }}"
    check_implicit_admin: true
    state: present
  with_items:
    - "{{ ansible_hostname }}"
    - "127.0.0.1"
    - "::1"
    - "localhost"
    - "**********/24"

- name: "Ensure anonymous users are not in the database"
  community.mysql.mysql_user:
    name: ""
    host: "{{ item }}"
    state: absent
  with_items:
    - "{{ ansible_hostname }}"
    - "localhost"

- name: "Remove the test database"
  community.mysql.mysql_db:
    name: test
    state: absent
