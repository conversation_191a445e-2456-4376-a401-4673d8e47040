---
- name: Create a network
  community.docker.docker_network:
    name: mysql-network
- name: "Setup exporter"
  community.docker.docker_container:
    image: "{{ percona_cluster_mysql_exporter_image }}:{{ percona_cluster_mysql_exporter_version }}"
    name: mysqld-exporter
    state: started
    restart: true
    restart_policy: always
    ports:
      - "0.0.0.0:{{ percona_cluster_mysql_exporter_port }}:{{ percona_cluster_mysql_exporter_port }}"
    command:
      - "--mysqld.username={{ percona_cluster_mysql_exporter_user }}:{{ mysql_prom_user_password }}"
      - "--mysqld.address={{ ansible_host }}:3306"

- name: Add service mysql exporter to consul
  ansible.builtin.import_role:
    name: consul-agents
