---
- name: Set parameters in service
  community.general.ini_file:
    dest: /etc/systemd/system/mysql.service.d/overrides.conf
    owner: root
    group: root
    mode: 0644
    section: Service
    option: "{{ item.option }}"
    value: "{{ item.value }}"
  loop:
    - option: LimitNOFILE
      value: "{{ percona_cluster_mysql_systemd_limitnofile }}"
    - option: TimeoutSec
      value: "{{ percona_cluster_mysql_systemd_timeoutsec }}"

- name: Force systemd to reread configs
  ansible.builtin.systemd:
    daemon_reload: true
