---
- name: "Make sure the MySQL databases are present"
  community.mysql.mysql_db:
    name: "{{ item.name }}"
    collation: "{{ item.collation | default('utf8_general_ci') }}"
    encoding: "{{ item.encoding | default('utf8') }}"
    state: "present"
    login_unix_socket: "{{ percona_cluster_mysql_unix_socket }}"
  with_items: "{{ mysql_databases }}"

- name: "Load timezone database into MySQL"
  ansible.builtin.shell:
    cmd: |
      set -o pipefail && mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql mysql"
  when:
    - "percona_cluster_mysql_timezone_info|bool"
  changed_when: false
