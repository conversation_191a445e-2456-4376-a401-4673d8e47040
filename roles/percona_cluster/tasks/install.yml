---
# (do not put quotes on key id, for some reason it won't work)
- name: "Obtaining percona public key"
  ansible.builtin.apt_key:
    keyserver: "keyserver.ubuntu.com"
    id: 9334A25F8507EFA5

- name: "Adding percona repository"
  ansible.builtin.apt_repository:
    repo: "deb http://repo.percona.com/apt {{ ansible_distribution_release }} main"
    state: "present"

- name: "Update apt cache"
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 300

- name: "Install percona-release package (Percona version >= 8)"
  ansible.builtin.apt:
    deb: "https://repo.percona.com/apt/percona-release_latest.{{ ansible_distribution_release }}_all.deb"
  when: percona_cluster_mysql_version_major|int >= 8

# https://www.percona.com/doc/percona-server/LATEST/installation/apt_repo.html
- name: "Enable Percona repository (Percona version >= 8)"
  ansible.builtin.command: "percona-release setup ps{{ percona_cluster_mysql_version_major }}{{ percona_cluster_mysql_version_minor }}"
  when: percona_cluster_mysql_version_major|int >= 8
  changed_when: false

- name: "Install python-is-python3 (Ubuntu >= Focal/20.04)"
  ansible.builtin.apt:
    name: "python-is-python3"
  when:
    - ansible_distribution_version is version_compare('20.04', '>=')

- name: "Get the major version of python used to run ansible"
  ansible.builtin.command: "{{ ansible_python_interpreter | default('/usr/bin/python') }} -c 'import sys; print(sys.version_info.major)'"
  register: ansible_python_major
  changed_when: false

- name: Debug
  ansible.builtin.debug:
    msg: "ansible_python_interpreter major version: {{ ansible_python_major.stdout }}"

- name: "Install package dependencies for ansible MySQL modules (python 2)"
  ansible.builtin.apt:
    name: "python-mysqldb"
  when:
    - ansible_python_major.stdout == "2"

- name: "Install package dependencies for ansible MySQL modules (python 3)"
  ansible.builtin.apt:
    name: "python3-mysqldb"
  when:
    - ansible_python_major.stdout == "3"

- name: "Install percona packages and dependencies on Ubuntu (Percona version < 8)"
  ansible.builtin.apt:
    name:
      - "percona-server-server-{{ percona_cluster_mysql_version_major }}.{{ percona_cluster_mysql_version_minor }}"
      - "percona-server-client-{{ percona_cluster_mysql_version_major }}.{{ percona_cluster_mysql_version_minor }}"
      - "percona-toolkit"
      - "percona-xtrabackup"
    state: "present"
    allow_downgrade: true
  when: percona_cluster_mysql_version_major|int < 8

- name: "Install | configure debconf for version 8.0 (Use Legacy Authentication Method)"
  ansible.builtin.debconf:
    name: "percona-server-server"
    question: "percona-server-server/default-auth-override"
    value: "Use Legacy Authentication Method (Retain MySQL 5.x Compatibility)"
    vtype: select
  changed_when: false
  when:
    - percona_cluster_mysql_version_major|int >= 8
    - percona_cluster_mysql_default_authentication_plugin is defined
    - percona_cluster_mysql_default_authentication_plugin == "mysql_native_password"

- name: "Install percona packages and dependencies on Ubuntu (Percona version >= 8)"
  ansible.builtin.apt:
    name:
      - "percona-server-common={{ percona_cluster_mysql_version_major }}.\
        {{ percona_cluster_mysql_version_minor }}.{{ percona_cluster_mysql_version_release }}*"
      - "percona-server-server={{ percona_cluster_mysql_version_major }}.\
        {{ percona_cluster_mysql_version_minor }}.{{ percona_cluster_mysql_version_release }}*"
      - "percona-server-client={{ percona_cluster_mysql_version_major }}.\
        {{ percona_cluster_mysql_version_minor }}.{{ percona_cluster_mysql_version_release }}*"
      - "percona-toolkit"
      - "percona-xtrabackup-80"
    state: "present"
  when: percona_cluster_mysql_version_major|int >= 8

- name: "Adjust permissions of datadir"
  ansible.builtin.file:
    path: "{{ item }}"
    owner: "mysql"
    group: "mysql"
    mode: 0700
    state: "directory"
  loop:
    - "{{ percona_cluster_mysql_log_bin_directory }}"
    - "{{ percona_cluster_mysql_datadir }}"
    - "{{ percona_cluster_mysql_logs_directory }}"
