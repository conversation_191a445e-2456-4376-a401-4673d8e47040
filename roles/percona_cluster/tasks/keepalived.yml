---
- name: Set cluster name
  ansible.builtin.set_fact:
    cluster_name: "{{ (group_names | select('search', 'db'))[0] }}"

- name: Add keepalived_script group
  ansible.builtin.group:
    name: keepalived_script
    state: present
    system: true

- name: Add the user 'keepalived_script'
  ansible.builtin.user:
    name: keepalived_script
    shell: /sbin/nologin
    groups: keepalived_script
    append: true
    system: true

- name: Add keepalived repository
  ansible.builtin.apt_repository:
    repo: ppa:hnakamur/keepalived

- name: Install keepalived
  ansible.builtin.apt:
    name: "keepalived={{ percona_cluster_mysql_keepalived_version }}"
    state: present
    update_cache: true

- name: Create check script
  ansible.builtin.template:
    src: mysql_check.sh.j2
    dest: "{{ percona_cluster_mysql_keepalived_etc_dir }}/mysql_check.sh"
    mode: 0755

- name: Create keepalived conf
  ansible.builtin.template:
    src: keepalived.conf.j2
    dest: "{{ percona_cluster_mysql_keepalived_etc_dir }}/keepalived.conf"
    mode: 0644
  notify: Restart keepalived

- name: Starting service keepalived
  ansible.builtin.systemd:
    name: keepalived
    state: started
    enabled: true
