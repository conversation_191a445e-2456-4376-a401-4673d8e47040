#
# The MySQL database server configuration file.
#
# For explanations see
# http://dev.mysql.com/doc/mysql/en/server-system-variables.html

[client]
port        = {{ percona_cluster_mysql_port }}
socket      = {{ percona_cluster_mysql_unix_socket }}

[mysqld_safe]
{% if percona_cluster_mysql_numa_interleave is defined %}
numa-interleave = {{ percona_cluster_mysql_numa_interleave }}
{% endif %}
socket      = {{ percona_cluster_mysql_unix_socket }}
nice        = 0
open-files-limit = {{ percona_cluster_mysql_open_files_limit }}

[mysqld]
# * Basic Settings
user        = mysql
pid-file    = /var/run/mysqld/mysqld.pid
socket      = {{ percona_cluster_mysql_unix_socket }}
port        = {{ percona_cluster_mysql_port }}
basedir     = /usr
datadir     = {{ percona_cluster_mysql_datadir }}
tmpdir      = /tmp
skip-name-resolve
sql-mode=""
{% if  percona_cluster_mysql_version is version('8.0', '>=') and percona_cluster_mysql_disable_log_bin|bool %}
disable_log_bin
{% endif %}
{% if  percona_cluster_mysql_version is version('5.7', '<') %}
# language is for pre-5.5. In 5.5 it is an alias for lc_messages_dir.
language = {{ percona_cluster_mysql_language }}
{% else %}
lc_messages_dir = {{ percona_cluster_mysql_language }}
{% endif %}
bind-address = {{ percona_cluster_mysql_bind_address }}
skip-external-locking
{% if mysql_sql_mode is defined %}
sql_mode={{ mysql_sql_mode }}
{% endif %}
{% if percona_cluster_mysql_default_authentication_plugin is defined and percona_cluster_mysql_version is version('5.7', '>=') %}
default_authentication_plugin={{ percona_cluster_mysql_default_authentication_plugin }}
{% endif %}
{% if percona_cluster_mysql_block_encryption_mode is defined %}
block_encryption_mode = {{ percona_cluster_mysql_block_encryption_mode }}
{% endif %}

{% if percona_cluster_mysql_performance_schema is defined %}
performance_schema = {{ percona_cluster_mysql_performance_schema }}
{% endif %}

{% if percona_cluster_mysql_audit_log_enabled | default(false) | bool %}
# * Audit Log Configuration
audit_log_policy={{ percona_cluster_mysql_audit_log_policy }}
audit_log_format={{ percona_cluster_mysql_audit_log_format }}
audit_log_file={{ percona_cluster_mysql_audit_log_file }}
audit_log_rotate_on_size={{ percona_cluster_mysql_audit_log_rotate_on_size }}
audit_log_rotations={{ percona_cluster_mysql_audit_log_rotations }}
{% endif %}

# * Fine Tuning
key_buffer_size         = {{ percona_cluster_mysql_key_buffer }}
max_allowed_packet      = {{ percona_cluster_mysql_max_allowed_packet }}
thread_stack            = {{ percona_cluster_mysql_thread_stack }}
thread_cache_size       = {{ percona_cluster_mysql_cache_size }}
{% if  percona_cluster_mysql_version is version('5.7', '<') %}
myisam-recover          = {{ percona_cluster_mysql_myisam_recover }}
{% else %}
myisam-recover-options  = {{ percona_cluster_mysql_myisam_recover }}
{% endif %}
max_connections         = {{ percona_cluster_mysql_max_connections }}
table_open_cache        = {{ percona_cluster_mysql_table_cache }}
{% if  percona_cluster_mysql_table_open_cache_instances is defined %}
table_open_cache_instances = {{ percona_cluster_mysql_table_open_cache_instances }}
{% endif %}
table_definition_cache  = {{ percona_cluster_mysql_table_definition_cache }}
innodb_open_files       = {{ percona_cluster_mysql_innodb_open_files }}
{% if  percona_cluster_mysql_innodb_thread_concurrency is defined %}
innodb_thread_concurrency = {{ percona_cluster_mysql_innodb_thread_concurrency }}
{% endif %}
max_heap_table_size     = {{ percona_cluster_mysql_max_heap_table_size }}
{% if  percona_cluster_mysql_version is version('5.7', '<') %}
thread_concurrency      = {{ percona_cluster_mysql_thread_concurrency }}
{% endif %}
sort_buffer_size        = {{ percona_cluster_mysql_sort_buffer_size }}
{% if percona_cluster_mysql_read_buffer_size is defined %}
read_buffer_size        = {{ percona_cluster_mysql_read_buffer_size }}
{% endif %}
{% if percona_cluster_mysql_read_rnd_buffer_size is defined %}
read_rnd_buffer_size    = {{ percona_cluster_mysql_read_rnd_buffer_size }}
{% endif %}
{% if percona_cluster_mysql_tmp_table_size is defined %}
tmp_table_size          = {{ percona_cluster_mysql_tmp_table_size }}
{% endif %}
{% if percona_cluster_mysql_join_buffer_size is defined %}
join_buffer_size        = {{ percona_cluster_mysql_join_buffer_size }}
{% endif %}
{% if percona_cluster_mysql_innodb_buffer_pool_instances is defined %}
innodb_buffer_pool_instances = {{ percona_cluster_mysql_innodb_buffer_pool_instances }}
{% endif %}
{% if percona_cluster_mysql_innodb_numa_interleave is defined %}
innodb_numa_interleave = {{ percona_cluster_mysql_innodb_numa_interleave }}
{% endif %}
{% if percona_cluster_mysql_innodb_use_native_aio is defined %}
innodb_use_native_aio = {{ percona_cluster_mysql_innodb_use_native_aio }}
{% endif %}
{% if percona_cluster_mysql_skip_innodb_doublewrite is defined %}
skip-innodb_doublewrite
{% endif %}
{% if percona_cluster_mysql_transaction_isolation is defined %}
transaction-isolation   = {{ percona_cluster_mysql_transaction_isolation }}
{% endif %}
{% if percona_cluster_mysql_thread_pool_size is defined %}
thread_pool_size        = {{ percona_cluster_mysql_thread_pool_size }}
{% endif %}
{% if percona_cluster_mysql_max_sort_length is defined %}
max_sort_length         = {{ percona_cluster_mysql_max_sort_length }}
{% endif %}
{% if percona_cluster_mysql_select_into_buffer_size is defined %}
select_into_buffer_size = {{ percona_cluster_mysql_select_into_buffer_size }}
{% endif %}
{% if percona_cluster_mysql_thread_handling is defined %}
thread_handling = {{ percona_cluster_mysql_thread_handling }}
{% endif %}

{% if percona_cluster_mysql_version_major|int < 8 %}
# ** Query Cache Configuration, removed in MySQL >= 8.0
query_cache_limit       = {{ percona_cluster_mysql_query_cache_limit }}
query_cache_size        = {{ percona_cluster_mysql_query_cache_size }}
{% endif %}

{% if percona_cluster_mysql_replica_parallel_workers is defined %}
replica_parallel_workers = {{ percona_cluster_mysql_replica_parallel_workers }}
{% endif %}
{% if percona_cluster_mysql_replica_parallel_type is defined %}
replica_parallel_type = {{ percona_cluster_mysql_replica_parallel_type }}
{% endif %}
{% if percona_cluster_mysql_replica_preserve_commit_order is defined %}
replica_preserve_commit_order = {{ percona_cluster_mysql_replica_preserve_commit_order }}
{% endif %}
{% if percona_cluster_mysql_sync_relay_log is defined %}
sync_relay_log = {{ percona_cluster_mysql_sync_relay_log }}
{% endif %}
{% if percona_cluster_mysql_relay_log is defined %}
relay_log = {{ percona_cluster_mysql_relay_log }}
{% endif %}
{% if percona_cluster_mysql_max_relay_log_size is defined %}
max_relay_log_size = {{ percona_cluster_mysql_max_relay_log_size }}
{% endif %}
{% if percona_cluster_mysql_log_slave_updates is defined %}
log_slave_updates = {{ percona_cluster_mysql_log_slave_updates }}
{% endif %}
{% if percona_cluster_mysql_replica_pending_jobs_size_max is defined %}
replica_pending_jobs_size_max = {{ percona_cluster_mysql_replica_pending_jobs_size_max }}
{% endif %}
{% if percona_cluster_mysql_replica_compressed_protocol is defined %}
replica_compressed_protocol = {{ percona_cluster_mysql_replica_compressed_protocol }}
{% endif %}
{% if percona_cluster_mysql_replica_allow_batching is defined %}
replica_allow_batching = {{ percona_cluster_mysql_replica_allow_batching }}
{% endif %}
{% if percona_cluster_mysql_slave_sql_verify_checksum is defined %}
slave-sql-verify-checksum
{% endif %}
{% if percona_cluster_mysql_relay_log_info_repository is defined %}
relay_log_info_repository = {{ percona_cluster_mysql_relay_log_info_repository }}
{% endif %}

# ** Logging and Replication
log_error = /var/log/mysql/error.log
{% if percona_cluster_mysql_version_major|int < 8 %}
log_warnings = 2
{% else %}
log_error_verbosity = {{ percona_cluster_mysql_log_error_verbosity }}

{% endif %}
#general_log_file        = /var/log/mysql/mysql.log
#general_log             = 1
#
{% if percona_cluster_mysql_slow_query_log is defined %}
slow_query_log          = {{ percona_cluster_mysql_slow_query_log }}
{% endif %}
{% if percona_cluster_mysql_slow_query_log_file is defined %}
slow_query_log_file     = {{ percona_cluster_mysql_slow_query_log_file }}
{% endif %}
{% if percona_cluster_mysql_long_query_time is defined %}
long_query_time = {{ percona_cluster_mysql_long_query_time }}
{% endif %}
{% if percona_cluster_mysql_log_queries_not_using_indexes is defined %}
log-queries-not-using-indexes = {{ percona_cluster_mysql_log_queries_not_using_indexes }}
{% endif %}
{% if percona_cluster_mysql_log_slow_admin_statements is defined %}
log_slow_admin_statements = {{ percona_cluster_mysql_log_slow_admin_statements }}
{% endif %}
{% if percona_cluster_mysql_log_slow_slave_statements is defined %}
log_slow_slave_statements = {{ percona_cluster_mysql_log_slow_slave_statements }}
{% endif %}

#long_query_time = 2
#log-queries-not-using-indexes
#
# The following can be used as easy to replay backup logs or for replication.
{% if percona_cluster_mysql_server_id is defined %}
server-id      = {{ percona_cluster_mysql_server_id }}
{% endif %}
log_bin            = {{ percona_cluster_mysql_log_bin }}
expire_logs_days    = {{ percona_cluster_mysql_expire_logs_days }}
max_binlog_size     = {{ percona_cluster_mysql_max_binlog_size }}
sync_binlog         = {{ percona_cluster_mysql_sync_binlog }}
#binlog_do_db       = include_database_name
#binlog_ignore_db   = include_database_name
{% if percona_cluster_mysql_gtid_mode is defined %}
gtid_mode = {{ percona_cluster_mysql_gtid_mode }}
{% endif %}
{% if percona_cluster_mysql_enforce_gtid_consistency is defined %}
enforce_gtid_consistency = {{ percona_cluster_mysql_enforce_gtid_consistency }}
{% endif %}
{% if percona_cluster_mysql_binlog_transaction_compression is defined %}
binlog_transaction_compression = {{ percona_cluster_mysql_binlog_transaction_compression }}
{% endif %}
{% if percona_cluster_mysql_binlog_format is defined %}
binlog_format = {{ percona_cluster_mysql_binlog_format }}
{% endif %}

# ** InnoDB
# InnoDB is enabled by default with a 10MB datafile in /var/lib/mysql/.
# Read the manual for more InnoDB related options. There are many!
innodb_flush_log_at_trx_commit = {{ percona_cluster_mysql_innodb_flush_log_at_trx_commit }}
innodb_buffer_pool_size = {{ percona_cluster_mysql_innodb_buffer_pool_size }}
{% if percona_cluster_mysql_innodb_flush_method != 'fdatasync': %}
innodb_flush_method = {{ percona_cluster_mysql_innodb_flush_method }}
{% endif %}
innodb_lock_wait_timeout = {{ percona_cluster_mysql_innodb_lock_wait_timeout }}
innodb_log_buffer_size = {{ percona_cluster_mysql_innodb_log_buffer_size }}
innodb_log_file_size = {{ percona_cluster_mysql_innodb_log_file_size }}
innodb_file_per_table = {{ percona_cluster_mysql_innodb_file_per_table }}
{% if percona_cluster_mysql_innodb_io_capacity is defined %}
innodb_io_capacity = {{ percona_cluster_mysql_innodb_io_capacity }}
{% endif %}
{% if percona_cluster_mysql_innodb_io_capacity_max is defined %}
innodb_io_capacity_max = {{ percona_cluster_mysql_innodb_io_capacity_max }}
{% endif %}
{% if percona_cluster_mysql_innodb_read_io_threads is defined %}
innodb_read_io_threads = {{ percona_cluster_mysql_innodb_read_io_threads }}
{% endif %}
{% if percona_cluster_mysql_innodb_write_io_threads is defined %}
innodb_write_io_threads = {{ percona_cluster_mysql_innodb_write_io_threads }}
{% endif %}
{% if percona_cluster_mysql_innodb_ddl_threads is defined %}
innodb_ddl_threads = {{ percona_cluster_mysql_innodb_ddl_threads }}
{% endif %}
{% if percona_cluster_mysql_innodb_use_fdatasync is defined %}
innodb_use_fdatasync = {{ percona_cluster_mysql_innodb_use_fdatasync }}
{% endif %}
{% if percona_cluster_mysql_innodb_parallel_read_threads is defined %}
innodb_parallel_read_threads = {{ percona_cluster_mysql_innodb_parallel_read_threads }}
{% endif %}
{% if percona_cluster_mysql_bulk_insert_buffer_size is defined %}
bulk_insert_buffer_size = {{ percona_cluster_mysql_bulk_insert_buffer_size }}
{% endif %}
{% if percona_cluster_mysql_innodb_buffer_pool_chunk_size is defined %}
innodb_buffer_pool_chunk_size = {{ percona_cluster_mysql_innodb_buffer_pool_chunk_size }}
{% endif %}

{% if mysql_optimizer_switch is defined %}
# Check https://bugs.mysql.com/bug.php?id=69721 for more info
optimizer_switch = {{ mysql_optimizer_switch }}
{% endif %}

# ** Security Features
# Read the manual, too, if you want chroot!
# chroot = /var/lib/mysql/

character_set_server = {{ percona_cluster_mysql_character_set_server }}
collation_server = {{ percona_cluster_mysql_collation_server }}
character-set-client-handshake = {{ percona_cluster_mysql_character_set_client_handshake }}


{% if percona_cluster_mysql_xtrabackup_open_files_limit is defined %}
[xtrabackup]
open-files-limit = {{ percona_cluster_mysql_xtrabackup_open_files_limit }}
{% endif %}

[mysqldump]
quick
quote-names
max_allowed_packet  = {{ percona_cluster_mysql_mysqldump_max_allowed_packet }}

[mysql]
#no-auto-rehash # faster start of mysql but no tab completition

[isamchk]
key_buffer      = {{ percona_cluster_mysql_isamchk_key_buffer }}

#
# * IMPORTANT: Additional settings that can override those from this file!
#   The files must end with '.cnf', otherwise they'll be ignored.
#
!includedir /etc/mysql/conf.d/
