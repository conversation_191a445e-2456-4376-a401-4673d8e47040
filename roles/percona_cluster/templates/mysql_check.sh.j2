#!/bin/bash

MYSQL_USER="root"
MYSQL_PASSWORD="{{ mysql_root_password }}"
MYSQL_HOST="localhost"
MYSQL_PORT="3306"

function check_mysql_availability() {
    if mysqladmin -h $MYSQL_HOST -P $MYSQL_PORT -u $MYSQL_USER -p$MYSQL_PASSWORD ping &> /dev/null; then
        echo "available"
    else
        echo "unavailable"
    fi
}

function check_mysql_replication_status() {
    replication_status=$(mysql -h $MYSQL_HOST -P $MYSQL_PORT -u $MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW SLAVE STATUS\G" | grep "Slave_IO_Running\|Slave_SQL_Running" | awk '{print $2}')

    if [[ $replication_status == "Yes" ]]; then
        echo "slave"
    else
        echo "master"
    fi
}

function check_mysql_status() {
    availability=$(check_mysql_availability)

    if [[ $availability == "unavailable" ]]; then
        exit 1
    fi

    role=$(check_mysql_replication_status)

    if [[ $role == "master" ]]; then
        exit 0
    else
        exit 1
    fi
}

check_mysql_status
