# Percona Server template configuration

[client]
port        = {{ percona_cluster_mysql_port }}
socket      = /var/lib/mysql/mysql.sock


[mysqld]
#
# Remove leading # and set to the amount of RAM for the most important data
# cache in MySQL. Start at 70% of total RAM for dedicated server, else 10%.
# innodb_buffer_pool_size = 128M
#
# Remove leading # to turn on a very important data integrity option: logging
# changes to the binary log between backups.
# log_bin
#
# Remove leading # to set options mainly useful for reporting servers.
# The server defaults are faster for transactions and fast SELECTs.
# Adjust sizes as needed, experiment to find the optimal values.
# join_buffer_size = 128M
# sort_buffer_size = 2M
# read_rnd_buffer_size = 2M
user        = mysql
socket      = /var/lib/mysql/mysql.sock
pid-file    = /var/run/mysqld/mysqld.pid
port        = {{ percona_cluster_mysql_port }}
basedir     = /usr
datadir     = {{ percona_cluster_mysql_datadir }}
tmpdir      = /tmp
{% if  percona_cluster_mysql_version is version('8.0', '>=') and percona_cluster_mysql_disable_log_bin|bool %}
disable_log_bin
{% endif %}
{% if mysql_sql_mode is defined %}
sql_mode={{ mysql_sql_mode }}
{% endif %}
{% if percona_cluster_mysql_default_authentication_plugin is defined and percona_cluster_mysql_version is version('5.7', '>=') %}
default_authentication_plugin={{ percona_cluster_mysql_default_authentication_plugin }}
{% endif %}

# language is for pre-5.5. In 5.5 it is an alias for lc_messages_dir.
language = {{ percona_cluster_mysql_language }}
bind-address = {{ percona_cluster_mysql_bind_address }}
skip-external-locking

# * Fine Tuning
key_buffer_size         = {{ percona_cluster_mysql_key_buffer }}
max_allowed_packet      = {{ percona_cluster_mysql_max_allowed_packet }}
thread_stack            = {{ percona_cluster_mysql_thread_stack }}
thread_cache_size       = {{ percona_cluster_mysql_cache_size }}
{% if  percona_cluster_mysql_version is version('5.7', '<') %}
myisam-recover          = {{ percona_cluster_mysql_myisam_recover }}
{% else %}
myisam-recover-options  = {{ percona_cluster_mysql_myisam_recover }}
{% endif %}
max_connections         = {{ percona_cluster_mysql_max_connections }}
table_open_cache        = {{ percona_cluster_mysql_table_cache }}
{% if  percona_cluster_mysql_version is version('5.7', '<') %}
thread_concurrency      = {{ percona_cluster_mysql_thread_concurrency }}
{% endif %}
sort_buffer_size        = {{ percona_cluster_mysql_sort_buffer_size }}

# ** Query Cache Configuration, removed in MySQL >= 8.0
{% if percona_cluster_mysql_version_major|int < 8 %}
query_cache_limit       = {{ percona_cluster_mysql_query_cache_limit }}
query_cache_size        = {{ percona_cluster_mysql_query_cache_size }}
{% endif %}
# ** Logging and Replication
log_error = /var/log/mysqld.log
{% if percona_cluster_mysql_version_major|int < 8 %}
log_warnings = 2
{% else %}
log_error_verbosity = 2
{% endif %}
#general_log_file        = /var/log/mysql/mysqld.log
#general_log             = 1
#
#log_slow_queries   = /var/log/mysql/mysql-slow.log
#long_query_time = 2
#log-queries-not-using-indexes
#
# The following can be used as easy to replay backup logs or for replication.
#server-id      = 1
#log_bin            = /var/log/mysql/mysql-bin.log
expire_logs_days    = 10
max_binlog_size     = 100M
#binlog_do_db       = include_database_name
#binlog_ignore_db   = include_database_name

# ** InnoDB
# InnoDB is enabled by default with a 10MB datafile in /var/lib/mysql/.
# Read the manual for more InnoDB related options. There are many!
innodb_flush_log_at_trx_commit = {{ percona_cluster_mysql_innodb_flush_log_at_trx_commit }}
innodb_buffer_pool_size = {{ percona_cluster_mysql_innodb_buffer_pool_size }}
{% if percona_cluster_mysql_innodb_flush_method != 'fdatasync': %}
innodb_flush_method = {{ percona_cluster_mysql_innodb_flush_method }}
{% endif %}
innodb_lock_wait_timeout = {{ percona_cluster_mysql_innodb_lock_wait_timeout }}
innodb_log_buffer_size = {{ percona_cluster_mysql_innodb_log_buffer_size }}
innodb_log_file_size = {{ percona_cluster_mysql_innodb_log_file_size }}
innodb_file_per_table = {{ percona_cluster_mysql_innodb_file_per_table }}

{% if mysql_optimizer_switch is defined %}
# Check https://bugs.mysql.com/bug.php?id=69721 for more info
optimizer_switch = {{ mysql_optimizer_switch }}
{% endif %}

# ** Security Features
# Read the manual, too, if you want chroot!
# chroot = /var/lib/mysql/

character_set_server = {{ percona_cluster_mysql_character_set_server }}
collation_server = {{ percona_cluster_mysql_collation_server }}
character-set-client-handshake = {{ percona_cluster_mysql_character_set_client_handshake }}

[mysqldump]
quick
quote-names
max_allowed_packet  = {{ percona_cluster_mysql_mysqldump_max_allowed_packet }}

[mysql]
#no-auto-rehash # faster start of mysql but no tab completition

[isamchk]
key_buffer      = {{ percona_cluster_mysql_isamchk_key_buffer }}
 
# Disabling symbolic-links is recommended to prevent assorted security risks
symbolic-links=0

[mysqld_safe]
log-error=/var/log/mysqld.log
pid-file=/var/run/mysqld/mysqld.pid
nice        = 0
open-files-limit = 16384


