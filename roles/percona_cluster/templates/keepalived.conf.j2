vrrp_script chk_mysql {
  script "{{ percona_cluster_mysql_keepalived_etc_dir }}/mysql_check.sh"
  interval 2
}

vrrp_instance VI_1 {
  interface {{ hostvars[inventory_hostname]['ansible_default_ipv4']['interface'] }}
  state BACKUP
  virtual_router_id 202
  priority {% if '-01' in inventory_hostname %}100{% elif '-02' in inventory_hostname %}90{% else %}80{% endif %}

  advert_int 1
  unicast_src_ip {{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}
  unicast_peer {
        {% for i in groups[cluster_name] %}
	 {{ hostvars[i]['ansible_host'] }}
	{% endfor %}
    }

  authentication {
    auth_type PASS
    auth_pass {{ percona_cluster_mysql_keepalived_auth_pass }}
  }

  virtual_ipaddress {
    {{ mysql_keepalived_vip }}
  }

  track_script {
    chk_mysql
  }
}
