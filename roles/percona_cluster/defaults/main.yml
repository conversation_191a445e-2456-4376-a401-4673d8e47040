---
# Version to install, defaulting to 5.6
percona_cluster_mysql_version_major: "8"
percona_cluster_mysql_version_minor: "0"
percona_cluster_mysql_version_release: "37-29-1"
percona_cluster_mysql_version: "{{ percona_cluster_mysql_version_major | int }}.{{ percona_cluster_mysql_version_minor | int }}"
# Basic settings
percona_cluster_mysql_port: "3306"
percona_cluster_mysql_bind_address: "0.0.0.0"
percona_cluster_mysql_language: "/usr/share/mysql/"
percona_cluster_mysql_datadir: "/data/mysql"
percona_cluster_mysql_open_files_limit: "10000"
percona_cluster_mysql_log_error_verbosity: "2"
# Fine tuning
percona_cluster_mysql_key_buffer: "8388608"
percona_cluster_mysql_max_allowed_packet: "67108864"
percona_cluster_mysql_thread_stack: "1048576"
percona_cluster_mysql_cache_size: "9"
percona_cluster_mysql_myisam_recover: "OFF"
percona_cluster_mysql_max_connections: "151"
percona_cluster_mysql_table_cache: "4000"
percona_cluster_mysql_thread_concurrency: "10"
percona_cluster_mysql_query_cache_limit: "1M"
percona_cluster_mysql_query_cache_size: "16M"
percona_cluster_mysql_character_set_server: "utf8mb4"
percona_cluster_mysql_collation_server: "utf8mb4_0900_ai_ci"
percona_cluster_mysql_mysqldump_max_allowed_packet: "1073741824"
percona_cluster_mysql_isamchk_key_buffer: "16M"
percona_cluster_mysql_sort_buffer_size: "262144"
percona_cluster_mysql_tmp_table_size: "16777216"
percona_cluster_mysql_max_heap_table_size: "16777216"
percona_cluster_mysql_table_definition_cache: "2000"
percona_cluster_mysql_logs_directory: /data/var/logs
percona_cluster_mysql_log_bin_directory: /data/logs
percona_cluster_mysql_log_bin: "{{ percona_cluster_mysql_log_bin_directory }}/mysql-bin.log"
percona_cluster_mysql_sync_binlog: "1"
percona_cluster_mysql_expire_logs_days: "0"
percona_cluster_mysql_max_binlog_size: "1073741824"
# InnoDB tuning
percona_cluster_mysql_innodb_file_per_table: "ON"
percona_cluster_mysql_innodb_flush_method: "fsync"
percona_cluster_mysql_innodb_buffer_pool_size: "134217728"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "1"
percona_cluster_mysql_innodb_lock_wait_timeout: "50"
percona_cluster_mysql_innodb_log_buffer_size: "16777216"
percona_cluster_mysql_innodb_log_file_size: "50331648"
percona_cluster_mysql_innodb_open_files: "4000"
percona_cluster_mysql_character_set_client_handshake: "FALSE"
percona_cluster_mysql_timezone_info: "false"
# To disable log_bin in percona >=8, enabled by default
percona_cluster_mysql_disable_log_bin: "false"
# Default Auth Plugin
# used in templates when Percona Server >= 5.7
percona_cluster_mysql_default_authentication_plugin: "mysql_native_password"
percona_cluster_mysql_systemd_limitnofile: "1048576"
percona_cluster_mysql_systemd_timeoutsec: "3600"
# Audit log
percona_cluster_mysql_audit_log_enabled: false
percona_cluster_mysql_audit_log_policy: "LOGINS"
percona_cluster_mysql_audit_log_format: "JSON"
percona_cluster_mysql_audit_log_file: "{{ percona_cluster_mysql_logs_directory }}/audit.log"
percona_cluster_mysql_audit_log_rotate_on_size: "10240M"
percona_cluster_mysql_audit_log_rotations: "90"
percona_cluster_mysql_audit_log_filter_compression: "GZIP"

percona_cluster_mysql_unix_socket: /var/run/mysqld/mysqld.sock

# Keepalived
percona_cluster_mysql_keepalived_setup: false
percona_cluster_mysql_keepalived_version: 1:2.2.8-1ubuntu1ppa1~focal
percona_cluster_mysql_keepalived_etc_dir: /etc/keepalived
percona_cluster_mysql_keepalived_auth_pass: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  35663661393761656332366161396662343561343834366132373863336232373765333633323030
  6562653036346134316261303137616266366338343237620a663562663661393634363233616361
  39663031326264366539613262633262663337313462613461373861313534656236393737666332
  3336636232653666640a346661643433383237303963666638356462386633613636376663616334
  3835
percona_cluster_mysql_pmm_agent_query_source: perfschema

percona_cluster_mysql_exporter_image: prom/mysqld-exporter
percona_cluster_mysql_exporter_version: v0.15.1
percona_cluster_mysql_exporter_port: 9104
percona_cluster_mysql_exporter_user: prom_user
