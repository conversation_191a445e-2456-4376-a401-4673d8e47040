---
# defaults file for roles/bareos
bareos_version: next

# For Ubuntu
bareos_ubuntu_repo: 'https://download.bareos.org/{{ bareos_version }}/xUbuntu_{{ ansible_distribution_version }}'
bareos_ubuntu_gpg_key: 'https://download.bareos.org/{{ bareos_version }}/xUbuntu_{{ ansible_distribution_version }}/Release.key'

# For Debian
bareos_repo: 'https://download.bareos.org/{{ bareos_version }}/{{ ansible_distribution }}_{{ ansible_distribution_version }}'
bareos_gpg_key: 'https://download.bareos.org/{{ bareos_version }}/{{ ansible_distribution }}_{{ ansible_distribution_version }}/Release.key'

# For CentOS
bareos_centos_repo: 'https://download.bareos.org/{{ bareos_version }}/CentOS_7/bareos.repo'
bareos_centos_gpg_key: 'https://download.bareos.org/{{ bareos_version }}/CentOS_7/RPM-GPG-KEY'

bareos_package:
  - bareos-client
  - bareos-filedaemon-python3-plugin

bareos_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66316565353463383030623532313837343934646263303065626163316639626431653762383262
          6531383933396261383931346636643666363335656361300a633566643561636361336364666261
          61353863323765623261616363393066626337323337653161346265653962626532336562323534
          3666356236623532650a333333363561373763646463313165666539613934643964393939316531
          37353732666166353761303330376461616539373639646331333039363930383532

bareos_fd_directory: /etc/bareos/bareos-fd.d

bareos_dir_docker_name: 'bareos_bareos-dir_1'
