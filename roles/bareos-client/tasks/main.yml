---
# tasks file for roles/bareos
- name: Set client ip address
  ansible.builtin.set_fact:
    bareos_client_ip_address: "{{ ansible_ssh_host }}"

- name: Add an Apt signing key for Ubuntu
  ansible.builtin.apt_key:
    url: '{{ bareos_ubuntu_gpg_key }}'
    state: present
  when: ansible_distribution == "Ubuntu"

- name: Print repo variable
  ansible.builtin.debug:
    msg: "{{ bareos_ubuntu_repo }}"

- name: Add bareos repository into sources list using specified filename on Ubuntu
  ansible.builtin.apt_repository:
    repo: "{{ item.repo }}"
    state: "{{ item.state }}"
    filename: "{{ item.filename }}"
  when: ansible_distribution == "Ubuntu"
  loop:
    - filename: bareos.list
      repo: 'deb https://download.bareos.org/bareos/release/21/xUbuntu_20.04 /'
      state: absent
    - filename: bareos
      repo: 'deb {{ bareos_ubuntu_repo }} /'
      state: present

- name: Add bareos repository into sources list using specified filename on CentOS
  ansible.builtin.get_url:
    url: "{{ bareos_centos_repo }}"
    dest: /etc/yum.repos.d/bareos.repo
    mode: 0644
  when: ansible_distribution == "CentOS"

- name: Add an Apt signing key for Debian
  ansible.builtin.apt_key:
    url: '{{ bareos_gpg_key }}'
    state: present
  when: ansible_distribution == "Debian"

- name: Add an rpm signing key for CentOS
  ansible.builtin.rpm_key:
    key: '{{ bareos_centos_gpg_key }}'
    state: present
  when: ansible_distribution == "CentOS"

- name: Add bareos repository into sources list using specified filename
  ansible.builtin.apt_repository:
    repo: 'deb {{ bareos_repo }} /'
    state: present
    filename: bareos.list
  when: ansible_distribution == "Debian"

- name: Install bareos on Debian or Ubuntu
  ansible.builtin.apt:
    name: "{{ item }}"
    update_cache: true
  with_items: "{{ bareos_package }}"
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"

- name: Install bareos on CentOS
  ansible.builtin.package:
    name: "{{ item }}"
  with_items: "{{ bareos_package }}"
  when: ansible_distribution == "CentOS"

- name: Start bareos-filedaemon
  ansible.builtin.systemd:
    name: bareos-filedaemon
    state: started
    daemon_reload: true
    enabled: true

- name: Copy client config to client
  ansible.builtin.template:
    src: client-myself.conf.j2
    dest: "{{ bareos_fd_directory }}/client/myself.conf"
    mode: "0644"
    owner: bareos
    group: bareos

- name: Copy bareos-dir config to client
  ansible.builtin.template:
    src: client-bareos-dir.conf.j2
    dest: "{{ bareos_fd_directory }}/director/bareos-dir.conf"
    mode: "0644"
    owner: bareos
    group: bareos

- name: Copy bareos-mon config to client
  ansible.builtin.template:
    src: bareos-mon.conf.j2
    dest: "{{ bareos_fd_directory }}/director/bareos-mon.conf"
    mode: "0644"
    owner: bareos
    group: bareos

- name: Restart service bareos-filedaemon
  ansible.builtin.systemd:
    state: restarted
    name: bareos-filedaemon

- name: Copy bareos-dir config to director
  ansible.builtin.template:
    dest: '/data/bareos/config/director/bareos-dir.d/client/{{ inventory_hostname }}.conf'
    src: bareos-dir.conf.j2
    mode: "0644"
    owner: systemd-network
    group: root
  delegate_to: "{{ groups.bareos[0] }}"

- name: Stop a director container
  community.docker.docker_container:
    name: '{{ bareos_dir_docker_name }}'
    state: stopped
  delegate_to: "{{ groups.bareos[0] }}"

- name: Start a director container
  community.docker.docker_container:
    name: '{{ bareos_dir_docker_name }}'
    state: started
  delegate_to: "{{ groups.bareos[0] }}"
