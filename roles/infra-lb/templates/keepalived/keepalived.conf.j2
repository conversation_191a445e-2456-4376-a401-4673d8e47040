vrrp_script chk_nginx {
  script "{{ nginx_keepalived_etc_dir }}/nginx_check.sh"
  interval 2
}

{% if nginx_external_vip is defined %}

vrrp_instance VI_1 {
  interface ens18

  state BACKUP

  virtual_router_id 51

  priority {% if '-01' in inventory_hostname %}200{% elif '-02' in inventory_hostname %}100{% else %}90{% endif %}

  advert_int 1

  unicast_src_ip {{ hostvars[inventory_hostname]['ansible_host'] }}
  {% if '-01' in inventory_hostname %}
  
  unicast_peer {
    {{ hostvars[groups[cluster_name][1]]['ansible_host'] }}
  }
  {% elif '-02' in inventory_hostname %}
  unicast_peer {
    {{ hostvars[groups[cluster_name][0]]['ansible_host'] }}
  }
  {% else %}
  {% endif %}

  authentication {
    auth_type PASS
    auth_pass {{ nginx_keepalived_auth_pass }}
  }

  virtual_ipaddress {
    {{ nginx_keepalived_external_vip }}{% if nginx_keepalived_external_vip_mask is defined %}/{{ nginx_keepalived_external_vip_mask }}{% endif %} {% if nginx_keepalived_external_vip_brd is defined %}brd {{ nginx_keepalived_external_vip_brd }}{% endif %} dev ens19

  }
  virtual_routes {
    0.0.0.0/0 via {{ nginx_keepalived_external_vip_gateway }} dev ens19
  }
  track_script {
    chk_nginx
  }
}
{% endif %}
{% if nginx_internal_vip is defined %}

vrrp_instance VI_1 {
  interface ens18

  state BACKUP

  virtual_router_id 61

  priority {% if '-01' in inventory_hostname %}200{% elif '-02' in inventory_hostname %}100{% else %}90{% endif %}

  advert_int 1

  unicast_src_ip {{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}
  unicast_peer {
        {% for i in groups[cluster_name] %}
	 {{ hostvars[i]['ansible_default_ipv4']['address'] }}
	{% endfor %}
    }

  authentication {
    auth_type PASS
    auth_pass {{ nginx_keepalived_auth_pass }}
  }

  virtual_ipaddress {
    {{ nginx_internal_vip }}

  }

  track_script {
    chk_nginx
  }
}
{% endif %}
