import os
import datetime
import re
import subprocess
import time
import consul
from certbot.main import main as certbot_main

CONSUL_HOST = "{{ consul_api_host }}"
CONSUL_PORT = "{{ consul_api_port }}"
CONSUL_ACL_TOKEN = "{{ consul_cluster_acl_token }}"

NGINX_DIR = '{{ nginx_sites_dir }}'
LETSENCRYPT_DIR = '/etc/letsencrypt/live'

#WEBROOT_PATH = '{{ nginx_certbot_webroot_path }}'
EMAIL = '{{ nginx_certbot_email }}'
ACME_SERVER = 'https://ipa-ca.sl.local/acme/directory'
HOOK_SCRIPT = '/etc/nginx/certbot-dns-ipa.py'

certbot_args = [
    'certonly',

    '--non-interactive',
    '--nginx',
    '--email', EMAIL,
    '--agree-tos',
    '--no-redirect'
]

consul_client = consul.Consul(
    host=CONSUL_HOST,
    port=CONSUL_PORT,
    token=CONSUL_ACL_TOKEN
)

os.environ['PATH'] = '/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/sbin/nginx'

def is_valid_cert(cert_path):
    if not os.path.exists(cert_path):
        return False

    cert_expiry_days = None

    try:
        output = subprocess.check_output(['openssl', 'x509', '-enddate', '-noout', '-in', cert_path])
        output = output.decode().strip()
        expiry_date_str = output.split('=')[1]
        expiry_date = datetime.datetime.strptime(expiry_date_str, '%b %d %H:%M:%S %Y %Z')
        cert_expiry_days = (expiry_date - datetime.datetime.now()).days
    except Exception as e:
        logging.error(f"Error occurred while checking certificate validity: {str(e)}")

    return cert_expiry_days is not None and cert_expiry_days >= 7

def comment_https_block(conf_file):
    with open(conf_file, 'r') as f:
        lines = f.readlines()

    with open(conf_file, 'w') as f:
        server_blocks = 0
        inside_server_block = False
        end_server_block = False
        for line in lines:
            if 'server' in line and '{' in line:
                server_blocks += 1
                inside_server_block = True
                end_server_block = False

            if '}' in line and inside_server_block:
                end_server_block = True

            if end_server_block and server_blocks > 1 and not line.strip().startswith('#'):
                line = '# ' + line

            if inside_server_block and server_blocks > 1 and not line.strip().startswith('#') and not end_server_block:
                line = '# ' + line

            f.write(line)

def uncomment_https_block(conf_file):
    with open(conf_file, 'r') as f:
        lines = f.readlines()

    with open(conf_file, 'w') as f:
        server_blocks = 0
        inside_server_block = False
        end_server_block = False
        for line in lines:
            if 'server' in line and '{' in line:
                server_blocks += 1
                inside_server_block = True
                end_server_block = False

            if '}' in line and inside_server_block:
                end_server_block = True

            if end_server_block and server_blocks > 1 and line.strip().startswith('#'):
                line = line[2:]

            if inside_server_block and server_blocks > 1 and line.strip().startswith('#') and not end_server_block:
                line = line[2:]

            f.write(line)

def remove_certbot_comments(conf_file):
    with open(conf_file, 'r') as f:
        lines = f.readlines()

    with open(conf_file, 'w') as f:
        for line in lines:
            if not line.rstrip().endswith('# managed by Certbot'):
                f.write(line)

def restart_nginx():
    subprocess.run(['systemctl', 'restart', 'nginx'])

# def run_certbot(domain_name):
#     if 'sl.local' in domain_name:
#         certbot_command = [
#             'certbot',
#             '--server', ACME_SERVER,
#             'certonly',
#             '--domain', domain_name,
#             '--preferred-challenges', 'dns',
#             '--manual',
#             '--manual-public-ip-logging-ok',
#             '--manual-auth-hook', HOOK_SCRIPT,
#             '--manual-cleanup-hook', HOOK_SCRIPT,
#             '--email', EMAIL,
#             '--agree-tos',
#             '--no-eff-email',
#             '--quiet'
#         ]

#         subprocess.run(certbot_command)
#     else:
#         certbot_args_domain = certbot_args + ['-d', domain_name]
#         certbot_main(certbot_args_domain)

def run_certbot(domain_name):
    if 'sl.local' in domain_name:
        certbot_command = [
            'certbot',
            '--server', ACME_SERVER,
            'certonly',
            '--domain', domain_name,
            '--preferred-challenges', 'dns',
            '--manual',
            '--key-type', 'rsa',
            '--manual-auth-hook', HOOK_SCRIPT,
            '--manual-cleanup-hook', HOOK_SCRIPT,
            '--email', EMAIL,
            '--agree-tos',
            '--no-eff-email',
            '--quiet'
        ]

        subprocess.run(certbot_command)
    else:
        pass

def save_cert_to_consul(domain_name, file_name):
    dir_path = os.path.join(LETSENCRYPT_DIR, domain_name)
    file_path = os.path.join(dir_path, file_name)

    with open(file_path, 'r') as f:
        file_content = f.read()

    kv_path = os.path.join('infra-lb/', {% if '-int-' in inventory_hostname %}'internal',{% else %}'external',{% endif %} 'sites',
                           domain_name, 'cert', file_name)
    consul_client.kv.put(kv_path, file_content)


def main():
    for conf_file in os.listdir(NGINX_DIR):
        if conf_file == 'default.conf' or conf_file.endswith('.sellerlogic.com.conf') or conf_file.endswith('.sellerlogic.dev.conf'):
            continue

        if not conf_file.endswith('.conf'):
            continue

        domain_name = conf_file[:-5]
        cert_path = os.path.join(LETSENCRYPT_DIR, domain_name, 'fullchain.pem')

        if not is_valid_cert(cert_path):
            conf_file_path = os.path.join(NGINX_DIR, conf_file)
            comment_https_block(conf_file_path)
            restart_nginx()
            time.sleep(5)
            run_certbot(domain_name)
            uncomment_https_block(conf_file_path)
            remove_certbot_comments(conf_file_path)
            time.sleep(10)

        for file_name in os.listdir(os.path.join(LETSENCRYPT_DIR, domain_name)):
            if not re.match(r'(cert|chain|fullchain|privkey)\.pem$', file_name):
                continue

            save_cert_to_consul(domain_name, file_name)

if __name__ == '__main__':
    main()
