log_level = "debug"

consul {
  address = "http://127.0.0.1:{{ consul_api_port }}"
  token   = "{{ consul_cluster_acl_token }}"
}

log_file {
  path = "/var/log/default/consul-template.log"
  log_rotate_bytes = 1024000
  log_rotate_duration = "3h"
  log_rotate_max_files = 10
}

# CA sellerlogic.com
template {
  source      = "{{ nginx_consul_template_keys_dir }}/sellerlogic.com.tpl"
  destination = "{{ nginx_ssl_dir }}/sellerlogic.com.key"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# CA sellerlogic.com
template {
  source      = "{{ nginx_consul_template_certs_dir }}/sellerlogic.com.tpl"
  destination = "{{ nginx_ssl_dir }}/sellerlogic.com.crtca"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# CA sellerlogic.dev
template {
  source      = "{{ nginx_consul_template_keys_dir }}/sellerlogic.dev.tpl"
  destination = "{{ nginx_ssl_dir }}/sellerlogic.dev.key"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# CA sellerlogic.dev
template {
  source      = "{{ nginx_consul_template_certs_dir }}/sellerlogic.dev.tpl"
  destination = "{{ nginx_ssl_dir }}/sellerlogic.dev.crtca"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# Nginx security headers dev
template {
  source      = "{{ nginx_consul_template_work_dir }}/proxy_security_headers_dev.tpl"
  destination = "{{ nginx_install_dir }}/proxy_security_headers_dev"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# Nginx security headers stage
template {
  source      = "{{ nginx_consul_template_work_dir }}/proxy_security_headers_stage.tpl"
  destination = "{{ nginx_install_dir }}/proxy_security_headers_stage"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# Nginx security headers rc
template {
  source      = "{{ nginx_consul_template_work_dir }}/proxy_security_headers_rc.tpl"
  destination = "{{ nginx_install_dir }}/proxy_security_headers_rc"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# Nginx security headers prod
template {
  source      = "{{ nginx_consul_template_work_dir }}/proxy_security_headers_prod.tpl"
  destination = "{{ nginx_install_dir }}/proxy_security_headers_prod"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# Nginx security headers infra
template {
  source      = "{{ nginx_consul_template_work_dir }}/proxy_security_headers_infra.tpl"
  destination = "{{ nginx_install_dir }}/proxy_security_headers_infra"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

# Nginx additional headers infra
template {
  source      = "{{ nginx_consul_template_work_dir }}/additional_headers.tpl"
  destination = "{{ nginx_install_dir }}/additional_headers"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}

{% for item in nginx_internal_enabled_sites %}
template {
  source      = "{{ nginx_consul_template_sites_dir }}/{{ item.name }}.tpl"
  destination = "{{ nginx_sites_dir }}/{{ item.name }}.conf"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}
{% endfor %}

{% for item in nginx_internal_enabled_sites %}
  {% if 'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name %}
template {
  source      = "{{ nginx_consul_template_keys_dir }}/{{ item.name }}.tpl"
  destination = "{{ nginx_ssl_dir }}/{{ item.name }}.key"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}
  {% endif %}
{% endfor %}

{% for item in nginx_internal_enabled_sites %}
  {% if 'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name %}
template {
  source      = "{{ nginx_consul_template_certs_dir }}/{{ item.name }}.tpl"
  destination = "{{ nginx_ssl_dir }}/{{ item.name }}.pem"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}
  {% endif %}
{% endfor %}

{% for item in nginx_default_internal_enabled_sites %}
template {
  source      = "{{ nginx_consul_template_sites_dir }}/{{ item.name }}.tpl"
  destination = "{{ nginx_sites_dir }}/{{ item.name }}.conf"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}
{% endfor %}

{% for item in nginx_default_internal_enabled_sites %}
  {% if 'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name %}
template {
  source      = "{{ nginx_consul_template_keys_dir }}/{{ item.name }}.tpl"
  destination = "{{ nginx_ssl_dir }}/{{ item.name }}.key"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}
  {% endif %}
{% endfor %}

{% for item in nginx_default_internal_enabled_sites %}
  {% if 'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name %}
template {
  source      = "{{ nginx_consul_template_certs_dir }}/{{ item.name }}.tpl"
  destination = "{{ nginx_ssl_dir }}/{{ item.name }}.pem"
  command = "systemctl reload nginx"
  command_timeout = "15s"
  error_on_missing_key = false
}
  {% endif %}
{% endfor %}
