server {
    server_name {{ item.name }};
    access_log /var/log/nginx/{{ item.name }}.access.log;
    {% if rewrite_url is defined %}
    rewrite ^/(.*)$ https://{{ rewrite_url }}/$1 permanent;
    {% else %}
    return 301 https://{{ item.name }}$request_uri;
    {% endif %}
    listen 80;
}

server {
    server_name {{ item.name }};
    {% if 'sellerlogic.com' in item.name %}
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.com.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.com.key";
    {% elif 'sellerlogic.dev' in item.name %}
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.dev.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.dev.key";
    {% else %}
    ssl_certificate "{{ nginx_ssl_dir }}/{{ item.name }}.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/{{ item.name }}.key";
    {% endif %}
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-{{ item.name }}.access.log;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    {% if rewrite_url is defined %}
    rewrite ^/(.*)$ https://{{ rewrite_url }}/$1 permanent;
    {% else %}
    location / {
            {% if item.proxy_pass is defined %}
            proxy_pass http://{{ item.proxy_pass }};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_http_version 1.1;
            {% endif %}
    }
    {% endif %}
    listen 443 ssl;
    http2 on;
}
