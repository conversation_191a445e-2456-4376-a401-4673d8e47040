server {
    server_name restapi-rc.sellerlogic.dev;
    access_log /var/log/nginx/restapi-rc.sellerlogic.dev.access.log;
    return 301 https://restapi-rc.sellerlogic.dev$request_uri;
    listen 80;
}

server {
    server_name restapi-rc.sellerlogic.dev;
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.dev.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.dev.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-restapi-rc.sellerlogic.dev.access.log;
    access_log /var/log/nginx/json_access_external.log json_analytics;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://*************;
            proxy_redirect http://************* /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
