server {
    server_name rc-ticketing-api.sellerlogic.dev;
    access_log /var/log/nginx/rc-ticketing-api.sellerlogic.dev.access.log;
    return 301 https://rc-ticketing-api.sellerlogic.dev$request_uri;
    listen 80;
}

server {
    server_name rc-ticketing-api.sellerlogic.dev;
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.dev.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.dev.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-rc-ticketing-api.sellerlogic.dev.access.log;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://***********;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
    }
    listen 443 ssl;
    http2 on;
}
