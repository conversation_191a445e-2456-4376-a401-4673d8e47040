server {
    server_name bas-api-rc.sellerlogic.com;
    access_log /var/log/nginx/bas-api-rc.sellerlogic.com.access.log;
    return 301 https://bas-api-rc.sellerlogic.com$request_uri;
    add_header X-Robots-Tag "noindex";
    listen 80;
}

server {
    server_name bas-api-rc.sellerlogic.com;
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.com.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.com.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-bas-api-rc.sellerlogic.com.access.log;
    access_log /var/log/nginx/json_access_external.log json_analytics;
    add_header X-Robots-Tag "noindex";
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://************;
            proxy_redirect http://************ /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
