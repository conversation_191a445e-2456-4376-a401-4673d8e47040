server {
    server_name client.sellerlogic.com www.client.sellerlogic.com;
    access_log /var/log/nginx/client.sellerlogic.com.access.log;
    return 301 https://$host:443$request_uri;
    add_header X-Robots-Tag "noindex";
    listen 80;
}

server {
    server_name client.sellerlogic.com www.client.sellerlogic.com;
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.com.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.com.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-client.sellerlogic.com.access.log;
    access_log /var/log/nginx/json_access_external.log json_analytics;
    add_header X-Robots-Tag "noindex";
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    return 301 https://app.sellerlogic.com$request_uri;
    listen 443 ssl;
    http2 on;
}
