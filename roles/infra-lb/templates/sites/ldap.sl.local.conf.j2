upstream ldap_hosts {

    server hz-prod-ldap-02.sl.local weight=5 max_fails=3 fail_timeout=10s;
    server hz-prod-ldap-01.sl.local backup;
    server hz-prod-ldap-03.sl.local backup;
}

server {
    server_name ldap.sl.local;
    access_log /var/log/nginx/ldap.sl.local.access.log;
    return 301 https://ldap.sl.local$request_uri;
    listen 80;
}
server {
    server_name ldap.sl.local;
    ssl_certificate "{{ nginx_ssl_dir }}/ldap.sl.local.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/ldap.sl.local.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    access_log /var/log/nginx/https-ldap.sl.local.access.log;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://ldap_hosts;
            proxy_redirect http://ldap_hosts /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
