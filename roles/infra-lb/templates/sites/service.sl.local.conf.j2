server {
    server_name service.sl.local;
    ssi on;
    access_log /var/log/nginx/service.sl.local.access.log;
    return 301 https://service.sl.local$request_uri;
    listen 80;
}

server {
    server_name service.sl.local;
    ssl_certificate "{{ nginx_ssl_dir }}/service.sl.local.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/service.sl.local.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-service.sl.local.access.log;
    access_log /var/log/nginx/json_access_external.log json_analytics;
    charset UTF-8;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://*************;
            proxy_redirect http://************* /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
