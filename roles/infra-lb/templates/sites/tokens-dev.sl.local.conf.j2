server {
    server_name tokens-dev.sl.local;
    access_log /var/log/nginx/tokens-dev.sl.local.access.log;
        return 301 https://tokens-dev.sl.local$request_uri;
        listen 80;
}

server {
    server_name tokens-dev.sl.local;
        ssl_certificate "/etc/nginx/ssl/tokens-dev.sl.local.pem";
    ssl_certificate_key "/etc/nginx/ssl/tokens-dev.sl.local.key";
        ssl_dhparam /etc/nginx/ssl/dhparam.pem;
    access_log /var/log/nginx/https-tokens-dev.sl.local.access.log;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    include /etc/nginx/proxy_security_headers_infra;
    include /etc/nginx/additional_headers;
        location / {
            proxy_pass http://************:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_http_version 1.1;
                }
        listen 443 ssl;
    http2 on;
}
