server {
    server_name app.sellerlogic.com;
    access_log /var/log/nginx/app.sellerlogic.com.access.log;
    return 301 https://app.sellerlogic.com$request_uri;
    add_header X-Robots-Tag "noindex";
    listen 80;
}

server {
    server_name app.sellerlogic.com;
    ssl_certificate "{{ nginx_ssl_dir }}/sellerlogic.com.crtca";
    ssl_certificate_key "{{ nginx_ssl_dir }}/sellerlogic.com.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-app.sellerlogic.com.access.log;
    access_log /var/log/nginx/json_access_external.log json_analytics;
	add_header Access-Control-Allow-Origin *;
    add_header X-Robots-Tag "noindex";
    add_header X-Frame-Options "DENY";
    add_header 'Access-Control-Allow-Origin' 'https://app.sellerlogic.com';
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    gzip on;
    	gzip_min_length 1000;
    	gzip_buffers 4 32k;
    	gzip_proxied any;
    	gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
    	gzip_vary on;
    	gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    location / {
            proxy_pass http://*************;
            proxy_redirect http://************* /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
