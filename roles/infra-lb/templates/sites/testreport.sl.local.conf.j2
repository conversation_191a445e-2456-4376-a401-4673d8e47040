server {
    server_name testreport.sl.local;
    access_log /var/log/nginx/testreport.sl.local.access.log;
    return 301 https://testreport.sl.local$request_uri;
    listen 80;
}

server {
    server_name testreport.sl.local;
    ssl_certificate "{{ nginx_ssl_dir }}/testreport.sl.local.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/testreport.sl.local.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-testreport.sl.local.access.log;
    access_log /var/log/nginx/json_access_external.log json_analytics;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://************:8080/core-automation-test/;
            proxy_set_header Host develop.pages.sellerlogic.dev;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Authorization "";
            auth_basic "Unauthorized";
            auth_basic_user_file /etc/nginx/.htpasswd_testreport;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
