server {
    server_name auth.sl.local;
    access_log /var/log/nginx/auth.sl.local.access.log;
    return 301 https://auth.sl.local$request_uri;
    listen 80;
    add_header X-Robots-Tag "noindex";
}

server {
    server_name auth.sl.local;
	ssl_stapling off;
    ssl_certificate "{{ nginx_ssl_dir }}/auth.sl.local.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/auth.sl.local.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-auth.sl.local.access.log;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://*************;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
    add_header X-Robots-Tag "noindex";
}
