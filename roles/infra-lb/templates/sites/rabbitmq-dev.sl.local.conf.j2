upstream rabbitmqdev_hosts {

    hash $remote_addr$remote_port consistent;

        server hz-dev-rabbitmq-01.sl.local:15672;
        server hz-dev-rabbitmq-02.sl.local:15672;
        server hz-dev-rabbitmq-03.sl.local:15672;
}

server {
    server_name rabbitmq-dev.sl.local;
    access_log /var/log/nginx/rabbitmq-dev.sl.local.access.log;
    return 301 https://rabbitmq-dev.sl.local$request_uri;
    listen 80;
}
server {
    server_name rabbitmq-dev.sl.local;
    ssl_certificate "{{ nginx_ssl_dir }}/rabbitmq-dev.sl.local.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/rabbitmq-dev.sl.local.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    access_log /var/log/nginx/https-rabbitmq-dev.sl.local.access.log;
    include {{ nginx_install_dir }}/proxy_security_headers_{{ item.env }};
    include {{ nginx_install_dir }}/additional_headers;
    location / {
            proxy_pass http://rabbitmqdev_hosts;
            proxy_redirect http://rabbitmqdev_hosts /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_http_version 1.1;
    }
    listen 443 ssl;
    http2 on;
}
