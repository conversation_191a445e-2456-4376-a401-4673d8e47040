import requests

CONSUL_API_URL = "{{ consul_api_url }}"

# Get all sites from Ansible variables
all_sites = [
{% for site in nginx_external_enabled_sites + nginx_internal_enabled_sites + nginx_default_external_enabled_sites + nginx_default_internal_enabled_sites %}
    {'name': '{{ site.name }}', 'env': '{{ site.env }}'},
{% endfor %}
]

# Function to get registered services
def get_registered_services():
    response = requests.get(f"{CONSUL_API_URL}/v1/agent/services", verify=False)
    services = response.json()
    return services

# Function to register or update service in Consul
def register_service_in_consul(service_address, environment):
    service_id = f"blackbox-exporter-{service_address}"
    payload = {
        "ID": service_id,
        "Name": "blackbox_exporter_lb",
        "Address": service_address,
        "Port": 443,
        "Check": {
            "HTTP": f"https://{service_address}",
            "Interval": "60s",
            "TLSSkipVerify": True
        },
        "Tags": [f"_environment={environment}", "exporter_service=blackbox_exporter_lb", "_location=lb"]
    }
    response = requests.put(f"{CONSUL_API_URL}/v1/agent/service/register", json=payload, verify=False)
    return response.status_code == 200

# Main function
def main():
    registered_services = get_registered_services()

    # Register or update services based on Ansible variables
    for site in all_sites:
        server_name = site['name']
        environment = site['env']
        service_id = f"blackbox-exporter-{server_name}"
        current_service = registered_services.get(service_id)
        
        if current_service:
            # Check for changes
            current_tags = current_service.get('Tags', [])
            new_tags = [f"_environment={environment}", "exporter_service=blackbox_exporter_lb"]
            
            if current_service['Address'] != server_name or current_service['Port'] != 443 or set(current_tags) != set(new_tags):
                if register_service_in_consul(server_name, environment):
                    print(f"Service for {server_name} updated.")
                else:
                    print(f"Failed to update service for {server_name}.")
            else:
                print(f"Service for {server_name} is already up-to-date.")
        else:
            if register_service_in_consul(server_name, environment):
                print(f"Service for {server_name} registered successfully.")
            else:
                print(f"Failed to register service for {server_name}.")

    # Remove services not in Ansible variables
    kv_service_ids = {f"blackbox-exporter-{site['name']}" for site in all_sites}
    for service_id in registered_services:
        if service_id.startswith("blackbox-exporter-") and service_id not in kv_service_ids:
            response = requests.put(f"{CONSUL_API_URL}/v1/agent/service/deregister/{service_id}", verify=False)
            if response.status_code == 200:
                print(f"Service {service_id} deregistered.")
            else:
                print(f"Failed to deregister service {service_id}.")

if __name__ == "__main__":
    main()
