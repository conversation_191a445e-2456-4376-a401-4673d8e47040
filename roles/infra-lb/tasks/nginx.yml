---
- name: <PERSON>dir {{ nginx_geoip_module_dir }}
  ansible.builtin.file:
    path: "{{ nginx_geoip_module_dir }}"
    state: directory
    mode: "0755"

- name: Download GeoIP2 module
  ansible.builtin.get_url:
    url: "{{ nginx_geoip_module_url }}"
    dest: /tmp/ngx_http_geoip2_module.zip
    mode: "0644"

- name: Unzip GeoIP2 module
  ansible.builtin.unarchive:
    src: /tmp/ngx_http_geoip2_module.zip
    dest: "{{ nginx_geoip_module_dir }}"
    remote_src: true
  when: not ansible_check_mode

- name: Download GeoIP2 database
  ansible.builtin.get_url:
    url: "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key={{ nginx_maxmind_license_key }}&suffix=tar.gz"
    dest: /tmp/GeoLite2-Country.tar.gz
    mode: "0644"

- name: Extract GeoIP2 database
  ansible.builtin.unarchive:
    src: /tmp/GeoLite2-Country.tar.gz
    dest: /tmp
    remote_src: true
    mode: "0644"
    list_files: true
  register: archive_content
  when: not ansible_check_mode

- name: Get folder name
  ansible.builtin.set_fact:
    geoip_folder_name: "{{ (archive_content.files | first) | dirname }}"
  when: not ansible_check_mode

- name: Copy folder contents to remote directory
  ansible.builtin.copy:
    src: "/tmp/{{ geoip_folder_name }}/"
    dest: /usr/share/GeoIP/
    remote_src: true
    mode: "0644"
  when: not ansible_check_mode

- name: Add Nginx repo key
  ansible.builtin.apt_key:
    url: http://nginx.org/keys/nginx_signing.key
    state: present

- name: Add repository
  ansible.builtin.apt_repository:
    repo: "{{ item }}"
    state: present
    update_cache: true
  with_items:
    - "deb https://nginx.org/packages/ubuntu/ {{ ansible_distribution_release }} nginx"
    - "deb-src https://nginx.org/packages/ubuntu/ {{ ansible_distribution_release }} nginx"
    - "ppa:maxmind/ppa"

- name: Install os pkg
  ansible.builtin.package:
    name: "{{ nginx_pkgs }}"
    state: present

- name: Install python pkg
  ansible.builtin.pip:
    name: "{{ nginx_python_pkgs }}"
    extra_args: "--break-system-packages"

- name: Download Nginx repo
  ansible.builtin.get_url:
    url: http://nginx.org/download/nginx-{{ nginx_ver }}.tar.gz
    dest: /tmp/nginx.tar.gz
    mode: "0644"

- name: Unzip Nginx
  ansible.builtin.unarchive:
    src: /tmp/nginx.tar.gz
    dest: "/tmp"
    remote_src: true
  when: not ansible_check_mode

- name: Configure geoip2 module
  ansible.builtin.shell: |
    ./configure \
      --with-compat \
      --add-dynamic-module={{ nginx_geoip_module_dir }}/ngx_http_geoip2_module-master
  args:
    chdir: "/tmp/nginx-{{ nginx_ver }}"
  changed_when: false
  when: not ansible_check_mode

- name: Make geoip2 module
  ansible.builtin.command: make modules
  args:
    chdir: "/tmp/nginx-{{ nginx_ver }}"
  changed_when: false
  when: not ansible_check_mode

- name: Copy geoip2 module
  ansible.builtin.copy:
    src: "/tmp/nginx-{{ nginx_ver }}/objs/ngx_http_geoip2_module.so"
    dest: /etc/nginx/modules/
    remote_src: true
    mode: "0755"
  when: not ansible_check_mode

- name: Cleanup
  ansible.builtin.file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/tmp/nginx.tar.gz"
    - "/tmp/GeoLite2-Country.tar.gz"
    - "/tmp/nginx-{{ nginx_ver }}"
    - "/tmp/ngx_http_geoip2_module.zip"
    - "/etc/nginx/conf.d/default.conf"
    - "/etc/cron.d/certbot"

- name: Mkdir {{ nginx_sites_dir }}
  ansible.builtin.file:
    path: "{{ nginx_sites_dir }}"
    state: directory
    mode: "0755"

- name: Mkdir {{ nginx_req_dir }}
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: 0755
    owner: www-data
    group: www-data
  with_items: "{{ nginx_req_dir }}"

- name: Copy nginx conf
  ansible.builtin.template:
    src: nginx/nginx.conf.j2
    dest: "/etc/nginx/nginx.conf"
    mode: "0644"
  notify: Restart nginx

- name: Copy default.conf
  ansible.builtin.template:
    src: sites/default.conf.j2
    dest: "{{ nginx_sites_dir }}/default.conf"
    mode: "0644"
  notify: Reload nginx

- name: Copy script cert_renew.py and certbot-dns-ipa.py
  ansible.builtin.template:
    src: '{{ item }}.j2'
    dest: '/etc/nginx/{{ item }}'
    mode: a+x
  with_items:
    - cert_renew.py
    - certbot-dns-ipa.py

- name: Added crontab jobs
  ansible.builtin.cron:
    name: "{{ item.name }}"
    state: present
    disabled: false
    cron_file: "{{ item.cron_file }}"
    backup: true
    minute: "{{ item.minute }}"
    hour: "{{ item.hour }}"
    day: "{{ item.day }}"
    month: "{{ item.month }}"
    weekday: "{{ item.weekday }}"
    user: "{{ item.user }}"
    job: "{{ item.job }}"
  loop: "{{ nginx_certbot_cronjob }}"

- name: Mkdir {{ nginx_ssl_dir }}
  ansible.builtin.file:
    path: "{{ nginx_ssl_dir }}"
    state: directory
    mode: "0755"

- name: Generate Diffie-Hellman parameters with the default size (4096 bits)
  community.crypto.openssl_dhparam:
    path: "{{ nginx_ssl_dir }}/dhparam.pem"
