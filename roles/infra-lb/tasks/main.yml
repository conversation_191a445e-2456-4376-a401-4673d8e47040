---
# tasks file for roles/infra-lb
- name: Install Nginx
  ansible.builtin.import_tasks: nginx.yml
  tags: nginx

- name: Install Keepalived
  ansible.builtin.import_tasks: keepalived.yml

- name: UFW
  ansible.builtin.import_tasks: ufw.yml
  tags: ufw

- name: Consul-template
  ansible.builtin.import_tasks: consul-template.yml
  tags: consul

- name: Configure and setup default Sites templates
  ansible.builtin.import_tasks: default-sites-configure.yml
  tags: default-sites

- name: Configure and setup custom Sites templates
  ansible.builtin.import_tasks: custom-sites-configure.yml
  tags: custom-sites

- name: Consul Sites to blackbox
  ansible.builtin.import_tasks: consul-blackbox.yml
  tags: consul-blackbox
