---
- name: Set site type based on hostname
  ansible.builtin.set_fact:
    site_type: "{% if '-int-' in inventory_hostname %}internal{% elif '-ext-' in inventory_hostname %}external{% endif %}"

- name: Add config to consul
  community.general.consul_kv:
    key: "infra-lb/{{ site_type }}/sites/{{ item.name }}/config"
    value: "{{ lookup('template', 'sites/' + item.name + '.conf.j2') }}"
    token: "{{ consul_cluster_acl_token }}"
    host: "{{ consul_api_host }}"
    port: "{{ consul_api_port }}"
  with_items: "{{ nginx_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  delegate_to: "{{ groups[cluster_name][0] }}"

- name: Configure consul-template config file
  ansible.builtin.template:
    backup: true
    src: consul/consul-template-{{ site_type }}.hcl.j2
    dest: "{{ nginx_consul_template_conf_dir }}/consul-template.hcl"
    mode: 0644
  with_items: "{{ nginx_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  notify: Restart consul-template

- name: Configure sites config file
  ansible.builtin.template:
    src: sites/sites_conf.tpl.j2
    dest: "{{ nginx_consul_template_sites_dir }}/{{ item.name }}.tpl"
    mode: 0644
  with_items: "{{ nginx_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  notify: Restart consul-template

- name: Configure certs config file
  ansible.builtin.template:
    src: consul/certs_conf.tpl.j2
    dest: "{{ nginx_consul_template_certs_dir }}/{{ item.name }}.tpl"
    mode: 0644
  with_items: "{{ nginx_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  notify: Restart consul-template
  when: "'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name"

- name: Configure keys config file
  ansible.builtin.template:
    src: consul/keys_conf.tpl.j2
    dest: "{{ nginx_consul_template_keys_dir }}/{{ item.name }}.tpl"
    mode: 0644
  with_items: "{{ nginx_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  notify: Restart consul-template
  when: "'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name"

- name: Configure CA certs config files for sellerlogic domains
  ansible.builtin.template:
    src: "consul/certs_conf_{{ item }}.tpl.j2"
    dest: "{{ nginx_consul_template_certs_dir }}/{{ item }}.tpl"
    mode: 0644
  with_items:
    - sellerlogic.com
    - sellerlogic.dev
  notify: Restart consul-template

- name: Configure CA keys config files for sellerlogic domains
  ansible.builtin.template:
    src: "consul/keys_conf_{{ item }}.tpl.j2"
    dest: "{{ nginx_consul_template_keys_dir }}/{{ item }}.tpl"
    mode: 0644
  with_items:
    - sellerlogic.com
    - sellerlogic.dev
  notify: Restart consul-template

- name: Starting service consul Template
  ansible.builtin.systemd:
    name: consul-template
    state: restarted
    enabled: true
    daemon_reload: true

- name: Run cert gen script
  ansible.builtin.command: "{{ nginx_certbot_script_command }}"
  delegate_to: "{{ groups[cluster_name][0] }}"
  run_once: true
  notify: Restart consul-template
  changed_when: false
  failed_when: false
