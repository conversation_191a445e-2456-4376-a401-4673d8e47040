---
- name: Set def site type based on hostname
  ansible.builtin.set_fact:
    site_def_type: "{% if '-int-' in inventory_hostname %}internal{% elif '-ext-' in inventory_hostname %}external{% endif %}"

- name: Add def config to consul
  community.general.consul_kv:
    key: "infra-lb/{{ site_def_type }}/sites/{{ item.name }}/config"
    value: "{{ lookup('template', 'sites/' + 'default_site' + '.j2') }}"
    token: "{{ consul_cluster_acl_token }}"
    host: "{{ consul_api_host }}"
    port: "{{ consul_api_port }}"
  with_items: "{{ nginx_default_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_default_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  delegate_to: "{{ groups[cluster_name][0] }}"

- name: Configure def consul-template config file
  ansible.builtin.template:
    backup: true
    src: consul/consul-template-{{ site_def_type }}.hcl.j2
    dest: "{{ nginx_consul_template_conf_dir }}/consul-template.hcl"
    mode: 0644
  with_items: "{{ nginx_default_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_default_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  notify: Restart consul-template

- name: Configure def sites config file
  ansible.builtin.template:
    src: sites/sites_conf.tpl.j2
    dest: "{{ nginx_consul_template_sites_dir }}/{{ item.name }}.tpl"
    mode: 0644
  with_items: "{{ nginx_default_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_default_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  notify: Restart consul-template

- name: Add def proxy-security-headers config file to consul for {{ site_def_type }}
  community.general.consul_kv:
    key: "infra-lb/{{ site_def_type }}/global-config/headers/proxy_security_headers_{{ item }}"
    value: "{{ lookup('template', 'nginx/' + 'proxy_security_headers_' + item + '.j2') }}"
    token: "{{ consul_cluster_acl_token }}"
    host: "{{ consul_api_host }}"
    port: "{{ consul_api_port }}"
  delegate_to: "{{ groups[cluster_name][0] }}"
  with_items:
    - infra
    - dev
    - stage
    - rc
    - prod

- name: Configure def proxy-security-headers config file
  ansible.builtin.template:
    src: "nginx/proxy_security_headers_{{ item }}.tpl.j2"
    dest: "{{ nginx_consul_template_work_dir }}/proxy_security_headers_{{ item }}.tpl"
    mode: 0644
  notify: Restart consul-template
  with_items:
    - infra
    - dev
    - stage
    - rc
    - prod

- name: Add def additional-headers config file to consul for {{ site_def_type }}
  community.general.consul_kv:
    key: "infra-lb/{{ site_def_type }}/global-config/headers/additional_headers"
    value: "{{ lookup('template', 'nginx/' + 'additional_headers.j2') }}"
    token: "{{ consul_cluster_acl_token }}"
    host: "{{ consul_api_host }}"
    port: "{{ consul_api_port }}"
  delegate_to: "{{ groups[cluster_name][0] }}"

- name: Configure def additional-headers config file
  ansible.builtin.template:
    src: "nginx/additional_headers.tpl.j2"
    dest: "{{ nginx_consul_template_work_dir }}/additional_headers.tpl"
    mode: 0644
  notify: Restart consul-template

- name: Configure def certs config file
  ansible.builtin.template:
    src: consul/certs_conf.tpl.j2
    dest: "{{ nginx_consul_template_certs_dir }}/{{ item.name }}.tpl"
    mode: 0644
  with_items: "{{ nginx_default_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_default_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  when: "'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name"
  notify: Restart consul-template

- name: Configure def keys config file for int
  ansible.builtin.template:
    src: consul/keys_conf.tpl.j2
    dest: "{{ nginx_consul_template_keys_dir }}/{{ item.name }}.tpl"
    mode: 0644
  with_items: "{{ nginx_default_internal_enabled_sites
                  if '-int-' in inventory_hostname
                  else nginx_default_external_enabled_sites
                  if '-ext-' in inventory_hostname
                  else [] }}"
  when: "'sellerlogic.com' not in item.name and 'sellerlogic.dev' not in item.name"
  notify: Restart consul-template

- name: Configure CA certs config files for sellerlogic domains
  ansible.builtin.template:
    src: "consul/certs_conf_{{ item }}.tpl.j2"
    dest: "{{ nginx_consul_template_certs_dir }}/{{ item }}.tpl"
    mode: 0644
  with_items:
    - sellerlogic.com
    - sellerlogic.dev
  notify: Restart consul-template

- name: Configure CA keys config files for sellerlogic domains
  ansible.builtin.template:
    src: "consul/keys_conf_{{ item }}.tpl.j2"
    dest: "{{ nginx_consul_template_keys_dir }}/{{ item }}.tpl"
    mode: 0644
  with_items:
    - sellerlogic.com
    - sellerlogic.dev
  notify: Restart consul-template

- name: Starting service consul Template
  ansible.builtin.systemd:
    name: consul-template
    state: restarted
    enabled: true
    daemon_reload: true

- name: Run cert gen script
  ansible.builtin.command: "{{ nginx_certbot_script_command }}"
  delegate_to: "{{ groups[cluster_name][0] }}"
  run_once: true
  notify: Restart consul-template
  changed_when: false
  failed_when: false
