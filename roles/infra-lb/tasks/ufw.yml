---
- name: UF<PERSON> tuning for OS
  community.general.ufw:
    port: "{{ nginx_ufw_port_os }}"
    to_ip: "{{ hostvars[inventory_hostname]['ansible_host'] }}"
    proto: tcp
    rule: allow
    state: enabled

- name: UFW tuning for int vip ip
  community.general.ufw:
    port: "{{ nginx_ufw_port_vip }}"
    to_ip: "{{ item }}"
    proto: tcp
    rule: allow
    state: enabled
  with_items:
    - "{{ nginx_internal_vip }}"
  when: nginx_internal_vip is defined

- name: UFW tuning for ext vip ip
  community.general.ufw:
    port: "{{ nginx_ufw_port_vip }}"
    to_ip: "{{ item }}"
    proto: tcp
    rule: allow
    state: enabled
  with_items:
    - "{{ nginx_keepalived_external_vip }}"
  when: nginx_keepalived_external_vip is defined

- name: UFW service enabled and restarted
  ansible.builtin.systemd:
    enabled: true
    name: ufw
    state: restarted
