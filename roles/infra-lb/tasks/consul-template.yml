---
- name: Download Consul Template binary
  ansible.builtin.get_url:
    url: "{{ nginx_consul_template_url }}"
    dest: "/tmp/consul-template.zip"
    mode: 0644

- name: Unzip Consul Template binary
  ansible.builtin.unarchive:
    src: "/tmp/consul-template.zip"
    dest: "{{ nginx_consul_template_bin_path | dirname }}"
    remote_src: true
  when: not ansible_check_mode

- name: Make Consul Template executable
  ansible.builtin.file:
    path: "{{ nginx_consul_template_bin_path }}"
    mode: "a+x"

- name: Create Consul Template config directory
  ansible.builtin.file:
    path: "{{ nginx_consul_template_conf_dir }}"
    state: directory
    mode: 0755

- name: Create req directory
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: 0755
  with_items: "{{ nginx_consul_req_dir }}"

- name: Configure consul-template service
  ansible.builtin.template:
    src: consul/consul-template.service.j2
    dest: /etc/systemd/system/consul-template.service
    mode: 0644
