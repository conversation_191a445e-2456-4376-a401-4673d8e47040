---
- name: Place consul-sites-to-blackbox.py
  ansible.builtin.template:
    src: consul-sites-to-blackbox.py.j2
    dest: "{{ nginx_install_dir }}/consul-sites-to-blackbox.py"
    owner: root
    group: root
    mode: 0755
  tags: consul-blackbox

- name: Run consul-sites-to-blackbox.py
  ansible.builtin.command: "python3 {{ nginx_install_dir }}/consul-sites-to-blackbox.py"
  changed_when: true
  run_once: true
  tags: consul-blackbox
