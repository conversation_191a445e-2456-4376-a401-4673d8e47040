infra-lb role
=========

Sites variables example
------------

```yaml
nginx_external_enabled_sites:
   - name: infra-mon.sellerlogic.dev
   - name: infra-graf.sellerlogic.dev

nginx_internal_enabled_sites:
   - name: infra-mon.sl.local
   - name: infra-graf.sl.local
```

Example ext site config
------------

```conf
server {
    server_name infra-graf.sellerlogic.dev;
    access_log /var/log/nginx/infra-graf.sellerlogic.dev.access.log;
    return 301 https://infra-graf.sellerlogic.dev$request_uri;
    listen 80;
}

server {
    server_name infra-graf.sellerlogic.dev;
    ssl_certificate "{{ nginx_ssl_dir }}/infra-graf.sellerlogic.dev.pem";
    ssl_certificate_key "{{ nginx_ssl_dir }}/infra-graf.sellerlogic.dev.key";
    ssl_dhparam {{ nginx_ssl_dir }}/dhparam.pem;
    access_log /var/log/nginx/https-infra-graf.sellerlogic.dev.access.log;
    access_log /var/log/nginx/json_access_internal.log json_analytics;
    location / {
            proxy_pass http://*************;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
    }
    listen 443 ssl;
}
```
