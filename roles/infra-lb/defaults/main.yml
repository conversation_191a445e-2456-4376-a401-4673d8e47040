---
# defaults file for roles/infra-lb
nginx_ver: "1.26.2"
nginx_install_dir: /etc/nginx
nginx_timeout_seconds: 600
nginx_log_dir: /var/log/nginx
nginx_sites_dir: /etc/nginx/sites-enabled
nginx_ssl_dir: /etc/nginx/ssl

nginx_req_dir:
  - /var/log/nginx
  - /var/www/html/.well-known/acme-challenge

nginx_pkgs:
  - wget
  - libreadline-dev
  - libncurses5-dev
  - libpcre3-dev
  - libssl-dev
  - libldap2-dev
  - zlib1g-dev
  - perl
  - make
  - build-essential
  - libmaxminddb0
  - libmaxminddb-dev
  - libgeoip-dev
  - build-essential
  - gcc
  - unzip
  - nginx={{ nginx_ver }}-1~{{ ansible_distribution_release }}
  - nginx-module-geoip={{ nginx_ver }}-2~{{ ansible_distribution_release }}
  - nginx-module-image-filter={{ nginx_ver }}-2~{{ ansible_distribution_release }}
  - nginx-module-njs={{ nginx_ver }}+0.8.5-1~{{ ansible_distribution_release }}
  - nginx-module-perl={{ nginx_ver }}-2~{{ ansible_distribution_release }}
  - nginx-module-xslt={{ nginx_ver }}-2~{{ ansible_distribution_release }}
  - mmdb-bin
  - certbot
  - python3-certbot-nginx

nginx_python_pkgs:
  - python-consul
  - pyOpenSSL
  - cryptography

# Conf
nginx_worker_rlimit_nofile: 64000
nginx_worker_connections: 130000


# Keepalived
nginx_keepalived_vip:
nginx_keepalived_etc_dir: /etc/keepalived
nginx_keepalived_auth_pass: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  30636463336231323735343266333134633333326135323162656561326635336336323239636634
  3230393663316233623437666331313936613135666664660a306634346461376436633035383736
  61306236303261303066656465666265613835363633343765326330326234333631396563666135
  3831373934343961360a313862373433386333666665666134653031383434656563633238323866
  39366364323761613031343963366538353661633438396332306166613937633036

# GeoIP2
nginx_geoip_module_dir: /usr/src/ngx_http_geoip2_module
nginx_geoip_module_url: https://github.com/leev/ngx_http_geoip2_module/archive/master.zip
nginx_maxmind_license_key: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  65643036323239303263653266326238303834393739386530636132393932623130373733643631
  3132346233326137306431376162656437353731303862370a353838626165646361663335623663
  37373238396566383761663932643565633730383562383064323364616136303561616431306138
  3036346565613438330a396264313436303464363763353165613632356565633236333066636636
  33633633636262396463633736356664303932386630366431396336363334626162

# UFW
nginx_ufw_port_os: 22,2202,9100,10050,9102,7301,7500,7600,111,9104
nginx_ufw_port_vip: 80,443

# Consul
nginx_consul_template_version: "0.30.0"
nginx_consul_template_url:
  "https://releases.hashicorp.com/consul-template/{{ nginx_consul_template_version }}/\
  consul-template_{{ nginx_consul_template_version }}_linux_amd64.zip"

nginx_consul_template_bin_path: /usr/local/bin/consul-template
nginx_consul_template_conf_dir: /etc/consul-template
nginx_consul_template_work_dir: "{{ nginx_consul_template_conf_dir }}/templates"
nginx_consul_template_sites_dir: "{{ nginx_consul_template_conf_dir }}/sites"
nginx_consul_template_keys_dir: "{{ nginx_consul_template_conf_dir }}/keys"
nginx_consul_template_certs_dir: "{{ nginx_consul_template_conf_dir }}/certs"
nginx_consul_kv_store: infra-lb
nginx_consul_req_dir:
  - "{{ nginx_consul_template_work_dir }}"
  - "{{ nginx_consul_template_sites_dir }}"
  - "{{ nginx_consul_template_keys_dir }}"
  - "{{ nginx_consul_template_certs_dir }}"

# Certbot
nginx_certbot_email: <EMAIL>
nginx_certbot_webroot_path: /var/www/html/
nginx_certbot_script_command: "/usr/bin/python3 /etc/nginx/cert_renew.py >> /var/log/default/cert_renew.log 2>&1"
nginx_certbot_cronjob:
  - name: cert renew and push to consul kv
    cron_file: cert-renew
    minute: "0"
    hour: "*/12"
    day: "*"
    month: "*"
    weekday: "*"
    user: root
    job: "{{ nginx_certbot_script_command }}"

# Sites
nginx_external_enabled_sites:
  - name: app-rc.sellerlogic.dev
    env: rc
  - name: auth-rc.sellerlogic.dev
    env: rc
  - name: restapi-rc.sellerlogic.dev
    env: rc
  - name: ts-rc.sellerlogic.dev
    env: rc
  - name: api.sellerlogic.com
    env: prod
  - name: app.sellerlogic.com
    env: prod
  - name: auth.sellerlogic.com
    env: prod
  - name: restapi.sellerlogic.com
    env: prod
  - name: bas-api-rc.sellerlogic.com
    env: rc
  - name: bas-api.sellerlogic.com
    env: prod
  - name: client.sellerlogic.com
    env: prod
  - name: oncall.sellerlogic.com
    env: infra
  - name: rc-ticketing-api.sellerlogic.dev
    env: rc
  - name: sd-api.sellerlogic.com
    env: prod

nginx_internal_enabled_sites:
  - name: auth.sl.local
    env: prod
  - name: auth-staging.sl.local
    env: stage
  - name: consul.sl.local
    env: infra
  - name: ldap.sl.local
    env: infra
  - name: rabbitmq-dev.sl.local
    env: infra
  - name: rabbitmq-rc.sl.local
    env: infra
  - name: rabbitmq-stage.sl.local
    env: infra
  - name: serviceapi.sl.local
    env: prod
  - name: serviceapi-rc.sl.local
    env: rc
  - name: serviceapi-dev.sl.local
    env: dev
  - name: testreport.sl.local
    env: dev
  - name: react.sl.local
    env: dev
  - name: app-dev.sl.local
    env: dev
  - name: restapi-staging.sl.local
    env: stage
  - name: service-dev.sl.local
    env: dev
  - name: auth-admin-staging.sl.local
    env: stage
  - name: auth-admin-rc.sellerlogic.dev
    env: rc
  - name: auth-admin.sellerlogic.com
    env: prod
  - name: auth-staging.sellerlogic.dev
    env: stage
  - name: auth-admin-staging.sellerlogic.dev
    env: stage
  - name: bas-api-staging.sl.local
    env: stage
  - name: service.sl.local
    env: prod
  - name: tokens-dev.sl.local
    env: dev
  - name: tokens.sl.local
    env: prod

nginx_default_external_enabled_sites:
  - name: avanto-gmbh.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: donzo.co.uk
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: donzo.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: gps-haus.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: intada.com
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: intada.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: konkurse-online.com
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: online-geiz.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: salezone.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: sellerlogic.co.uk
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: sellerlogic.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: sellerlogic.es
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: sellerlogic.fr
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: sellerlogic.it
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: www.sellerlogic.co.uk
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: www.sellerlogic.de
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: www.sellerlogic.es
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: www.sellerlogic.fr
    rewrite_url: www.sellerlogic.com
    env: prod
  - name: www.sellerlogic.it
    rewrite_url: www.sellerlogic.com
    env: prod


nginx_default_internal_enabled_sites:
  - name: auth-dev.sl.local
    proxy_pass: 192.168.2.142
    env: dev
  - name: auth-admin-dev.sl.local
    proxy_pass: 192.168.2.142
    env: dev
  - name: auth-rc.sl.local
    proxy_pass: 192.168.2.212
    env: rc
  - name: bareos.sl.local
    proxy_pass: 192.168.3.156:8080
    env: infra
  - name: dev-crm-api.sl.local
    proxy_pass: 192.168.2.149
    env: dev
  - name: staging-crm-api.sl.local
    proxy_pass: 192.168.2.219
    env: stage
  - name: staging-crm.sl.local
    proxy_pass: 192.168.3.22
    env: stage
  - name: storybook-dev.sl.local
    proxy_pass: 192.168.2.126
    env: dev
  - name: userapi-rc.sl.local
    proxy_pass: 192.168.2.210
    env: rc
  - name: userapi-staging.sl.local
    proxy_pass: 192.168.2.211
    env: stage
  - name: monitoring.sl.local
    proxy_pass: 192.168.3.118
    env: infra
  - name: restapi-dev.sl.local
    proxy_pass: 192.168.2.142
    env: dev
