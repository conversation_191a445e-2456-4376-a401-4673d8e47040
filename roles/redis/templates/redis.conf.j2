# {{ ansible_managed }}

daemonize yes
pidfile /run/redis/redis-server.pid
port {{ redis_port }}
bind {{ redis_bind_interface }}

tcp-backlog 4096
tcp-keepalive 300

{% if redis_unixsocket %}
unixsocket {{ redis_unixsocket }}
{% endif %}

timeout {{ redis_timeout }}

loglevel {{ redis_loglevel }}
logfile {{ redis_logfile }}

syslog-enabled yes
syslog-ident redis
syslog-facility local0

databases {{ redis_databases }}

{% for save in redis_save %}
save {{ save }}
{% endfor %}

rdbcompression {{ redis_rdbcompression }}

{% if redis_dbfile == true %}
dbfilename {{ redis_dbfilename }}
{% else %}
dbfilename ""
{% endif %}

dir {{ redis_dbdir }}

maxclients 65000

{% if redis_maxmemory %}
maxmemory {{ redis_maxmemory }}
maxmemory-policy {{ redis_maxmemory_policy }}
maxmemory-samples {{ redis_maxmemory_samples }}
{% endif %}

appendonly {{ redis_appendonly }}
appendfsync {{ redis_appendfsync }}
no-appendfsync-on-rewrite no

{% for include in redis_includes %}
include {{ include }}
{% endfor %}

{% if redis_requirepass %}
requirepass {{ redis_requirepass }}
{% endif %}

{% if redis_disabled_commands %}
{% for redis_disabled_command in redis_disabled_commands %}
rename-command {{ redis_disabled_command }} ""
{% endfor %}
{% endif %}

protected-mode no

{% if redis_users is defined %}
# User definitions
{% for user in redis_users %}
user {{ user.name }} on {{ user.password }} {{ user.acl }}
{% endfor %}
{% endif %}
