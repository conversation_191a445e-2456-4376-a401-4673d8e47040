---
redis_sysctl_conf:
  vm.overcommit_memory: 1

redis_version: 7.0.15
redis_download_url: "https://github.com/redis/redis/archive/{{ redis_version }}.tar.gz"
redis_download_path: /tmp/redis-{{ redis_version }}.tar.gz
redis_tmp_install_path: /tmp

redis_user: redis
redis_group: redis

redis_conf_path: /etc/redis/redis.conf
redis_conf_mode: 0640

redis_port: 6379
redis_bind_interface: 0.0.0.0
redis_unixsocket: ""
redis_timeout: 0

redis_loglevel: "notice"
redis_log_dir: /var/log/redis
redis_logfile: "{{ redis_log_dir }}/redis-server.log"

redis_databases: 8

redis_save: []

redis_rdbcompression: "yes"
redis_dbfile: true
redis_dbfilename: dump.rdb
redis_dbdir: /var/lib/redis

redis_maxmemory: "{{ (ansible_memtotal_mb * 80 / 100) | int | abs }}mb"
redis_maxmemory_policy: "volatile-ttl"
redis_maxmemory_samples: 5

redis_appendonly: "no"
redis_appendfsync: "everysec"

# Add extra include files for local configuration/overrides.
redis_includes: []

# Require authentication to Redis with a password.
redis_requirepass: ""

# Disable certain Redis commands for security reasons.
redis_disabled_commands: []
#  - FLUSHDB
#  - FLUSHALL
#  - KEYS
#  - PEXPIRE
#  - DEL
#  - CONFIG
#  - SHUTDOWN
#  - BGREWRITEAOF
#  - BGSAVE
#  - SAVE
#  - SPOP
#  - SREM
#  - RENAME
#  - DEBUG
