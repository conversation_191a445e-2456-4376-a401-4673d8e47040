---
- name: Pre configure sysctl
  ansible.posix.sysctl:
    name: "{{ item.key }}"
    value: "{{ item.value }}"
    state: present
    reload: true
  with_dict: "{{ redis_sysctl_conf }}"

- name: Install sysfsutils for disabling transparent huge pages
  ansible.builtin.apt:
    name: sysfsutils
    state: latest

- name: Disable transparent huge pages for performance
  ansible.builtin.lineinfile:
    path: /etc/sysfs.conf
    line: |
      kernel/mm/transparent_hugepage/enabled = never

- name: Create redis group
  ansible.builtin.group:
    name: "{{ redis_group }}"
    system: true
    state: present

- name: Create redis user
  ansible.builtin.user:
    name: "{{ redis_user }}"
    group: "{{ redis_group }}"
    system: true
    state: present

- name: Ensure Redis dir
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: 0755
    owner: "{{ redis_user }}"
    group: "{{ redis_group }}"
  with_items:
    - "{{ redis_dbdir }}"
    - "{{ redis_log_dir }}"

- name: Ensure Redis configuration dir exists.
  ansible.builtin.file:
    path: "{{ redis_conf_path | dirname }}"
    state: directory
    mode: 0755

- name: Ensure Redis is configured.
  ansible.builtin.template:
    src: redis.conf.j2
    dest: "{{ redis_conf_path }}"
    mode: "{{ redis_conf_mode }}"
    owner: "{{ redis_user }}"
    group: "{{ redis_group }}"
    backup: true
  notify: Restart redis

- name: Download redis
  ansible.builtin.get_url:
    url: "{{ redis_download_url }}"
    dest: "{{ redis_download_path }}"
    mode: 0755

- name: Unarchive redis
  ansible.builtin.unarchive:
    src: "{{ redis_download_path }}"
    dest: "{{ redis_tmp_install_path }}"
    remote_src: true

- name: Make redis
  ansible.builtin.command: make
  args:
    chdir: "{{ redis_tmp_install_path }}/redis-{{ redis_version }}"
  changed_when: false

- name: Make install redis
  ansible.builtin.command: make install
  args:
    chdir: "{{ redis_tmp_install_path }}/redis-{{ redis_version }}"
  changed_when: false

- name: Remove redis tmp install path and redis download path
  ansible.builtin.file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "{{ redis_tmp_install_path }}/redis-{{ redis_version }}"
    - "{{ redis_download_path }}"

- name: Copy redis-cli and redis-server to /usr/bin/
  ansible.builtin.copy:
    src: "{{ item }}"
    dest: /usr/bin/
    mode: 0755
    remote_src: true
  with_items:
    - "/usr/local/bin/redis-cli"
    - "/usr/local/bin/redis-server"

- name: Create systemd unit file for redis
  ansible.builtin.template:
    src: redis-server.service.j2
    dest: /lib/systemd/system/redis-server.service
    mode: 0644
  notify: Restart redis

- name: Link systemd unit file for redis
  ansible.builtin.file:
    src: /lib/systemd/system/redis-server.service
    dest: /etc/systemd/system/multi-user.target.wants/redis-server.service
    state: link

- name: Ensure Redis is running and enabled on boot.
  ansible.builtin.systemd:
    name: redis-server
    state: restarted
    enabled: true
    daemon_reload: true

- name: Setup redis-exporter
  ansible.builtin.import_role:
    name: prometheus-redis-exporter

- name: Setup consul-agents
  ansible.builtin.import_role:
    name: consul-agents

- name: Add line to redis.conf for zabbix-agent2
  become: true
  ansible.builtin.lineinfile:
    path: /etc/zabbix/zabbix_agent2.d/plugins.d/redis.conf
    line: "Plugins.Redis.Default.Password={{ redis_requirepass }}"
    state: present
  when: redis_requirepass is defined

- name: Add line to zabbix_agent2.conf for zabbix-agent2
  become: true
  ansible.builtin.lineinfile:
    path: /etc/zabbix/zabbix_agent2.conf
    line: "Include=/etc/zabbix/zabbix_agent2.d/plugins.d/redis.conf"
    state: present
  when: redis_requirepass is defined

- name: Restart zabbix-agent2
  become: true
  ansible.builtin.service:
    name: zabbix-agent2
    state: restarted
  when: redis_requirepass is defined
