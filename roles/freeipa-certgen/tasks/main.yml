---
# tasks file for roles/freeipa-certgen
- name: Check if the key exists
  ansible.builtin.stat:
    path: "{{ freeipa_certgen_key_path }}"
  register: key_check

- name: Generate an OpenSSL private key with the default values (4096 bits, RSA)
  community.crypto.openssl_privatekey:
    path: "{{ freeipa_certgen_key_path }}"
  when: not key_check.stat.exists

- name: Generate a certificate signing request
  community.crypto.openssl_csr:
    path: "{{ freeipa_certgen_csr_path }}"
    privatekey_path: "{{ freeipa_certgen_key_path }}"
    common_name: "{{ freeipa_certgen_ssl_common_name }}"
    country_name: "{{ freeipa_certgen_ssl_country_name }}"
    state_or_province_name: "{{ freeipa_certgen_ssl_state_or_province_name }}"
    locality_name: "{{ freeipa_certgen_ssl_locality_name }}"
    organization_name: "{{ freeipa_certgen_ssl_organization_name }}"
    organizational_unit_name: "{{ freeipa_certgen_ssl_organizational_unit_name }}"
    email_address: "{{ freeipa_certgen_ssl_email_address }}"
    state: present
  when: not key_check.stat.exists

- name: Check if the cert exists
  ansible.builtin.stat:
    path: "{{ freeipa_certgen_cert_path }}"
  register: cert_check

- name: Obtain kerberos ticket
  ansible.builtin.command:
    cmd: kinit {{ ipa_admin_user }}
    stdin: "{{ admin_password }}"
  changed_when: false
  when: cert_check.stat.exists != 'false'

- name: Check if the host exists
  ansible.builtin.command: ipa host-find {{ freeipa_certgen_ssl_common_name }}
  register: host_check
  failed_when:
    - "'0 hosts matched' not in host_check.stdout"
    - "'1 host matched' not in host_check.stdout"
  changed_when: false

- name: Create the host if it does not exist
  ansible.builtin.command: ipa host-add {{ freeipa_certgen_ssl_common_name }} --force
  when: host_check.rc != 0
  changed_when: false

- name: Check id DNS record exists
  ansible.builtin.command: ipa dnsrecord-find {{ ipa_domain }} --name={{ freeipa_certgen_domain }} --all
  register: dns_check
  failed_when:
    - "'Number of entries returned' not in dns_check.stdout"
  changed_when: false

- name: Create DNS record if it does not exist
  community.general.ipa_dnsrecord:
    ipa_host: "{{ ipa_server_hostname }}.{{ ipa_domain }}"
    ipa_pass: "{{ admin_password }}"
    state: present
    zone_name: "{{ ipa_domain }}"
    record_name: "{{ freeipa_certgen_domain }}"
    record_type: 'A'
    record_value: "{{ ansible_host }}"
    validate_certs: false
    ipa_user: "{{ ipa_admin_user }}"
  when: dns_check.rc != 0

- name: Check if the principal exists
  ansible.builtin.command: ipa service-find HTTP/{{ freeipa_certgen_ssl_common_name }}@{{ ipa_realm }}
  register: principal_check
  failed_when:
    - "'Number of entries returned' not in principal_check.stdout"
  changed_when: false

- name: Create the principal if it does not exist
  ansible.builtin.command: ipa service-add HTTP/{{ freeipa_certgen_ssl_common_name }}@{{ ipa_realm }}
  when: principal_check.rc != 0
  changed_when: false

- name: Sign the certificate
  ansible.builtin.command: ipa cert-request --principal=HTTP/{{ freeipa_certgen_ssl_common_name }}@{{ ipa_realm }} {{ freeipa_certgen_csr_path }}
  register: certificate_request
  when: not cert_check.stat.exists
  changed_when: false

- name: Extract serial number from certificate request output
  ansible.builtin.set_fact:
    serial_number: "{{ (certificate_request.stdout_lines | join('\n') | regex_search('Serial number: (.*)', '\\1'))[0] }}"
  when: not cert_check.stat.exists

- name: Save certificate with ipa cert-show
  ansible.builtin.command: ipa cert-show {{ serial_number }} --out {{ freeipa_certgen_cert_path }}
  when: not cert_check.stat.exists
  changed_when: false
