---
# defaults file for roles/sftpgo
# Nginx
sftpgo_nginx_ver: "1.26.2"
sftpgo_req_dir:
  - /var/log/nginx
sftpgo_nginx_install_dir: /etc/nginx
sftpgo_nginx_timeout_seconds: 600
sftpgo_nginx_log_dir: /var/log/nginx
sftpgo_nginx_sites_dir: /etc/nginx/sites-enabled
sftpgo_nginx_ssl_dir: /etc/nginx/ssl
sftpgo_nginx_worker_rlimit_nofile: 64000
sftpgo_nginx_worker_connections: 130000
sftpgo_nginx_req_dir:
  - /var/log/nginx
  - /etc/nginx/sites-enabled

# SFTPGo
sftpgo_version: v2.6.6-alpine
sftpgo_image: "harbor.sl.local/proxy-cache/drakkan/sftpgo:{{ sftpgo_version }}"

sftpgo_dir: /srv/sftpgo
sftpgo_data_dir: /srv/sftpgo/data
sftpgo_port: 8080
sftpgo_bind_address: 127.0.0.1

sftpgo_data_provider_driver: "postgresql"
sftpgo_data_provider_name: "sftpgo"
sftpgo_data_provider_host: "*********"
sftpgo_data_provider_port: 5432
sftpgo_data_provider_username: "sftpgo"
sftpgo_data_provider_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33653530353834386232396364393639363064623866343131383432313765323536623264666264
          3464643331323539396639613630623237643039616363340a653139336530646233383965343966
          37656538663338653433626366393837663632633330653538626461343436643437643963366161
          3666343534646137370a616330643562613138336633313730613639376136356539613233643930
          35666530386635643162613334323432633064663031396233383063363635306538

sftpgo_sftp_port: 2022
sftpgo_ftp_port: 2121

sftpgo_httpd_signing_passphrase: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63633039613461666636663264356430333763353937643036633166373637363639396561346331
          3033356464303536656263663766616662303030393266370a383362356139333362623931626431
          64353632623639363865663739613437303632333538313035643333303064353538633439326634
          3931386635396236310a353033333436323161633431353137643134343737326234313763656632
          65383534646338306365326330396239633933333635346236306563353661623764353662663966
          3966343537616337346434343463393862316436303061343065
