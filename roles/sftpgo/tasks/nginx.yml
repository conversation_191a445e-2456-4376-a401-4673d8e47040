---
- name: Add Nginx repo key
  ansible.builtin.apt_key:
    url: http://nginx.org/keys/nginx_signing.key
    state: present
  tags: nginx

- name: Add repository
  ansible.builtin.apt_repository:
    repo: "{{ item }}"
    state: present
    update_cache: true
  with_items:
    - "deb https://nginx.org/packages/ubuntu/ {{ ansible_distribution_release }} nginx"
    - "deb-src https://nginx.org/packages/ubuntu/ {{ ansible_distribution_release }} nginx"
  tags: nginx

- name: Install nginx
  ansible.builtin.package:
    name: nginx={{ sftpgo_nginx_ver }}-1~{{ ansible_distribution_release }}
    state: present
  tags: nginx

- name: Mkdir {{ sftpgo_nginx_req_dir }}
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: 0755
    owner: www-data
    group: www-data
  with_items: "{{ sftpgo_nginx_req_dir }}"
  tags: nginx

- name: Copy nginx conf
  ansible.builtin.template:
    src: nginx.conf.j2
    dest: "/etc/nginx/nginx.conf"
    mode: "0644"
  notify: Restart nginx
  tags: nginx

- name: Copy default.conf
  ansible.builtin.template:
    src: default.conf.j2
    dest: "{{ sftpgo_nginx_sites_dir }}/default.conf"
    mode: "0644"
  notify: Reload nginx
  tags: nginx

- name: Mkdir {{ sftpgo_nginx_ssl_dir }}
  ansible.builtin.file:
    path: "{{ sftpgo_nginx_ssl_dir }}"
    state: directory
    mode: "0755"
  tags: nginx

- name: Rm /etc/nginx/conf.d/default.conf
  ansible.builtin.file:
    path: "/etc/nginx/conf.d/default.conf"
    state: absent
  tags: nginx
