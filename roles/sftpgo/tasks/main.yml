---
# tasks file for roles/sftpgo
- name: Install and prepare nginx
  ansible.builtin.import_tasks: nginx.yml
  tags: nginx

- name: Template site sftp.sellerlogic.com
  ansible.builtin.template:
    src: sftp.sellerlogic.com.conf.j2
    dest: "{{ sftpgo_nginx_sites_dir }}/sftp.sellerlogic.com.conf"
    mode: "0644"
  notify: Reload nginx
  tags:
    - nginx
    - consul

- name: Install and prepare fail2ban
  ansible.builtin.import_tasks: fail2ban.yml
  tags: fail2ban

- name: Create directory for sftpgo
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: 0755
  loop:
    - "{{ sftpgo_dir }}"
    - "{{ sftpgo_data_dir }}"
  tags: sftpgo

- name: Template docker-compose.yml
  ansible.builtin.template:
    src: docker-compose.yml.j2
    dest: "{{ sftpgo_dir }}/docker-compose.yml"
    mode: "0644"
  tags: sftpgo

- name: Start sftpgo docker container
  ansible.builtin.command: docker compose -f {{ sftpgo_dir }}/docker-compose.yml up -d
  tags: sftpgo
  changed_when: false
