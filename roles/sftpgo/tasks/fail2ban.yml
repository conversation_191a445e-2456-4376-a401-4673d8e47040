---
- name: Install package fail2ban
  ansible.builtin.package:
    name: fail2ban
    state: present
  tags: fail2ban

- name: Copy jail.local
  ansible.builtin.template:
    src: jail.local.j2
    dest: /etc/fail2ban/jail.local
    mode: "0644"
  notify: Restart fail2ban
  tags: fail2ban

- name: Start fail2ban
  ansible.builtin.systemd:
    name: fail2ban
    state: started
  tags: fail2ban

- name: Enable fail2ban
  ansible.builtin.systemd:
    name: fail2ban
    enabled: true
  tags: fail2ban
