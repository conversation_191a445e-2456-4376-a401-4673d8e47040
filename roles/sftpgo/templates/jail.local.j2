[DEFAULT]
bantime  = 4w
findtime = 1h
maxretry = 3
ignoreip = 127.0.0.1/8 ::1 ***********/16 10.0.0.0/16
banaction = %(banaction_allports)s
backend = systemd

[sshd]
enabled   = true
port     = 22
filter   = sshd
logpath  = %(sshd_log)s
maxretry = 15

[sshd-custom-port]
enabled  = true
port     = 2202
filter   = sshd
logpath  = %(sshd_log)s
maxretry = 3

[recidive]
enabled  = true
filter   = recidive
logpath  = /var/log/fail2ban.log
banaction = %(banaction_allports)s
bantime   = 1y
findtime  = 1w
maxretry  = 2
