services:
  sftpgo:
    image: "{{ sftpgo_image }}"
    restart: always
    ports:
      - "{{ sftpgo_port }}:8080"
      - "{{ sftpgo_sftp_port }}:2022"
      - "{{ sftpgo_ftp_port }}:2121"
    environment:
      SFTPGO_DATA_PROVIDER__DRIVER: "{{ sftpgo_data_provider_driver }}"
      SFTPGO_DATA_PROVIDER__NAME: "{{ sftpgo_data_provider_name }}"
      SFTPGO_DATA_PROVIDER__HOST: "{{ sftpgo_data_provider_host }}"
      SFTPGO_DATA_PROVIDER__PORT: "{{ sftpgo_data_provider_port }}"
      SFTPGO_DATA_PROVIDER__USERNAME: "{{ sftpgo_data_provider_username }}"
      SFTPGO_DATA_PROVIDER__PASSWORD: "{{ sftpgo_data_provider_password }}"
      SFTPGO_SFTPD__BINDINGS__0__PORT: "{{ sftpgo_sftp_port }}"
      SFTPGO_FTPD__BINDINGS__0__PORT: "{{ sftpgo_ftp_port }}"
      SFTPGO_HTTPD__TOKEN_VALIDATION: "1"
      SFTPGO_HTTPD__SIGNING_PASSPHRASE: "{{ sftpgo_httpd_signing_passphrase }}"
      SFTPGO_COMMON__DEFENDER__ENABLED: "true"
      SFTPGO_COMMON__DEFENDER__DRIVER: "provider"
    volumes:
      - {{ sftpgo_data_dir }}:/srv/sftpgo
