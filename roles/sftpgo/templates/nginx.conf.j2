user www-data;
worker_processes auto;
pid /var/run/nginx.pid;
worker_rlimit_nofile {{ sftpgo_nginx_worker_rlimit_nofile }};

events {
    worker_connections {{ sftpgo_nginx_worker_connections }};
    multi_accept on;
    use epoll;
}

http {

    include mime.types;
    default_type application/octet-stream;
    sendfile on;


    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout  65;
    server_tokens off;

    client_max_body_size 256M;
    server_names_hash_max_size 512;
    server_names_hash_bucket_size 64;
    proxy_headers_hash_max_size 512;
    proxy_headers_hash_bucket_size 128;

    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 4;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript;

    ssl_session_timeout  5m;
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_session_cache shared:SSL:50m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:ECDHE-RSA-AES128-GCM-SHA256:AES256+EECDH:DHE-RSA-AES128-GCM-SHA256:AES256+EDH:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4";
    ssl_prefer_server_ciphers on;

    log_format main '[$time_local] "$host" "$remote_addr" "$status" "$request_time" "$upstream_response_time" "$bytes_sent" "$request" "$http_referer"';

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    include /etc/nginx/sites-enabled/*.conf;

    server {
	    listen 80;
        server_name 127.0.0.1;

	    location /basic_status {
                stub_status;
                allow 127.0.0.1;
                allow ::1;
	            deny all;
        }
    }

}
