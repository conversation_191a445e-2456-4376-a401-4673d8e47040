server {
    server_name sftp.sellerlogic.com;
    access_log /var/log/nginx/sftp.sellerlogic.com.access.log;
    error_log /var/log/nginx/sftp.sellerlogic.com.error.log;
    ssi on;
    add_header X-Robots-Tag "noindex";
    location / {
            proxy_pass http://{{ sftpgo_bind_address }}:8080;
            proxy_redirect http://{{ sftpgo_bind_address }}:8080 /;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
    }
    listen 80;
    http2 on;
}
