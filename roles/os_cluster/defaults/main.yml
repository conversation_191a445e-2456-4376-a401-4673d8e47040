---
# OpenSearch cluster configuration
os_cluster_nodes: |-
          {% for item in ansible_play_hosts_all -%}
            {{ hostvars[item]['ansible_default_ipv4']['address'] }}:9300{% if not loop.last %},{% endif %}
          {%- endfor %}

os_cluster_master_nodes: |-
          {% for item in ansible_play_hosts_all -%}
            {%- if 'cluster_manager' in (hostvars[item]['os_cluster_node_roles'] | default(['cluster_manager', 'data', 'ingest'])) -%}
              {{ item }}{% if not loop.last %},{% endif %}
          {%- endif -%}
          {%- endfor %}

os_cluster_populate_inventory_to_hosts_file: true

os_cluster_home: /usr/share/opensearch
os_cluster_conf_dir: /usr/share/opensearch/config
os_cluster_plugin_bin_path: /usr/share/opensearch/bin/opensearch-plugin
os_cluster_sec_plugin_conf_path: /usr/share/opensearch/plugins/opensearch-security/securityconfig
os_cluster_sec_plugin_tools_path: /usr/share/opensearch/plugins/opensearch-security/tools
os_cluster_api_port: 9200

os_cluster_systemctl_path: /etc/systemd/system

# Security settings
os_cluster_admin_password: "admin"
os_cluster_kibanaserver_password: "kibanaserver"

# Version and download settings
os_cluster_version: "2.3.0"
os_cluster_dashboards_version: "2.3.0"
os_cluster_download_url: "https://artifacts.opensearch.org/releases/bundle/opensearch"

# User settings
os_cluster_user: opensearch
os_cluster_dashboards_user: opensearch-dashboards

# Domain and cluster settings
os_cluster_domain_name: "opensearch.local"
os_cluster_type: "multi-node"

# JVM settings
os_cluster_xms_value: 10
os_cluster_xmx_value: 10

# Data disk settings
os_cluster_data_disk: /dev/vdb
os_cluster_data_mount_point: /data
os_cluster_data_filesystem: ext4

# Java settings
os_cluster_java_packages:
  - openjdk-21-jdk
  - openjdk-21-jre

# System tuning
os_cluster_vm_max_map_count: 262144
os_cluster_fs_file_max: 65536

# Service settings
os_cluster_service_enabled: true
os_cluster_service_state: started

# Default node roles if not specified in host_vars
os_cluster_node_roles:
  - cluster_manager
  - data
  - ingest

# OpenSearch Dashboards configuration
os_cluster_dashboards_enabled: false
os_cluster_dashboards_home: /usr/share/opensearch-dashboards
os_cluster_dashboards_conf_dir: /usr/share/opensearch-dashboards/config
os_cluster_dashboards_plugin_bin_path: /usr/share/opensearch-dashboards/bin/opensearch-dashboards-plugin
os_cluster_dashboards_port: 5601
os_cluster_dashboards_host: "{{ ansible_default_ipv4.address }}"

# OpenSearch nodes for dashboards connection
# Define which group contains OpenSearch nodes (can be overridden in group_vars)
os_cluster_opensearch_group: "{{ group_names | select('match', '.*opensearch.*') | first | default(group_names[0]) }}"

# OpenSearch hosts for dashboards to connect to
os_cluster_dashboards_opensearch_hosts: |-
        {% for item in groups[os_cluster_opensearch_group] | default([]) -%}
          https://{{ hostvars[item]['ansible_default_ipv4']['address'] }}:{{ os_cluster_api_port }}{% if not loop.last %},{% endif %}
        {%- endfor %}

# Dashboards security settings
os_cluster_dashboards_opensearch_username: kibanaserver
os_cluster_dashboards_opensearch_password: "{{ os_cluster_kibanaserver_password }}"
os_cluster_dashboards_ssl_verification_mode: none
os_cluster_dashboards_cookie_secure: false

# Dashboards multitenancy
os_cluster_dashboards_multitenancy_enabled: true
os_cluster_dashboards_multitenancy_preferred_tenants:
  - "Private"
  - "Global"
os_cluster_dashboards_readonly_roles:
  - "kibana_read_only"

os_cluster_dashboards_telemetry_enabled: false
os_cluster_dashboards_maps_include_elastic: false
os_cluster_dashboards_timeline_enabled: false

os_cluster_security_ssl_http_enabled: true
