---
# OpenSearch Dashboards configuration

# Server settings
server.port: {{ os_cluster_dashboards_port }}
server.host: "{{ os_cluster_dashboards_host }}"

# OpenSearch connection settings
opensearch.hosts: [{{ os_cluster_dashboards_opensearch_hosts }}]
opensearch.ssl.verificationMode: {{ os_cluster_dashboards_ssl_verification_mode }}
opensearch.username: "{{ os_cluster_dashboards_opensearch_username }}"
opensearch.password: "{{ os_cluster_dashboards_opensearch_password }}"
opensearch.requestHeadersWhitelist: [authorization, securitytenant]

# Security plugin settings
opensearch_security.multitenancy.enabled: {{ os_cluster_dashboards_multitenancy_enabled | lower }}
opensearch_security.multitenancy.tenants.preferred: {{ os_cluster_dashboards_multitenancy_preferred_tenants | to_json }}
opensearch_security.readonly_mode.roles: {{ os_cluster_dashboards_readonly_roles | to_json }}

# Cookie security
opensearch_security.cookie.secure: {{ os_cluster_dashboards_cookie_secure | lower }}

# Logging settings
{% if os_cluster_logger_level_root is defined %}
logging.root.level: {{ os_cluster_logger_level_root }}
{% endif %}

# Additional dashboards settings
{% if os_cluster_dashboards_base_path is defined %}
server.basePath: "{{ os_cluster_dashboards_base_path }}"
{% endif %}

{% if os_cluster_dashboards_rewrite_base_path is defined %}
server.rewriteBasePath: {{ os_cluster_dashboards_rewrite_base_path | lower }}
{% endif %}

# Data directory
{% if os_cluster_dashboards_data_dir is defined %}
path.data: "{{ os_cluster_dashboards_data_dir }}"
{% endif %}

# Performance settings
{% if os_cluster_dashboards_max_payload_bytes is defined %}
server.maxPayloadBytes: {{ os_cluster_dashboards_max_payload_bytes }}
{% endif %}

{% if os_cluster_dashboards_request_timeout is defined %}
opensearch.requestTimeout: {{ os_cluster_dashboards_request_timeout }}
{% endif %}

# Telemetry
telemetry.enabled: {{ os_cluster_dashboards_telemetry_enabled | default(false) | lower }}
telemetry.optIn: false

# Maps
map.includeElasticMapsService: {{ os_cluster_dashboards_maps_include_elastic | default(false) | lower }}

# Vis type timeline
vis_type_timeline.enabled: {{ os_cluster_dashboards_timeline_enabled | default(false) | lower }}

# Logging
{% if os_cluster_dashboards_log_level is defined %}
logging.root.level: {{ os_cluster_dashboards_log_level }}
{% endif %}

{% if os_cluster_dashboards_log_quiet is defined %}
logging.quiet: {{ os_cluster_dashboards_log_quiet | lower }}
{% endif %}
