plugins.security.allow_default_init_securityindex: true
plugins.security.audit.type: internal_opensearch
plugins.security.enable_snapshot_restore_privilege: true
plugins.security.check_snapshot_restore_write_privileges: true
plugins.security.restapi.roles_enabled: ["all_access", "security_rest_api_access"]

# SSL Transport settings
{% if os_cluster_security_ssl_transport_enabled is defined %}
plugins.security.ssl.transport.enabled: {{ os_cluster_security_ssl_transport_enabled | lower }}
{% endif %}

# SSL HTTP settings
{% if os_cluster_security_ssl_http_enabled is defined %}
plugins.security.ssl.http.enabled: {{ os_cluster_security_ssl_http_enabled | lower }}
{% endif %}
