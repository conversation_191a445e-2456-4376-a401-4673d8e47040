[Unit]
Description=opensearch
Wants=network-online.target
After=network-online.target

[Service]
RuntimeDirectory=opensearch
PrivateTmp=true

WorkingDirectory={{ os_cluster_home }}

User={{ os_cluster_user }}
Group={{ os_cluster_user }}

ExecStart={{ os_cluster_home }}/bin/opensearch -p {{ os_cluster_home }}/opensearch.pid -q

StandardOutput=journal
StandardError=inherit

# Specifies the maximum file descriptor number that can be opened by this process
LimitNOFILE=65536

# Specifies the memory lock settings
LimitMEMLOCK=infinity

# Specifies the maximum number of processes
LimitNPROC=4096

# Specifies the maximum size of virtual memory
LimitAS=infinity

# Specifies the maximum file size
LimitFSIZE=infinity

# Disable timeout logic and wait until process is stopped
TimeoutStopSec=0

# SIGTERM signal is used to stop the Java process
KillSignal=SIGTERM

# Send the signal only to the JVM rather than its control group
KillMode=process

# Java process is never killed
SendSIGKILL=no

# When a JVM receives a SIGTERM signal it exits with code 143
SuccessExitStatus=143

[Install]
WantedBy=multi-user.target
