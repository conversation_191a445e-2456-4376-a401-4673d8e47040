---
ca:
  root:
    dn: "CN=root.ca.{{ os_cluster_domain_name }},OU=CA,O={{ os_cluster_domain_name }} Inc.,DC={{ os_cluster_domain_name }}"
    keysize: 2048
    validityDays: 730
    pkPassword: none
    file: root-ca.pem

# Default values and global settings
defaults:
  validityDays: 730
  pkPassword: none
  # Set this to true in order to generate config and certificates for
  # the HTTP interface of nodes
  httpsEnabled: true
  reuseTransportCertificatesForHttp: false
  verifyHostnames: false
  resolveHostnames: false

# Nodes
# Specify the nodes of your ES cluster here
nodes:
{% for item in ansible_play_hosts_all %}
  - name: "{{ item }}"
    dn: "CN={{ item }}.{{ os_cluster_domain_name }},OU=Ops,O={{ os_cluster_domain_name }} Inc.,DC={{ os_cluster_domain_name }}"
    dns: "{{ item }}.{{ os_cluster_domain_name }}"
    ip: "{{ hostvars[item]['ansible_default_ipv4']['address'] }}"
{% endfor %}

# Clients
# Specify the clients that shall access your ES cluster with certificate authentication here
# At least one client must be an admin user (i.e., a super-user). Admin users can
# be specified with the attribute admin: true
clients:
  - name: admin
    dn: "CN=admin.{{ os_cluster_domain_name }},OU=Ops,O={{ os_cluster_domain_name }} Inc.,DC={{ os_cluster_domain_name }}"
    admin: true
