# OpenSearch cluster configuration
cluster.name: "{{ os_cluster_name }}"
node.name: "{{ inventory_hostname }}"

# Network settings
network.host: "{{ ansible_default_ipv4.address }}"
http.port: 9200
transport.port: 9300

# Memory settings
bootstrap.memory_lock: true

# Discovery settings
discovery.seed_hosts: [{{ os_cluster_nodes }}]
cluster.initial_master_nodes: [{{ os_cluster_master_nodes }}]

# Path settings
path.data: "{{ os_cluster_data_mount_point }}"
path.logs: "{{ os_cluster_home }}/logs"

# Node roles
node.roles:
{% for role in os_cluster_node_roles %}
  - "{{ role }}"
{% endfor %}

# Compatibility
compatibility.override_main_response_version: true

# Performance settings
indices.query.bool.max_clause_count: 10000
thread_pool.write.queue_size: 1000

# Additional cluster settings
{% if os_cluster_indices_memory_index_buffer_size is defined %}
indices.memory.index_buffer_size: "{{ os_cluster_indices_memory_index_buffer_size }}"
{% endif %}
{% if os_cluster_indices_memory_min_index_buffer_size is defined %}
indices.memory.min_index_buffer_size: "{{ os_cluster_indices_memory_min_index_buffer_size }}"
{% endif %}

# Logging settings
{% if os_cluster_logger_level_root is defined %}
logger.level.root: "{{ os_cluster_logger_level_root }}"
{% endif %}
{% if os_cluster_logger_level_org_opensearch is defined %}
logger.level.org.opensearch: "{{ os_cluster_logger_level_org_opensearch }}"
{% endif %}

# Cluster routing settings
{% if os_cluster_routing_allocation_disk_threshold_enabled is defined %}
cluster.routing.allocation.disk.threshold_enabled: {{ os_cluster_routing_allocation_disk_threshold_enabled | lower }}
{% endif %}
{% if os_cluster_routing_allocation_disk_watermark_low is defined %}
cluster.routing.allocation.disk.watermark.low: "{{ os_cluster_routing_allocation_disk_watermark_low }}"
{% endif %}
{% if os_cluster_routing_allocation_disk_watermark_high is defined %}
cluster.routing.allocation.disk.watermark.high: "{{ os_cluster_routing_allocation_disk_watermark_high }}"
{% endif %}
{% if os_cluster_routing_allocation_disk_watermark_flood_stage is defined %}
cluster.routing.allocation.disk.watermark.flood_stage: "{{ os_cluster_routing_allocation_disk_watermark_flood_stage }}"
{% endif %}

# HTTP CORS settings
{% if os_cluster_http_cors_enabled is defined %}
http.cors.enabled: {{ os_cluster_http_cors_enabled | lower }}
{% endif %}
{% if os_cluster_http_cors_allow_origin is defined %}
http.cors.allow-origin: "{{ os_cluster_http_cors_allow_origin }}"
{% endif %}
{% if os_cluster_http_cors_allow_headers is defined %}
http.cors.allow-headers: "{{ os_cluster_http_cors_allow_headers }}"
{% endif %}
