[Unit]
Description=OpenSearch Dashboards
Documentation=https://opensearch.org/docs/latest/dashboards/
Wants=network-online.target
After=network-online.target

[Service]
Type=simple
RuntimeDirectory=opensearch-dashboards
PrivateTmp=true

WorkingDirectory={{ os_cluster_dashboards_home }}

User={{ os_cluster_dashboards_user }}
Group={{ os_cluster_dashboards_user }}

ExecStart={{ os_cluster_dashboards_home }}/bin/opensearch-dashboards

StandardOutput=journal
StandardError=inherit

# Specifies the maximum file descriptor number that can be opened by this process
LimitNOFILE=65536

# Specifies the maximum number of processes
LimitNPROC=4096

# Disable timeout logic and wait until process is stopped
TimeoutStopSec=0

# SIGTERM signal is used to stop the service
KillSignal=SIGTERM

# Send the signal only to the main process, not the entire control group
KillMode=process

# Give a reasonable amount of time for the process to start up
TimeoutStartSec=300

[Install]
WantedBy=multi-user.target
