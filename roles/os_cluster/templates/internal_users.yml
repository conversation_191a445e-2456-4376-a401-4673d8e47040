---
# This is the internal user database
# The hash value is a bcrypt hash and can be generated with plugin/tools/hash.sh

_meta:
  type: "internalusers"
  config_version: 2

# Define your internal users here

admin:
  hash: "{{ os_cluster_admin_password }}"
  reserved: true
  backend_roles:
    - "admin"
  description: "admin user"

kibanaserver:
  hash: "{{ os_cluster_kibanaserver_password }}"
  reserved: true
  description: "kibanaserver user"
