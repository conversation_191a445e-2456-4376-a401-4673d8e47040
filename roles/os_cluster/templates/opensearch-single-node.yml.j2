# OpenSearch single node configuration
cluster.name: "{{ os_cluster_name }}"
node.name: "{{ inventory_hostname }}"

# Network settings
network.host: "{{ ansible_default_ipv4.address }}"
http.port: 9200
transport.port: 9300

# Discovery settings
discovery.type: single-node

# Memory settings
bootstrap.memory_lock: true

# Path settings
path.data: "{{ os_cluster_data_mount_point }}"
path.logs: "{{ os_cluster_home }}/logs"

# Node roles
node.roles:
{% for role in os_cluster_node_roles %}
  - "{{ role }}"
{% endfor %}

# Compatibility
compatibility.override_main_response_version: true

# Additional cluster settings
{% if os_cluster_indices_memory_index_buffer_size is defined %}
indices.memory.index_buffer_size: "{{ os_cluster_indices_memory_index_buffer_size }}"
{% endif %}
{% if os_cluster_indices_memory_min_index_buffer_size is defined %}
indices.memory.min_index_buffer_size: "{{ os_cluster_indices_memory_min_index_buffer_size }}"
{% endif %}

# Logging settings
{% if os_cluster_logger_level_root is defined %}
logger.level.root: "{{ os_cluster_logger_level_root }}"
{% endif %}
{% if os_cluster_logger_level_org_opensearch is defined %}
logger.level.org.opensearch: "{{ os_cluster_logger_level_org_opensearch }}"
{% endif %}

# HTTP CORS settings
{% if os_cluster_http_cors_enabled is defined %}
http.cors.enabled: {{ os_cluster_http_cors_enabled | lower }}
{% endif %}
{% if os_cluster_http_cors_allow_origin is defined %}
http.cors.allow-origin: "{{ os_cluster_http_cors_allow_origin }}"
{% endif %}
{% if os_cluster_http_cors_allow_headers is defined %}
http.cors.allow-headers: "{{ os_cluster_http_cors_allow_headers }}"
{% endif %}
