# OpenSearch Cluster Role

This Ansible role installs and configures OpenSearch cluster nodes and OpenSearch Dashboards.

## Requirements

- Ansible 2.9+
- Ubuntu 20.04+ or RHEL/CentOS 8+
- Java 21 or higher
- Minimum 4GB RAM per node
- Dedicated data disk (optional but recommended)

## Role Variables

### Required Variables

- `os_cluster_name`: Name of the OpenSearch cluster
- `os_cluster_version`: OpenSearch version to install
- `os_cluster_domain_name`: Domain name for SSL certificates
- `ansible_default_ipv4.address`: IP address of the node (automatically detected)
- `os_cluster_node_roles`: List of node roles

### Optional Variables

- `os_cluster_user`: OpenSearch system user (default: opensearch)
- `os_cluster_xms_value`: JVM heap min size in GB (default: 10)
- `os_cluster_xmx_value`: JVM heap max size in GB (default: 10)
- `os_cluster_type`: single-node or multi-node (default: multi-node)
- `os_cluster_data_disk`: Data disk device (default: /dev/vdb)
- `os_cluster_data_mount_point`: Data mount point (default: /data)
- `os_cluster_service_enabled`: Enable OpenSearch service (default: true)
- `os_cluster_service_state`: OpenSearch service state (default: started)

### Security Variables (should be vaulted)

- `os_cluster_admin_password`: Admin user password
- `os_cluster_kibanaserver_password`: Kibanaserver user password

## Host Variables

Each OpenSearch node should have the following host variables defined:

```yaml
# inventories/host_vars/hostname.yml
---
os_cluster_node_roles:
  - cluster_manager
  - data
  - ingest
```

## Usage

### Basic Setup

1. **Configure inventory**: Add your OpenSearch nodes to any group (e.g., `rc_opensearch`, `prod_opensearch`)
2. **Set group variables**: Configure cluster settings in `group_vars/your_group/main.yml`
3. **Set host variables**: Configure node-specific settings in `host_vars/hostname.yml`
4. **Run playbook**: Execute the playbook targeting your group

### Example Playbook

```yaml
---
- name: Deploy OpenSearch cluster and dashboards
  hosts: "{{ opensearch_hosts | default('opensearch') }}"
  become: true
  roles:
    - role: os_cluster
```

### Running the Playbook

#### Install complete OpenSearch stack (cluster + dashboards)

```bash
# For RC environment
ansible-playbook -i inventories/hosts playbook/opensearch.yml -e opensearch_hosts=rc_opensearch

# For Production environment
ansible-playbook -i inventories/hosts playbook/opensearch.yml -e opensearch_hosts=prod_opensearch

# For any custom group
ansible-playbook -i inventories/hosts playbook/opensearch.yml -e opensearch_hosts=your_group_name
```

#### Install only OpenSearch cluster (without dashboards)

```bash
ansible-playbook -i inventories/hosts playbook/opensearch.yml -e opensearch_hosts=rc_opensearch --tags opensearch
```

#### Install only OpenSearch Dashboards (on nodes where enabled)

```bash
ansible-playbook -i inventories/hosts playbook/opensearch.yml -e opensearch_hosts=rc_opensearch --tags dashboards
```

## Configuration

### Group Variables (`group_vars/your_group/main.yml`)

```yaml
---
# Cluster configuration
os_cluster_name: "my-cluster"
os_cluster_version: "2.3.0"
os_cluster_dashboards_version: "2.3.0"
os_cluster_domain_name: "opensearch.example.com"

# User configuration
os_cluster_user: opensearch
os_cluster_dashboards_user: opensearch-dashboards

# Security (use ansible-vault for passwords)
os_cluster_admin_password: "{{ vault_admin_password }}"
os_cluster_kibanaserver_password: "{{ vault_kibanaserver_password }}"

# Dashboards settings (optional - enable on specific nodes via host_vars)
os_cluster_dashboards_enabled: false
os_cluster_dashboards_port: 5601
```

### Host Variables (`host_vars/hostname.yml`)

```yaml
---
# Node roles
os_cluster_node_roles:
  - cluster_manager
  - data
  - ingest

# Enable dashboards on this specific node (optional)
os_cluster_dashboards_enabled: true
os_cluster_dashboards_host: "{{ ansible_default_ipv4.address }}"
```

## Tags

- `opensearch`: Install and configure OpenSearch cluster nodes
- `dashboards`: Install and configure OpenSearch Dashboards

## OpenSearch Dashboards

Dashboards can be enabled on any node by setting `os_cluster_dashboards_enabled: true` in the node's host_vars. This allows flexible deployment where dashboards can run on:

- Dedicated dashboard nodes
- One or more cluster nodes
- All cluster nodes (if desired)

### Dashboards Variables

- `os_cluster_dashboards_enabled`: Enable dashboards on this node (default: false)
- `os_cluster_dashboards_port`: Dashboards port (default: 5601)
- `os_cluster_dashboards_host`: Dashboards bind address (default: ansible_default_ipv4.address)
- `os_cluster_dashboards_ssl_verification_mode`: SSL verification mode (default: none)
- `os_cluster_dashboards_multitenancy_enabled`: Enable multitenancy (default: true)

## Node Roles

- `cluster_manager`: Can be elected as cluster manager
- `data`: Stores data and executes data-related operations
- `ingest`: Processes documents before indexing
- `ml`: Machine learning node
- `remote_cluster_client`: Connects to remote clusters

## Dependencies

- ansible.posix collection
- community.general collection
