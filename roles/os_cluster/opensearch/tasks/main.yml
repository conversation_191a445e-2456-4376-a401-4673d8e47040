---
- name: <PERSON>reate groups
  ansible.builtin.group:
    name: "{{ item }}"
    state: present
  loop:
    - opensearch
    - opensearch-dashboards
- name: Create users
  ansible.builtin.user:
    name: "{{ item.name }}"
    groups: "{{ item.groups }}"
    state: present
  loop: 
    - { name: "{{ os_user }}", groups: 'opensearch' }
    - { name: "{{ os_dashboards_user }}", groups: 'opensearch-dashboards' }

- name: Create a ext4 filesystem on /dev/vdb and check disk blocks
  community.general.filesystem:
    fstype: ext4
    dev: /dev/vdb
- name: Mount up device
  ansible.posix.mount:
    path: /data
    src: /dev/vdb
    fstype: ext4
    opts: defaults
    dump: 1
    state: mounted
- name: Permissions
  ansible.builtin.file:
    path: /data
    owner: "{{ os_user }}"
    group: "{{ os_user }}"
    mode: '0755'

- name: Install java
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
  loop:
    - default-jdk
    - default-jre

- name: Disable the selinux
  selinux:
    state: disabled
  when: (ansible_distribution != "Ubuntu") and (ansible_distribution != "Amazon")

- name: Populate the nodes to /etc/hosts
  import_tasks: etchosts.yml

- name: Tune the system settings
  import_tasks: tune.yml

- name: include opensearch installation
  include: opensearch.yml

- name: include security plugin for opensearch
  include: security.yml

# After the cluster forms successfully for the first time,
# remove the cluster.initial_master_nodes setting from each nodes' configuration.
- name: Remove `cluster.initial_master_nodes` setting from configuration
  command: sed -i '/cluster.initial_master_nodes/d' "{{os_conf_dir}}/opensearch.yml"

- name: Make sure opensearch is started
  service:
    name: opensearch
    state: started
    enabled: yes

- name: Get all the installed ES plugins
  command: "{{ os_plugin_bin_path }} list"
  register: list_plugins

- name: Show all the installed ES plugins
  debug:
    msg: "{{ list_plugins.stdout }}"

- name: Wait for opensearch to startup
  wait_for: host={{ hostvars[inventory_hostname]['ip'] }} port={{os_api_port}} delay=5 connect_timeout=1

- name: Check the opensearch status
  command: curl https://{{ inventory_hostname }}:9200/_cluster/health?pretty -u 'admin:{{ admin_password }}' -k
  register: os_status

- name: Show the opensearch status
  debug:
    msg: "{{ os_status.stdout }}"
  failed_when: "'number_of_nodes' not in os_status.stdout"

- name: Verify the roles of opensearch cluster nodes
  command: curl https://{{ inventory_hostname }}:9200/_cat/nodes?v -u 'admin:{{ admin_password }}' -k
  register: os_roles
  run_once: true

- name: Show the roles of opensearch cluster nodes
  debug:
    msg: "{{ os_roles.stdout }}"
  run_once: true
