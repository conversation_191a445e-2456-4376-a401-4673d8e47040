---
- name: <PERSON><PERSON> opensearch
  ansible.builtin.systemd:
    name: opensearch
    state: restarted
    daemon_reload: true

- name: Reload opensearch
  ansible.builtin.systemd:
    name: opensearch
    state: reloaded

- name: Wait for opensearch
  ansible.builtin.wait_for:
    host: "{{ ansible_default_ipv4.address }}"
    port: "{{ os_cluster_api_port }}"
    delay: 10
    timeout: 300
    connect_timeout: 5

- name: Reload systemd
  ansible.builtin.systemd:
    daemon_reload: true

- name: Restart opensearch dashboards
  ansible.builtin.systemd:
    name: opensearch-dashboards
    state: restarted
    daemon_reload: true

- name: Reload opensearch dashboards
  ansible.builtin.systemd:
    name: opensearch-dashboards
    state: reloaded

- name: Wait for opensearch dashboards
  ansible.builtin.wait_for:
    host: "{{ os_cluster_dashboards_host }}"
    port: "{{ os_cluster_dashboards_port }}"
    delay: 10
    timeout: 300
    connect_timeout: 5
