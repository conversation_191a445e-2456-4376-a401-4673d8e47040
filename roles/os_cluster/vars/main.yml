---
# Internal role variables
# These variables are used internally by the role and should not be overridden

# OpenSearch service name
os_cluster_opensearch_service_name: opensearch

# Default system limits
os_cluster_limits:
  - domain: "{{ os_cluster_user }}"
    limit_type: soft
    limit_item: nofile
    value: 65536
  - domain: "{{ os_cluster_user }}"
    limit_type: hard
    limit_item: nofile
    value: 65536
  - domain: "{{ os_cluster_user }}"
    limit_type: soft
    limit_item: memlock
    value: unlimited
  - domain: "{{ os_cluster_user }}"
    limit_type: hard
    limit_item: memlock
    value: unlimited

# Required packages
os_cluster_required_packages:
  - curl
  - wget
  - unzip
  - tar

# Systemd service template variables
os_cluster_systemd_service:
  name: opensearch
  description: OpenSearch
  after: network-online.target
  wants: network-online.target
  type: notify
  user: "{{ os_cluster_user }}"
  group: opensearch
  working_directory: "{{ os_cluster_home }}"
  exec_start: "{{ os_cluster_home }}/bin/opensearch"
  limit_nofile: 65536
  limit_memlock: infinity
  limit_nproc: 4096
  limit_as: infinity
  limit_fsize: infinity
  timeout_stop_sec: 0

# OpenSearch Dashboards system limits
os_cluster_dashboards_limits:
  - domain: "{{ os_cluster_dashboards_user }}"
    limit_type: soft
    limit_item: nofile
    value: 65536
  - domain: "{{ os_cluster_dashboards_user }}"
    limit_type: hard
    limit_item: nofile
    value: 65536

# OpenSearch Dashboards required packages
os_cluster_dashboards_required_packages:
  - curl
  - wget

# Systemd service template variables for dashboards
os_cluster_dashboards_systemd_service:
  name: opensearch-dashboards
  description: OpenSearch Dashboards
  after: network-online.target
  wants: network-online.target
  type: simple
  user: "{{ os_cluster_dashboards_user }}"
  group: opensearch-dashboards
  working_directory: "{{ os_cluster_dashboards_home }}"
  exec_start: "{{ os_cluster_dashboards_home }}/bin/opensearch-dashboards"
  limit_nofile: 65536
  limit_nproc: 4096
  timeout_stop_sec: 0
  timeout_start_sec: 300
