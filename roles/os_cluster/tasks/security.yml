---
## Here we are going to use self-signed certificates for Transport (Node-Node communication) & REST API layer
## Using searchguard offline TLS tool to create node & root certificates

- name: Security Plugin configuration | Set temporary directory variable
  ansible.builtin.set_fact:
    temp_certs_dir: "/tmp/opensearch-certs-{{ ansible_date_time.epoch }}"
  run_once: true

- name: Security Plugin configuration | Create temporary directory for certificates generation
  ansible.builtin.file:
    path: /tmp/opensearch-nodecerts
    state: directory
    mode: 0755
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true
  register: configuration

- name: Security Plugin configuration | Download certificates generation tool
  ansible.builtin.get_url:
    url: https://search.maven.org/remotecontent?filepath=com/floragunn/search-guard-tlstool/1.8/search-guard-tlstool-1.8.tar.gz
    dest: /tmp/opensearch-nodecerts/search-guard-tlstool.tar.gz
    mode: 0644
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Extract the certificates generation tool
  ansible.builtin.unarchive:
    src: /tmp/opensearch-nodecerts/search-guard-tlstool.tar.gz
    dest: /tmp/opensearch-nodecerts
    remote_src: true
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Make the executable file
  ansible.builtin.file:
    dest: /tmp/opensearch-nodecerts/tools/sgtlstool.sh
    mode: a+x
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Prepare the certificates generation template file
  ansible.builtin.template:
    src: tlsconfig.yml.j2
    dest: /tmp/opensearch-nodecerts/config/tlsconfig.yml
    mode: '0644'
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Generate the node & admin certificates
  ansible.builtin.command: >
    /tmp/opensearch-nodecerts/tools/sgtlstool.sh
    -c /tmp/opensearch-nodecerts/config/tlsconfig.yml
    -ca -crt
    -t /tmp/opensearch-nodecerts/config/
  environment:
    JAVA_HOME: "{{ os_cluster_home }}/jdk"
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true
  changed_when: false

- name: Security Plugin configuration | Fetch common certificates from first host to controller
  ansible.builtin.fetch:
    src: "/tmp/opensearch-nodecerts/config/{{ item }}"
    dest: "{{ temp_certs_dir }}/"
    flat: true
  with_items:
    - root-ca.pem
    - root-ca.key
    - admin.key
    - admin.pem
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Fetch host-specific certificates from first host to controller
  ansible.builtin.fetch:
    src: "/tmp/opensearch-nodecerts/config/{{ host_item }}{{ cert_type }}"
    dest: "{{ temp_certs_dir }}/"
    flat: true
  loop: "{{ ansible_play_hosts | product(['.key', '.pem', '_http.key', '_http.pem']) | list }}"
  loop_control:
    loop_var: cert_item
  vars:
    host_item: "{{ cert_item[0] }}"
    cert_type: "{{ cert_item[1] }}"
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Fetch elasticsearch config snippets from first host to controller
  ansible.builtin.fetch:
    src: "/tmp/opensearch-nodecerts/config/{{ item }}_elasticsearch_config_snippet.yml"
    dest: "{{ temp_certs_dir }}/"
    flat: true
  loop: "{{ ansible_play_hosts }}"
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true

- name: Security Plugin configuration | Copy certificates to all nodes
  ansible.builtin.copy:
    src: "{{ temp_certs_dir }}/{{ item }}"
    dest: "{{ os_cluster_conf_dir }}/"
    mode: 0600
    owner: "{{ os_cluster_user }}"
    group: opensearch
  with_items:
    - root-ca.pem
    - root-ca.key
    - "{{ inventory_hostname }}.key"
    - "{{ inventory_hostname }}.pem"
    - "{{ inventory_hostname }}_http.key"
    - "{{ inventory_hostname }}_http.pem"
    - admin.key
    - admin.pem

- name: Security Plugin configuration | Copy the security configuration file 1 to cluster
  ansible.builtin.blockinfile:
    block: "{{ lookup('template', 'templates/security_conf.yml.j2') }}"
    dest: "{{ os_cluster_conf_dir }}/opensearch.yml"
    backup: true
    insertafter: EOF
    marker: "## {mark} OpenSearch Security common configuration ##"

- name: Security Plugin configuration | Copy the security configuration file 2 to cluster
  ansible.builtin.blockinfile:
    block: "{{ lookup('file', temp_certs_dir + '/' + inventory_hostname + '_elasticsearch_config_snippet.yml') }}"
    dest: "{{ os_cluster_conf_dir }}/opensearch.yml"
    backup: true
    insertafter: EOF
    marker: "## {mark} opensearch Security Node & Admin certificates configuration ##"

- name: Security Plugin configuration | Prepare the opensearch security configuration file
  ansible.builtin.replace:
    path: "{{ os_cluster_conf_dir }}/opensearch.yml"
    regexp: 'searchguard'
    replace: 'plugins.security'

- name: Security Plugin configuration | Set the file ownerships
  ansible.builtin.file:
    dest: "{{ os_cluster_home }}"
    owner: "{{ os_cluster_user }}"
    group: opensearch
    recurse: true

- name: Security Plugin configuration | Set the folder permission
  ansible.builtin.file:
    dest: "{{ os_cluster_conf_dir }}"
    owner: "{{ os_cluster_user }}"
    group: opensearch
    mode: '0700'

- name: Security Plugin configuration | Restart opensearch with security configuration
  ansible.builtin.systemd:
    name: opensearch
    state: restarted
    enabled: true
    daemon_reload: true

- name: Pause for 3 seconds to provide sometime for OpenSearch start
  ansible.builtin.pause:
    seconds: 3

- name: Security Plugin configuration | Create security config directory
  ansible.builtin.file:
    path: "{{ os_cluster_sec_plugin_conf_path }}"
    state: directory
    mode: 0755
    owner: "{{ os_cluster_user }}"
    group: opensearch

- name: Security Plugin configuration | Copy the opensearch security internal users template
  ansible.builtin.template:
    src: internal_users.yml
    dest: "{{ os_cluster_sec_plugin_conf_path }}/internal_users.yml"
    mode: 0644
  run_once: true

- name: Security Plugin configuration | Generate admin password hash
  ansible.builtin.command: >
    bash {{ os_cluster_sec_plugin_tools_path }}/hash.sh -p {{ os_cluster_admin_password }}
  environment:
    JAVA_HOME: "{{ os_cluster_home }}/jdk"
  register: admin_password_hash
  changed_when: false
  run_once: true

- name: Security Plugin configuration | Set the Admin user password
  ansible.builtin.replace:
    path: "{{ os_cluster_sec_plugin_conf_path }}/internal_users.yml"
    regexp: 'hash: .*$'
    replace: "hash: {{ admin_password_hash.stdout_lines[-1] }}"
  run_once: true

- name: Security Plugin configuration | Generate kibanaserver password hash
  ansible.builtin.command: >
    bash {{ os_cluster_sec_plugin_tools_path }}/hash.sh -p {{ os_cluster_kibanaserver_password }}
  environment:
    JAVA_HOME: "{{ os_cluster_home }}/jdk"
  register: kibanaserver_password_hash
  changed_when: false
  run_once: true

- name: Security Plugin configuration | Set the kibanaserver user password
  ansible.builtin.replace:
    path: "{{ os_cluster_sec_plugin_conf_path }}/internal_users.yml"
    regexp: '(^kibanaserver:.*\n(?:.*\n)*?\s*hash: ).*$'
    replace: '\1{{ kibanaserver_password_hash.stdout_lines[-1] }}'
  run_once: true

- name: Set permission on security tools
  ansible.builtin.file:
    path: "{{ os_cluster_sec_plugin_tools_path }}"
    mode: '0774'
    owner: "{{ os_cluster_user }}"
    group: opensearch

- name: Set permission on data directory
  ansible.builtin.file:
    path: "{{ os_cluster_data_mount_point }}"
    mode: '0774'
    owner: "{{ os_cluster_user }}"
    group: opensearch

- name: Check connection
  ansible.builtin.wait_for:
    host: "{{ ansible_default_ipv4.address }}"
    port: 9300
    delay: 10
    state: present

- name: Security Plugin configuration | Initialize the opensearch security index in opensearch
  become: true
  ansible.builtin.shell: >
    sudo bash {{ os_cluster_sec_plugin_tools_path }}/securityadmin.sh
    -cacert {{ os_cluster_conf_dir }}/root-ca.pem
    -cert {{ os_cluster_conf_dir }}/admin.pem
    -key {{ os_cluster_conf_dir }}/admin.key
    -f {{ os_cluster_sec_plugin_conf_path }}/internal_users.yml
    -nhnv -icl
    -h {{ ansible_default_ipv4.address }}
  environment:
    JAVA_HOME: "{{ os_cluster_home }}/jdk"
  run_once: true
  changed_when: false

- name: Security Plugin configuration | Cleanup temporary directory on first host
  ansible.builtin.file:
    path: /tmp/opensearch-nodecerts
    state: absent
  delegate_to: "{{ ansible_play_hosts[0] }}"
  run_once: true
