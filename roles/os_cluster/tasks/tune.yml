---
- name: Set vm.max_map_count for OpenSearch
  ansible.posix.sysctl:
    name: vm.max_map_count
    value: "{{ os_cluster_vm_max_map_count }}"
    state: present
    reload: true
    sysctl_file: /etc/sysctl.d/99-opensearch.conf

- name: Set fs.file-max for OpenSearch
  ansible.posix.sysctl:
    name: fs.file-max
    value: "{{ os_cluster_fs_file_max }}"
    state: present
    reload: true
    sysctl_file: /etc/sysctl.d/99-opensearch.conf

- name: Set vm.swappiness to reduce swapping
  ansible.posix.sysctl:
    name: vm.swappiness
    value: 1
    state: present
    reload: true
    sysctl_file: /etc/sysctl.d/99-opensearch.conf

- name: Disable swap
  ansible.posix.mount:
    state: absent_from_fstab
    path: none
    src: /swap.img
    backup: true
