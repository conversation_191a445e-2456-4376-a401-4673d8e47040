---
- name: Validate required variables
  ansible.builtin.assert:
    that:
      - os_cluster_name is defined
      - os_cluster_version is defined
      - ansible_default_ipv4.address is defined
      - os_cluster_node_roles is defined
    fail_msg: "Required variables are not defined"
    success_msg: "All required variables are defined"

- name: Create opensearch group
  ansible.builtin.group:
    name: "{{ os_cluster_user }}"
    state: present

- name: Create opensearch-dashboards group
  ansible.builtin.group:
    name: opensearch-dashboards
    state: present

- name: Create opensearch user
  ansible.builtin.user:
    name: "{{ os_cluster_user }}"
    group: "{{ os_cluster_user }}"
    system: true
    shell: /bin/bash
    home: "{{ os_cluster_home }}"
    create_home: false
    state: present

- name: Create opensearch-dashboards user
  ansible.builtin.user:
    name: "{{ os_cluster_dashboards_user }}"
    group: opensearch-dashboards
    system: true
    shell: /bin/bash
    create_home: false
    state: present

- name: Check if data disk exists
  ansible.builtin.stat:
    path: "{{ os_cluster_data_disk }}"
  register: os_cluster_data_disk_stat

- name: Create filesystem on data disk
  community.general.filesystem:
    fstype: "{{ os_cluster_data_filesystem }}"
    dev: "{{ os_cluster_data_disk }}"
    force: false
  when: os_cluster_data_disk_stat.stat.exists

- name: Create data mount point
  ansible.builtin.file:
    path: "{{ os_cluster_data_mount_point }}"
    state: directory
    mode: '0755'

- name: Mount data disk
  ansible.posix.mount:
    path: "{{ os_cluster_data_mount_point }}"
    src: "{{ os_cluster_data_disk }}"
    fstype: "{{ os_cluster_data_filesystem }}"
    opts: defaults,noatime
    dump: 1
    passno: 2
    state: mounted
  when: os_cluster_data_disk_stat.stat.exists

- name: Set permissions on data directory
  ansible.builtin.file:
    path: "{{ os_cluster_data_mount_point }}"
    owner: "{{ os_cluster_user }}"
    group: opensearch
    mode: '0755'
    recurse: false

- name: Update package cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600
  when: ansible_os_family == "Debian"

- name: Install Java packages
  ansible.builtin.package:
    name: "{{ os_cluster_java_packages }}"
    state: present
  when: ansible_os_family == "Debian"

- name: Install Java packages (RedHat family)
  ansible.builtin.package:
    name: "{{ os_cluster_java_packages }}"
    state: present
  when: ansible_os_family == "RedHat"

- name: Disable SELinux
  ansible.posix.selinux:
    state: disabled
  when:
    - ansible_os_family == "RedHat"
    - ansible_selinux.status is defined
    - ansible_selinux.status != "disabled"

- name: Populate the nodes to /etc/hosts
  ansible.builtin.import_tasks: etchosts.yml

- name: Tune the system settings
  ansible.builtin.import_tasks: tune.yml

- name: Include opensearch installation
  ansible.builtin.import_tasks: opensearch.yml

- name: Include security plugin for opensearch
  ansible.builtin.import_tasks: security.yml

# After the cluster forms successfully for the first time,
# remove the cluster.initial_master_nodes setting from each nodes' configuration.
- name: Remove `cluster.initial_master_nodes` setting from configuration
  ansible.builtin.lineinfile:
    path: "{{ os_cluster_conf_dir }}/opensearch.yml"
    regexp: '^cluster\.initial_master_nodes'
    state: absent

- name: Start and enable opensearch service
  ansible.builtin.systemd:
    name: opensearch
    state: "{{ os_cluster_service_state }}"
    enabled: "{{ os_cluster_service_enabled }}"
    daemon_reload: true
  register: opensearch_service_result

- name: Wait for opensearch to startup
  ansible.builtin.wait_for:
    host: "{{ ansible_default_ipv4.address }}"
    port: "{{ os_cluster_api_port }}"
    delay: 10
    timeout: 300
    connect_timeout: 5

- name: Get installed OpenSearch plugins
  ansible.builtin.command: "{{ os_cluster_plugin_bin_path }} list"
  register: list_plugins
  changed_when: false
  failed_when: false

- name: Display installed OpenSearch plugins
  ansible.builtin.debug:
    msg: "{{ list_plugins.stdout }}"
  when: list_plugins.rc == 0

- name: Check OpenSearch cluster health
  ansible.builtin.uri:
    url: "https://{{ ansible_default_ipv4.address }}:{{ os_cluster_api_port }}/_cluster/health?pretty"
    method: GET
    user: admin
    password: "{{ os_cluster_admin_password }}"
    force_basic_auth: true
    validate_certs: false
    return_content: true
    timeout: 30
  register: os_status
  changed_when: false
  retries: 5
  delay: 10
  until: os_status.status == 200

- name: Display OpenSearch cluster status
  ansible.builtin.debug:
    msg: "{{ os_status.json }}"
  when: os_status.json is defined

- name: Verify OpenSearch cluster nodes and roles
  ansible.builtin.uri:
    url: "https://{{ ansible_default_ipv4.address }}:{{ os_cluster_api_port }}/_cat/nodes?v&h=name,node.role,master"
    method: GET
    user: admin
    password: "{{ os_cluster_admin_password }}"
    force_basic_auth: true
    validate_certs: false
    return_content: true
    timeout: 30
  register: os_roles
  run_once: true
  changed_when: false
  retries: 3
  delay: 5

- name: Display OpenSearch cluster nodes and roles
  ansible.builtin.debug:
    msg: "{{ os_roles.content }}"
  run_once: true
  when: os_roles.content is defined

# OpenSearch Dashboards installation and configuration
- name: Include OpenSearch Dashboards installation
  ansible.builtin.include_tasks: dashboards.yml
  when: os_cluster_dashboards_enabled | default(false)
  tags: dashboards
