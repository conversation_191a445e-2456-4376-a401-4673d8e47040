---
- name: Validate dashboards variables
  ansible.builtin.assert:
    that:
      - os_cluster_name is defined
      - os_cluster_dashboards_version is defined
      - os_cluster_download_url is defined
      - os_cluster_dashboards_user is defined
    fail_msg: "Required dashboards variables are not defined"
    success_msg: "All required dashboards variables are defined"
  tags: dashboards

- name: Create opensearch-dashboards group
  ansible.builtin.group:
    name: opensearch-dashboards
    state: present
  tags: dashboards

- name: Create opensearch-dashboards user
  ansible.builtin.user:
    name: "{{ os_cluster_dashboards_user }}"
    group: opensearch-dashboards
    system: true
    shell: /bin/bash
    home: "{{ os_cluster_dashboards_home }}"
    create_home: false
    state: present
  tags: dashboards

- name: Download OpenSearch Dashboards {{ os_cluster_dashboards_version }}
  ansible.builtin.get_url:
    url:
      "{{ os_cluster_download_url }}-dashboards/{{ os_cluster_dashboards_version }}/\
      opensearch-dashboards-{{ os_cluster_dashboards_version }}-linux-x64.tar.gz"
    dest: "/tmp/opensearch-dashboards-{{ os_cluster_dashboards_version }}.tar.gz"
    mode: '0644'
    timeout: 300
    backup: true
  register: os_cluster_dashboards_download_result
  tags: dashboards

- name: Create dashboards home directory
  ansible.builtin.file:
    path: "{{ os_cluster_dashboards_home }}"
    state: directory
    owner: "{{ os_cluster_dashboards_user }}"
    group: opensearch-dashboards
    mode: '0755'
  tags: dashboards

- name: Extract OpenSearch Dashboards tar file
  ansible.builtin.unarchive:
    src: "/tmp/opensearch-dashboards-{{ os_cluster_dashboards_version }}.tar.gz"
    dest: "{{ os_cluster_dashboards_home }}"
    remote_src: true
    owner: "{{ os_cluster_dashboards_user }}"
    group: opensearch-dashboards
    extra_opts: [--strip-components=1]
    creates: "{{ os_cluster_dashboards_home }}/bin/opensearch-dashboards"
  notify: Restart opensearch dashboards
  tags: dashboards

- name: Copy OpenSearch Dashboards configuration file
  ansible.builtin.template:
    src: opensearch_dashboards.yml.j2
    dest: "{{ os_cluster_dashboards_conf_dir }}/opensearch_dashboards.yml"
    owner: "{{ os_cluster_dashboards_user }}"
    group: opensearch-dashboards
    mode: '0644'
    backup: true
  notify: Restart opensearch dashboards
  tags: dashboards

- name: Create systemd service file for dashboards
  ansible.builtin.template:
    src: opensearch-dashboards.service.j2
    dest: "{{ os_cluster_systemctl_path }}/opensearch-dashboards.service"
    mode: '0644'
    backup: true
  notify:
    - Reload systemd
    - Restart opensearch dashboards
  tags: dashboards

- name: Set permissions on dashboards home directory
  ansible.builtin.file:
    path: "{{ os_cluster_dashboards_home }}"
    owner: "{{ os_cluster_dashboards_user }}"
    group: opensearch-dashboards
    recurse: true
    mode: '0755'
  tags: dashboards

- name: Start and enable opensearch dashboards service
  ansible.builtin.systemd:
    name: opensearch-dashboards
    state: "{{ os_cluster_service_state }}"
    enabled: "{{ os_cluster_service_enabled }}"
    daemon_reload: true
  register: os_cluster_dashboards_service_result
  tags: dashboards

- name: Wait for OpenSearch Dashboards to startup
  ansible.builtin.wait_for:
    host: "{{ os_cluster_dashboards_host }}"
    port: "{{ os_cluster_dashboards_port }}"
    delay: 10
    timeout: 300
    connect_timeout: 5
  tags: dashboards

- name: Get installed OpenSearch Dashboards plugins
  ansible.builtin.command: "{{ os_cluster_dashboards_plugin_bin_path }} list"
  register: os_cluster_dashboards_plugins_list
  changed_when: false
  failed_when: false
  become: true
  become_user: "{{ os_cluster_dashboards_user }}"
  tags: dashboards

- name: Display installed OpenSearch Dashboards plugins
  ansible.builtin.debug:
    msg: "{{ os_cluster_dashboards_plugins_list.stdout }}"
  when: os_cluster_dashboards_plugins_list.rc == 0
  tags: dashboards

- name: Verify OpenSearch Dashboards is accessible
  ansible.builtin.uri:
    url: "http://{{ os_cluster_dashboards_host }}:{{ os_cluster_dashboards_port }}/api/status"
    method: GET
    timeout: 30
    status_code: [200, 401, 403]
  register: os_cluster_dashboards_status
  retries: 5
  delay: 10
  until: os_cluster_dashboards_status.status in [200, 401, 403]
  tags: dashboards

- name: Display OpenSearch Dashboards status
  ansible.builtin.debug:
    msg: "OpenSearch Dashboards is accessible at http://{{ os_cluster_dashboards_host }}:{{ os_cluster_dashboards_port }}"
  when: os_cluster_dashboards_status.status in [200, 401, 403]
  tags: dashboards
