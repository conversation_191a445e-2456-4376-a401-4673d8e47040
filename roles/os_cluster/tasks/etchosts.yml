---
- name: Hosts | populate inventory into hosts file
  ansible.builtin.blockinfile:
    dest: /etc/hosts
    block: |-
      {% for item in ansible_play_hosts_all %}
      {{ hostvars[item]['ansible_default_ipv4']['address'] }} {{ item }}.{{ os_cluster_domain_name }} {{ item }}
      {% endfor %}
    state: present
    create: true
    backup: true
    marker: "# Ansible inventory hosts {mark}"
    mode: '0644'
  when: os_cluster_populate_inventory_to_hosts_file
