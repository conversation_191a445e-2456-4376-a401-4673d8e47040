---
- name: OpenSearch Install | Download opensearch {{ os_cluster_version }}
  ansible.builtin.get_url:
    url: "{{ os_cluster_download_url }}/{{ os_cluster_version }}/opensearch-{{ os_cluster_version }}-linux-x64.tar.gz"
    dest: "/tmp/opensearch-{{ os_cluster_version }}.tar.gz"
    mode: '0644'
    timeout: 300
    backup: true
  register: download_result

- name: OpenSearch Install | Create home directory
  ansible.builtin.file:
    path: "{{ os_cluster_home }}"
    state: directory
    owner: "{{ os_cluster_user }}"
    group: "{{ os_cluster_user }}"
    mode: '0755'

- name: OpenSearch Install | Extract the tar file
  ansible.builtin.unarchive:
    src: "/tmp/opensearch-{{ os_cluster_version }}.tar.gz"
    dest: "{{ os_cluster_home }}"
    remote_src: true
    owner: "{{ os_cluster_user }}"
    group: "{{ os_cluster_user }}"
    extra_opts: [--strip-components=1]
    creates: "{{ os_cluster_home }}/bin/opensearch"
  notify: Restart opensearch

- name: OpenSearch Install | Copy Configuration File
  ansible.builtin.template:
    src: "opensearch-{{ os_cluster_type }}.yml.j2"
    dest: "{{ os_cluster_conf_dir }}/opensearch.yml"
    owner: "{{ os_cluster_user }}"
    group: "{{ os_cluster_user }}"
    mode: '0600'
    backup: true
  notify: Restart opensearch

- name: OpenSearch Install | Copy jvm.options File for Instance
  ansible.builtin.template:
    src: jvm.options
    dest: "{{ os_cluster_conf_dir }}/jvm.options"
    owner: "{{ os_cluster_user }}"
    group: "{{ os_cluster_user }}"
    mode: '0600'
    backup: true
  notify: Restart opensearch

- name: OpenSearch Install | Create systemd service file
  ansible.builtin.template:
    src: opensearch.service
    dest: "{{ os_cluster_systemctl_path }}/opensearch.service"
    mode: '0644'
    backup: true
  notify:
    - Reload systemd
    - Restart opensearch

- name: OpenSearch Install | Set system limits for opensearch user
  community.general.pam_limits:
    domain: "{{ item.domain }}"
    limit_type: "{{ item.limit_type }}"
    limit_item: "{{ item.limit_item }}"
    value: "{{ item.value }}"
  loop: "{{ os_cluster_limits }}"
  notify: Restart opensearch

- name: OpenSearch Install | create systemd service
  ansible.builtin.template:
    src: opensearch.service
    dest: "{{ os_cluster_systemctl_path }}/opensearch.service"
    mode: 0644
