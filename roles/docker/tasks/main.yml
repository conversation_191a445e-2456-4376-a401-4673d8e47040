---
- name: Install prerequisites for Docker repository
  ansible.builtin.apt:
    name: "{{ item }}"
    update_cache: true
  with_items: "{{ docker_requirements }}"
  tags: docker

- name: Add Docker GPG key
  ansible.builtin.apt_key:
    url: "{{ docker_apt_gpg_key }}"
  tags: docker

- name: Set the stable repository
  ansible.builtin.apt_repository:
    repo: "{{ docker_apt_repository }}"
  tags: docker

- name: Configure sysctl
  ansible.posix.sysctl:
    name: net.ipv4.ip_forward
    value: 1
    state: present
    reload: true
  tags: docker

- name: Install Docker
  ansible.builtin.apt:
    name: "{{ item }}"
    update_cache: true
  with_items: "{{ docker_packages }}"
  when: ansible_distribution == "Ubuntu" or ansible_distribution == "Debian"
  tags: docker

- name: Create daemon.json
  ansible.builtin.template:
    src: daemon.json.j2
    dest: "{{ docker_config_file }}"
    mode: u=rw,g=r,o=r
  tags: docker
  notify: Docker config change

- name: Create link for docker-compose
  ansible.builtin.file:
    src: "{{ docker_compose_plugin_dir }}"
    dest: "{{ docker_compose_bin_dir }}"
    state: link
    force: true
  when: ansible_distribution == "Ubuntu" or ansible_distribution == "Debian"
  tags: docker
