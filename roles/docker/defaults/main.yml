---
docker_requirements:
  - apt-transport-https
  - ca-certificates
  - curl
  - gnupg2
  - software-properties-common
  - gnupg
  - lsb-release

docker_packages:
  - containerd.io
  - docker-ce
  - docker-ce-cli

docker_repo_url: https://download.docker.com/{{ ansible_system | lower }}

docker_apt_gpg_key: "{{ docker_repo_url }}/{{ ansible_distribution | lower }}/gpg"
docker_apt_repository: "deb [arch=amd64] {{ docker_repo_url }}/{{ ansible_distribution | lower }} {{ ansible_distribution_release }} stable"

docker_compose_plugin_dir: /usr/libexec/docker/cli-plugins/docker-compose
docker_compose_bin_dir: /usr/local/bin/docker-compose

docker_log_driver: local
docker_log_opts_max_size: "500m"
docker_log_opts_max_file: "5"
docker_config_file: "/etc/docker/daemon.json"
