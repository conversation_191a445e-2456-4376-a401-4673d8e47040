---
# - name: Install Dependencies CentOS
#   yum:
#     name: systemd-resolved
#     state: present
#   when: ansible_distribution == "CentOS" or ansible_distribution == "RedHat"

- name: Configure systemd-resolved
  ansible.builtin.template:
    src: resolved.conf.j2
    dest: /etc/systemd/resolved.conf
    owner: root
    group: root
    mode: 0644
  register: __systemd_resolved_configuration
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"

- name: Enable systemd-resolved service and assure it is started
  ansible.builtin.systemd:
    name: systemd-resolved
    enabled: true
    state: >-
      {{
        __systemd_resolved_configuration.changed |
          default(False) |
          ternary("restarted", "started")
      }}
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"

- name: Link /etc/resolv.conf to stub
  ansible.builtin.file:
    src: /run/systemd/resolve/stub-resolv.conf
    dest: /etc/resolv.conf
    state: link
    force: true
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"
