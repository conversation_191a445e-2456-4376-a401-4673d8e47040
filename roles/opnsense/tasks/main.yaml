---
- name: Manage OPNsense firewall aliases
  module_defaults:
    group/ansibleguy.opnsense.all:
      firewall: "{{ ansible_host }}"
      api_key: "{{ opnsense_connect_api_key }}"
      api_secret: "{{ opnsense_connect_api_secret }}"
      ssl_verify: false

  block:
    - name: Add groups to FreeIPA for manage user's aliases
      community.general.ipa_group:
        description: "OPNsense user's firewall alias {{ item }}"
        name: "fw_{{ item }}"
        state: present
        append: true
        ipa_host: "{{ ipa_server_hostname }}.sl.local"
        ipa_user: "{{ ipa_admin_user }}"
        ipa_pass: "{{ admin_password }}"
      loop: "{{ opnsense_firewall_aliases_user }}"
      delegate_to: localhost
      run_once: true

    - name: Pulling aliases
      ansibleguy.opnsense.list:
        target: 'alias'
      register: existing_aliases
      delegate_to: localhost

    - name: Create LDAP managed user's aliases
      ansibleguy.opnsense.alias:
        name: '{{ item }}'
        description: LDAP managed by group fw_{{ item }}
        reload: true
      loop: "{{ opnsense_firewall_aliases_user | difference(existing_aliases.data | map(attribute='name') | list) }}"
      delegate_to: localhost
