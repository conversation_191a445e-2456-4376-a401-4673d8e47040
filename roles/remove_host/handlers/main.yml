---
# handlers file for roles/remove_host
- name: Stop the Bareos director container
  delegate_to: "{{ groups.bareos[0] }}"
  become: true
  become_user: root
  tags: bareos
  community.docker.docker_container:
    name: 'bareos_bareos-dir_1'
    state: stopped

- name: Start the Bareos director container
  delegate_to: "{{ groups.bareos[0] }}"
  become: true
  become_user: root
  tags: bareos
  community.docker.docker_container:
    name: 'bareos_bareos-dir_1'
    state: started

- name: Restart DHCP service
  delegate_to: hz-prod-nat-node-01
  become: true
  become_user: root
  tags: dhcp
  ansible.builtin.service:
    name: isc-dhcp-server
    state: restarted

- name: Handle files removal
  delegate_to: "{{ groups.bareos[0] }}"
  become: true
  become_user: root
  tags: bareos
  ansible.builtin.debug:
    msg: "Files related to the host have been removed."
  when: files_removed.changed
