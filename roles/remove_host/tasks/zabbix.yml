---
- name: Get Zabbix API token
  ansible.builtin.uri:
    url: "https://{{ zabbix_server_web }}/api_jsonrpc.php"
    method: POST
    headers:
      Content-Type: "application/json"
    body: |
      {
        "jsonrpc": "2.0",
        "method": "user.login",
        "params": {
          "username": "{{ zabbix_user_ansible }}",
          "password": "{{ zabbix_user_ansible_password }}"
        },
        "id": 1
      }
    body_format: json
    return_content: true
    validate_certs: false
  register: login_response
  tags: zabbix

- name: Get host ID from Zabbix
  ansible.builtin.uri:
    url: "https://{{ zabbix_server_web }}/api_jsonrpc.php"
    method: POST
    headers:
      Content-Type: "application/json"
    body: |
      {
        "jsonrpc": "2.0",
        "method": "host.get",
        "params": {
          "filter": {
            "host": [
              "{{ remove_host_name }}"
            ]
          }
        },
        "id": 2,
        "auth": "{{ login_response.json.result }}"
      }
    body_format: json
    return_content: true
    validate_certs: false
  register: host_response
  tags: zabbix

- name: Remove host from Zabbix
  ansible.builtin.uri:
    url: "https://{{ zabbix_server_web }}/api_jsonrpc.php"
    method: POST
    headers:
      Content-Type: "application/json"
    body: |
      {
        "jsonrpc": "2.0",
        "method": "host.delete",
        "params": [
          "{{ host_response.json.result[0].hostid }}"
        ],
        "id": 3,
        "auth": "{{ login_response.json.result }}"
      }
    body_format: json
    return_content: true
    validate_certs: false
  when: host_response.json.result | length > 0
  tags: zabbix
