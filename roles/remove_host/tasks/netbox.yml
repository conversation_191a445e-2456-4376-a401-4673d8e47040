---
- name: Find IP address in Netbox
  tags: netbox
  ansible.builtin.uri:
    url: "{{ netbox_url }}/api/ipam/ip-addresses/?dns_name={{ remove_host_name }}.sl.local."
    method: GET
    headers:
      Authorization: "Token {{ netbox_token }}"
    return_content: true
    validate_certs: false

  register: netbox_ip_response

- name: Remove IP address from Netbox
  tags: netbox
  ansible.builtin.uri:
    url: "{{ item.url }}"
    method: DELETE
    headers:
      Authorization: "Token {{ netbox_token }}"
    status_code: 204
    validate_certs: false
  loop: "{{ netbox_ip_response.json.results }}"
  when: netbox_ip_response.json.count > 0
