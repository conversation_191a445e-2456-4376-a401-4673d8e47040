---
# tasks file for roles/remove_host
- name: <PERSON><PERSON>ve from Bareos
  ansible.builtin.include_tasks: bareos.yml
  tags: bareos

- name: Remove host from NFS
  ansible.builtin.include_tasks: nfs.yml
  tags: nfs

- name: Remove host from Netbox
  ansible.builtin.include_tasks: netbox.yml
  tags: netbox

- name: Remove host from FreeIPA
  ansible.builtin.include_tasks: freeipa.yml
  tags: freeipa

- name: Remove host from Proxmox
  ansible.builtin.include_tasks: proxmox.yml
  tags: proxmox

- name: Remove host from PMM
  ansible.builtin.include_tasks: pmm.yml
  tags: pmm

- name: Remove host from Wazuh
  ansible.builtin.include_tasks: wazuh.yml
  tags: wazuh

- name: Remove host from Zabbix
  ansible.builtin.include_tasks: zabbix.yml
  tags: zabbix
