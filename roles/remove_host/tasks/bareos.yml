---
- name: Ensure directories and find files related to the host
  delegate_to: "{{ groups.bareos[0] }}"
  become: true
  become_user: root
  tags: bareos
  block:
    - name: Ensure client directory exists
      ansible.builtin.stat:
        path: /data/bareos/config/director/bareos-dir.d/client
      register: client_dir

    - name: Ensure job directory exists
      ansible.builtin.stat:
        path: /data/bareos/config/director/bareos-dir.d/job
      register: job_dir

    - name: Find client files related to the host
      ansible.builtin.find:
        paths:
          - /data/bareos/config/director/bareos-dir.d/client
        patterns: "*{{ remove_host_name }}*"
      when: client_dir.stat.exists
      register: client_files

    - name: Find job files related to the host
      ansible.builtin.find:
        paths:
          - /data/bareos/config/director/bareos-dir.d/job
        patterns: "*{{ remove_host_name }}*"
      when: job_dir.stat.exists
      register: job_files

- name: Get existing tasks from TaskShed
  ansible.builtin.uri:
    url: "{{ taskshed_url_tasks }}"
    method: GET
    return_content: true
  register: taskshed_tasks

- name: Remove tasks related to the host from TaskShed
  when: taskshed_tasks.json is defined
  block:
    - name: Find tasks related to the host
      ansible.builtin.set_fact:
        tasks_to_remove: "{{ taskshed_tasks.json | selectattr('hosts', 'equalto', remove_host_name) | list }}"

    - name: Remove tasks from TaskShed
      ansible.builtin.uri:
        url: "{{ taskshed_url_tasks }}/{{ item.id }}"
        method: DELETE
        status_code: 204
      loop: "{{ tasks_to_remove }}"
      loop_control:
        loop_var: item

- name: Remove files related to the host
  delegate_to: "{{ groups.bareos[0] }}"
  become: true
  become_user: root
  notify:
    - Handle files removal
    - Stop the Bareos director container
    - Start the Bareos director container
  tags: bareos
  block:
    - name: Remove client files related to the host
      ansible.builtin.file:
        path: "{{ item.path }}"
        state: absent
      loop: "{{ client_files.files }}"
      when: client_files.matched > 0

    - name: Remove job files related to the host
      ansible.builtin.file:
        path: "{{ item.path }}"
        state: absent
      loop: "{{ job_files.files }}"
      when: job_files.matched > 0
      register: files_removed
