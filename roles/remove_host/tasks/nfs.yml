---
- name: Mount a temporary NFS volume
  delegate_to: "{{ remove_host_name }}"
  tags: nfs
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}"
    path: /mnt/backuptmp
    opts: vers={{ nfs_mount_version }},rw,sync,soft,intr
    state: mounted
    fstype: nfs
  become: true

- name: Check if backup directory exists
  delegate_to: "{{ remove_host_name }}"
  tags: nfs
  ansible.builtin.stat:
    path: "/mnt/backuptmp/{{ remove_host_name }}"
  register: backup_dir_stat
  become: true

- name: Check if archive directory exists
  delegate_to: "{{ remove_host_name }}"
  tags: nfs
  ansible.builtin.stat:
    path: "/mnt/backuptmp/archive"
  register: archive_dir_stat
  become: true

- name: Create archive directory on NFS
  delegate_to: "{{ remove_host_name }}"
  tags: nfs
  ansible.builtin.file:
    path: "/mnt/backuptmp/archive"
    state: directory
    mode: '0755'
  when: not archive_dir_stat.stat.exists
  become: true

- name: Move backup directory to archive
  delegate_to: "{{ remove_host_name }}"
  tags: nfs
  ansible.builtin.command:
    cmd: mv "/mnt/backuptmp/{{ remove_host_name }}" "/mnt/backuptmp/archive/{{ remove_host_name }}"
  when: backup_dir_stat.stat.exists
  become: true
  changed_when: false

- name: Unmount the temporary NFS volume
  delegate_to: "{{ remove_host_name }}"
  tags: nfs
  ansible.posix.mount:
    src: "{{ bareos_backup_nfs_source }}"
    path: /mnt/backuptmp
    opts: vers={{ nfs_mount_version }},rw,sync,soft,intr
    state: absent
    fstype: nfs
  become: true
