---
- name: Find host in PMM
  tags: pmm
  ansible.builtin.uri:
    url: "https://{{ pmm_server_ip }}/v1/inventory/nodes"
    method: GET
    headers:
      Authorization: "Bearer {{ pmm_api_key }}"
    return_content: true
    validate_certs: false
  register: pmm_nodes

- name: Set facts for PMM node ID from container nodes
  tags: pmm
  ansible.builtin.set_fact:
    pmm_node_id: "{{ (pmm_nodes.json.container | selectattr('node_name', 'equalto', remove_host_name) | list).0.node_id }}"
  when: pmm_nodes.json.container | selectattr('node_name', 'equalto', remove_host_name) | list | length > 0

- name: Remove host from PMM
  tags: pmm
  ansible.builtin.uri:
    url: "https://{{ pmm_server_ip }}/v1/inventory/nodes/{{ pmm_node_id }}?force=true"
    method: DELETE
    headers:
      Authorization: "Bearer {{ pmm_api_key }}"
      Content-Type: "application/json"
    return_content: true
    validate_certs: false
  when: pmm_node_id is defined
