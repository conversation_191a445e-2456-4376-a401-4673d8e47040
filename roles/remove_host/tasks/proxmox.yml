---
- name: Authenticate with Proxmox API
  tags: proxmox
  ansible.builtin.uri:
    url: "https://{{ proxmox_host }}:8006/api2/json/access/ticket"
    method: POST
    body_format: form-urlencoded
    body:
      username: "{{ proxmox_user }}"
      password: "{{ proxmox_password }}"
    headers:
      Content-Type: "application/x-www-form-urlencoded"
    validate_certs: false
  register: proxmox_auth

- name: Find all nodes in Proxmox
  tags: proxmox
  ansible.builtin.uri:
    url: "https://{{ proxmox_host }}:8006/api2/json/nodes"
    method: GET
    headers:
      Cookie: "PVEAuthCookie={{ proxmox_auth.json.data.ticket }}"
      CSRFPreventionToken: "{{ proxmox_auth.json.data.CSRFPreventionToken }}"
    validate_certs: false
  register: proxmox_nodes

- name: Find VM on each node
  tags: proxmox
  community.general.proxmox_kvm:
    node: "{{ item.node }}"
    api_user: "{{ proxmox_user }}"
    api_password: "{{ proxmox_password }}"
    api_host: "{{ proxmox_host }}"
    validate_certs: false
    name: "{{ remove_host_name }}"
    state: current
  loop: "{{ proxmox_nodes.json.data }}"
  register: proxmox_vm_info
  ignore_errors: true

- name: Get VM ID and node from Proxmox
  tags: proxmox
  ansible.builtin.set_fact:
    proxmox_vm_id: "{{ item.vmid }}"
    proxmox_node: "{{ item.invocation.module_args.node }}"
  loop: "{{ proxmox_vm_info.results }}"
  when: item.vmid is defined

- name: Debug
  tags: proxmox
  ansible.builtin.debug:
    msg: "VM ID: {{ proxmox_vm_id }}"
  when: proxmox_vm_id is defined

- name: Ensure the VM is stopped
  tags: proxmox
  community.general.proxmox_kvm:
    node: "{{ proxmox_node }}"
    name: "{{ remove_host_name }}"
    api_user: "{{ proxmox_user }}"
    api_password: "{{ proxmox_password }}"
    api_host: "{{ proxmox_host }}"
    validate_certs: false
    state: stopped
    timeout: 3600
  when: proxmox_vm_id is defined

- name: Wait for the VM to be fully stopped
  tags: proxmox
  ansible.builtin.pause:
    seconds: 10
  when: proxmox_vm_id is defined

- name: Get VM current state
  tags: proxmox
  community.general.proxmox_kvm:
    node: "{{ proxmox_node }}"
    name: "{{ remove_host_name }}"
    api_user: "{{ proxmox_user }}"
    api_password: "{{ proxmox_password }}"
    api_host: "{{ proxmox_host }}"
    validate_certs: false
    state: current
  register: vm_state
  when: proxmox_vm_id is defined

- name: Debug VM state
  tags: proxmox
  ansible.builtin.debug:
    var: vm_state
  when: proxmox_vm_id is defined

- name: Wait for the VM to be fully stopped
  tags: proxmox
  community.general.proxmox_kvm:
    node: "{{ proxmox_node }}"
    name: "{{ remove_host_name }}"
    api_user: "{{ proxmox_user }}"
    api_password: "{{ proxmox_password }}"
    api_host: "{{ proxmox_host }}"
    validate_certs: false
    state: current
  register: vm_state
  until: vm_state.status == "stopped"
  retries: 5
  delay: 10
  when: proxmox_vm_id is defined

- name: Remove the VM from Proxmox
  tags: proxmox
  community.general.proxmox_kvm:
    node: "{{ proxmox_node }}"
    name: "{{ remove_host_name }}"
    api_user: "{{ proxmox_user }}"
    api_password: "{{ proxmox_password }}"
    api_host: "{{ proxmox_host }}"
    validate_certs: false
    state: absent
    timeout: 3600
  when: proxmox_vm_id is defined
