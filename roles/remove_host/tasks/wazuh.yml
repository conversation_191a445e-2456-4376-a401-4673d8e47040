---
- name: Get Wazuh API token
  ansible.builtin.uri:
    url: "https://{{ wazuh_manager_ip }}:55000/security/user/authenticate?raw=true"
    method: POST
    headers:
      Content-Type: "application/json"
      Authorization: "Basic {{ (wazuh_api_user ~ ':' ~ wazuh_api_password) | b64encode }}"
    return_content: true
    validate_certs: false
  register: wazuh_login_response
  tags: wazuh

- name: Get agent ID from Wazuh
  ansible.builtin.uri:
    url: "https://{{ wazuh_manager_ip }}:55000/agents?name={{ remove_host_name }}.{{ ipa_domain }}"
    method: GET
    headers:
      Content-Type: "application/json"
      Authorization: "Bearer {{ wazuh_login_response.content }}"
    return_content: true
    validate_certs: false
  register: wazuh_agent_response
  tags: wazuh

- name: Debug Wazuh agent response
  ansible.builtin.debug:
    var: wazuh_agent_response
  tags: wazuh

- name: Set URL for deleting agent
  ansible.builtin.set_fact:
    delete_agent_url:
      "{{ 'https://%s:55000/agents?pretty=true&older_than=0s&agents_list=%s&status=all'
      % (wazuh_manager_ip, wazuh_agent_response.json.data.affected_items[0].id) }}"
  tags: wazuh
  when: wazuh_agent_response.json.data.affected_items | length > 0

- name: Remove agent from Wazuh
  ansible.builtin.uri:
    url: "{{ delete_agent_url }}"
    method: DELETE
    headers:
      Content-Type: "application/json"
      Authorization: "Bearer {{ wazuh_login_response.content }}"
    return_content: true
    validate_certs: false
  when: wazuh_agent_response.json.data.affected_items | length > 0
  tags: wazuh
