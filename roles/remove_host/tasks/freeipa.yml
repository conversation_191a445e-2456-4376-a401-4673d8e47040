---
- name: Remove host from FreeIPA
  tags: freeipa
  community.general.ipa_host:
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    name: "{{ remove_host_name }}.sl.local"
    update_dns: true
    state: absent
    validate_certs: false

- name: Remove HBAC rule from FreeIPA
  tags: freeipa
  community.general.ipa_hbacrule:
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    name: "access_to_{{ remove_host_name }}"
    state: absent
    validate_certs: false
