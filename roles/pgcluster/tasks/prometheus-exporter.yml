---
- name: Create a postgres-exporter docker container
  community.docker.docker_container:
    name: postgres-exporter
    image: quay.io/prometheuscommunity/postgres-exporter:{{ pgcluster_postgres_prom_exporter_version }}
    network_mode: host
    restart: true
    restart_policy: always
    cpu_period: "{{ pgcluster_postgres_prom_exporter_cpu_period }}"
    cpu_quota: "{{ pgcluster_postgres_prom_exporter_cpu_quota }}"
    memory: "{{ pgcluster_postgres_prom_exporter_memory }}"
    env:
      DATA_SOURCE_NAME: "postgresql://{{ pgcluster_user }}:{{ pass_pg_postgres }}@\
        {{ pgcluster_prometheus_exporter_host }}:\
        {{ pgcluster_port }}/{{ pgcluster_user }}?sslmode=disable"
