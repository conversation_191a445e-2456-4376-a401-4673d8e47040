---
- name: Pgcluster role
  module_defaults:
    ansible.builtin.apt:
      cache_valid_time: 86400
  block:
    - name: Check some requrements and pass preparation
      ansible.builtin.import_tasks: common-preparation.yml
      tags: always

    - name: Preparation node to deploy patroni
      ansible.builtin.import_tasks: node-preparation.yml
      tags: node

    - name: Setup consul node
      ansible.builtin.import_tasks: setup-consul.yml
      tags: consul

    - name: Setup postgresql node
      ansible.builtin.import_tasks: install-postgresql.yml
      tags: postgre

    - name: Install pg_stat_monitor extension for postgresql
      ansible.builtin.import_tasks: install-pg-stat-monitor.yml
      tags: pg_stat_monitor
      when: pgcluster_pg_stat_monitor

    - name: Install pgBackRest
      ansible.builtin.import_tasks: install-pgbackrest.yml
      tags: pgbackrest

    - name: Include backup role
      ansible.builtin.include_role:
        name: bareos-backup
      when: "'-03' in inventory_hostname and pgcluster_backup == 'true'"
      tags:
        - backup
        - skip_ansible_lint

    - name: Pause for manually support installation during recovery for manually helthcheck
      ansible.builtin.pause:
        minutes: 999999
      tags:
        - never
        - wait

    - name: Setup patroni node
      ansible.builtin.include_tasks: install-patroni.yml
      tags:
        - patroni
        - patroni-config
        - patroni-restart
        - patroni-install

    - name: Pause for manually support installation during recovery for manually helthcheck
      ansible.builtin.pause:
        minutes: 999999
      tags:
        - never
        - wait

    - name: Wait to initialize cluster before stanza create for backup
      ansible.builtin.pause:
        seconds: 120
      tags: wait

    - name: Prepare pgbackrest
      ansible.builtin.import_tasks: prepare_pgbackrest.yml
      tags: prepare_pgbackrest

    - name: Setup haproxy
      ansible.builtin.import_tasks: install-haproxy.yml
      tags:
        - haproxy
        - install_haproxy

    - name: Setup keepalived
      ansible.builtin.import_tasks: install-keepalived.yml
      tags: keepalived

    - name: Setup haproxy templates
      ansible.builtin.import_tasks: install-consul-template.yml
      tags:
        - haproxy
        - template_haproxy

    - name: Pause for manually support installation during recovery for manually helthcheck
      ansible.builtin.pause:
        minutes: 999999
      tags:
        - never
        - wait

    - name: Setup pgbouncer
      ansible.builtin.include_tasks: pgbouncer.yml
      tags:
        - pgbouncer
        - pgbouncer-config
      when: pgcluster_pgbouncer_app_user is defined

    - name: Setup postgres-exporter for prometheus
      ansible.builtin.import_tasks: prometheus-exporter.yml
      tags: exporter


    - name: Create extention pg_stat_statements
      ansible.builtin.import_tasks: pg-stat-statements.yml
      tags: pg-stat-statements

    - name: Ldap preparation
      ansible.builtin.import_tasks: ldap-preparation.yml
      tags:
        - ldap
        - ldap_preparation

    - name: Ldap2pg installation
      ansible.builtin.import_tasks: install-ldap2pg.yml
      tags:
        - ldap
        - ldap2pg
        - ldap2pg_install

    - name: Ldap2pg configuration
      ansible.builtin.import_tasks: ldap2pg-configuration.yml
      tags:
        - ldap
        - ldap2pg
        - ldap2pg_config

    - name: Ldap2pg cron
      ansible.builtin.import_tasks: ldap2pg-cron.yml
      tags:
        - ldap
        - ldap2pg
        - ldap2pg_cron

    - name: Create users and groups in LDAP
      ansible.builtin.import_tasks: ldap-objects.yml
      tags:
        - ldap
        - ldap_objects

    - name: Setup pmm client
      ansible.builtin.import_tasks: pmm-client.yml
      tags: pmm-client

    - name: Zabbix monitoring
      ansible.builtin.import_tasks: zabbix.yml
      tags: zabbix

    - name: Setup pgbouncer exporter
      ansible.builtin.import_tasks: pgbouncer-exporter.yml
      tags: pgbouncer-exporter
      when: pgcluster_pgbouncer_app_user is defined

    - name: Setup consul-exporter
      ansible.builtin.import_tasks: consul-exporter.yml
      tags: consul-exporter

    - name: Setup pgluster-exporter
      ansible.builtin.import_tasks: pgcluster-exporter.yml
      tags: pgcluster-exporter
