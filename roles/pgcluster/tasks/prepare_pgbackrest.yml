---
- name: Create a directory for key if it does not exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0700'
    owner: "{{ pgcluster_user }}"
    group: "{{ pgcluster_user }}"
  loop:
    - /var/lib/postgresql/.ssh
    - /backup

- name: Create key for postgres OS user
  community.crypto.openssh_keypair:
    path: /var/lib/postgresql/.ssh/id_rsa
  become: true
  become_user: "{{ pgcluster_user }}"
  register: new_pub_key

- name: Check pub key for postgres OS user
  ansible.builtin.slurp:
    src: /var/lib/postgresql/.ssh/id_rsa.pub
  register: rsa_key
  delegate_to: "{{ item }}"
  loop: "{{ groups[postgres_db_env + '_' + postgres_cluster_name + '_' + 'pgcluster'] }}"

- name: Msg new key
  ansible.builtin.debug:
    msg: "{{ rsa_key }}"

- name: Set authorized key taken from file
  ansible.posix.authorized_key:
    user: "{{ pgcluster_user }}"
    state: present
    key: "{{ item['content'] | b64decode }}"
  loop: "{{ rsa_key['results'] }}"

- name: Msg set known_hosts
  ansible.builtin.debug:
    msg: "{{ hostvars[item]['ansible_host'] }}"
  loop: "{{ groups[postgres_db_env + '_' + postgres_cluster_name + '_' + 'pgcluster'] }}"
  become: true
  become_user: "{{ pgcluster_user }}"

- name: Set known_hosts
  ansible.builtin.shell: ssh-keyscan -p {{ infra_ssh_port }} -H {{ hostvars[item]['ansible_host'] }} >> ~/.ssh/known_hosts
  loop: "{{ groups[postgres_db_env + '_' + postgres_cluster_name + '_' + 'pgcluster'] }}"
  become: true
  become_user: "{{ pgcluster_user }}"
  changed_when: true
  tags: known_hosts

- name: Create a directory for log PGBackRest if it does not exist
  ansible.builtin.file:
    path: /var/log/pgbackrest
    state: directory
    mode: '0774'
    owner: "{{ pgcluster_user }}"
    group: "{{ pgcluster_user }}"

- name: Create stanza for pgBackRest
  ansible.builtin.command:
    cmd: "/bin/pgbackrest --stanza={{ postgres_db_env + '_' + postgres_cluster_name + '_' + 'pgcluster' }} --log-level-file=detail stanza-create"
  when:
    - ( inventory_hostname | regex_search('\d$')) == '3'
  become_user: "{{ pgcluster_user }}"
  become: true
  changed_when: true
  tags: pgbackrest_stanza
