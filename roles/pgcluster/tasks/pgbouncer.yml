---
- name: Ansible block pgbouncer
  tags: pgbouncer
  block:
    - name: Install pgbouncer
      ansible.builtin.apt:
        name: pgbouncer
        state: latest

    - name: Create config from template and copy it destination node
      ansible.builtin.template:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        backup: true
        mode: "{{ item.mode }}"
        owner: "{{ item.owner }}"
        group: "{{ item.group }}"
      loop:
        - {src: pgbouncer.ini.j2, dest: /etc/pgbouncer/pgbouncer.ini, mode: '640', owner: postgres, group: postgres}
        - {src: userlist.txt.j2, dest: /etc/pgbouncer/userlist.txt, mode: '640', owner: postgres, group: postgres}
        - {src: pgbouncer.service.j2, dest: /lib/systemd/system/pgbouncer.service, mode: '644', owner: root, group: root}
      tags:
        - pgbouncer-config

    - name: Enable and restart pgbouncer
      ansible.builtin.systemd:
        name: pgbouncer
        state: restarted
        enabled: true
        daemon_reload: true
        masked: false

    - name: Block for second pgbouncer
      when: pgcluster_second_pgbouncer_install
      block:
        - name: Create config for second pgbouncer from template and copy it destination node
          ansible.builtin.template:
            src: "{{ item.src }}"
            dest: "{{ item.dest }}"
            backup: true
            mode: "{{ item.mode }}"
            owner: "{{ item.owner }}"
            group: "{{ item.group }}"
          loop:
            - {src: pgbouncer2.ini.j2, dest: /etc/pgbouncer/pgbouncer2.ini, mode: '640', owner: postgres, group: postgres}
            - {src: pgbouncer2.service.j2, dest: /lib/systemd/system/pgbouncer2.service, mode: '644', owner: root, group: root}
            - {src: userlist2.txt.j2, dest: /etc/pgbouncer/userlist2.txt, mode: '640', owner: postgres, group: postgres}
          tags:
            - pgbouncer-config
        - name: Enable and restart second pgbouncer
          ansible.builtin.systemd:
            name: pgbouncer2
            state: restarted
            enabled: true
            daemon_reload: true
            masked: false
