---
- name: Add postgres user zbx_monitor
  community.postgresql.postgresql_user:
    conn_limit: "{{ pgcluster_zbx_monitor_con_limit }}"
    name: "{{ pguser_zbx_monitor }}"
    port: "{{ pgcluster_vip_port }}"
    password: "{{ pguser_zbx_monitor_password }}"
    login_host: "{{ vip_address }}"
    login_password: "{{ hostvars['localhost']['goal_pass_pass_pg_postgres'] }}"
    login_user: "{{ pgcluster_user }}"
  run_once: true

- name: Grant privilegies to {{ pguser_zbx_monitor }}
  community.postgresql.postgresql_membership:
    db: "{{ pgcluster_user }}"
    login_host: "{{ vip_address }}"
    login_password: "{{ hostvars['localhost']['goal_pass_pass_pg_postgres'] }}"
    login_user: "{{ pgcluster_user }}"
    port: "{{ pgcluster_vip_port }}"
    groups:
      - pg_monitor
    target_roles:
      - "{{ pguser_zbx_monitor }}"
  run_once: true

- name: <PERSON> connect privilege to {{ pguser_zbx_monitor }}
  community.postgresql.postgresql_privs:
    database: "{{ pgcluster_user }}"
    login_host: "{{ vip_address }}"
    login_password: "{{ hostvars['localhost']['goal_pass_pass_pg_postgres'] }}"
    login_user: "{{ pgcluster_user }}"
    obj: "{{ pgcluster_user }}"
    port: "{{ pgcluster_vip_port }}"
    privs: CONNECT
    roles: "{{ pguser_zbx_monitor }}"
    type: database
  run_once: true

- name: Link template for PostgreSQL and Linux host in zabbix
  become: false
  community.zabbix.zabbix_host:
    host_name: "{{ item }}"
    link_templates:
      - PostgreSQL by Zabbix agent 2
      - Linux by Zabbix agent
    macros:
      - macro: '{$PG.URI}'
        value: "tcp://{{ pgcluster_prometheus_exporter_host }}:{{ pgcluster_port }}"
    interfaces:
      - type: agent
        main: 1
        useip: 1
        ip: '{{ hostvars[item]["ansible_host"] }}'
        port: "{{ zabbix_agent_port }}"
  loop: "{{ ansible_play_hosts_all }}"
  run_once: true
  tags: zabbix
  vars:
    ansible_network_os: community.zabbix.zabbix
    ansible_connection: httpapi
    ansible_httpapi_port: 443
    ansible_httpapi_use_ssl: true
    ansible_httpapi_validate_certs: false
    ansible_zabbix_url_path: ''
    ansible_user: "{{ zabbix_user_ansible }}"
    ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
  when: hostvars[item]['group_names'] | select('search', 'pgcluster') | list | length >= 1
  delegate_to: "{{ zabbix_server_web }}"
