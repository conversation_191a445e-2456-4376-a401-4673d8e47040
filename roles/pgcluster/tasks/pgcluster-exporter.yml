---
- name: Create pgcluster-exporter directory structure
  ansible.builtin.file:
    state: directory
    path: "{{ pgcluster_exporter_dir }}"
    owner: "root"
    group: "root"
    mode: '755'

- name: Download artifact archive
  ansible.builtin.get_url:
    url: "{{ pgcluster_exporter_download_url }}"
    dest: "{{ pgcluster_exporter_dir }}"
    mode: '644'
    headers:
      PRIVATE-TOKEN: "{{ pgcluster_exporter_gitlab_token }}"
  changed_when: false

- name: Unzip the downloaded package
  ansible.builtin.unarchive:
    src: "{{ pgcluster_exporter_dir }}/{{ pgcluster_exporter_archive_file }}"
    dest: "{{ pgcluster_exporter_dir }}"
    owner: "root"
    group: "root"
    copy: false

- name: Remove artifact archive
  ansible.builtin.file:
    path: "{{ pgcluster_exporter_dir }}/{{ pgcluster_exporter_archive_file }}"
    state: absent
  changed_when: false

- name: Copy pgcluster-exporter systemd service configuration
  ansible.builtin.template:
    backup: true
    src: "{{ pgcluster_exporter_name_service }}.service.j2"
    dest: "/etc/systemd/system/{{ pgcluster_exporter_name_service }}.service"
    mode: '755'

- name: Restart pgcluster-exporter sevice
  ansible.builtin.systemd:
    name: "{{ pgcluster_exporter_name_service }}"
    state: restarted
    enabled: true
