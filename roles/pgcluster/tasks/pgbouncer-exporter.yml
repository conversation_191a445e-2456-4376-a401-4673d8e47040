---
- name: Create a pgbouncer-exporter docker container
  community.docker.docker_container:
    name: pgbouncer-exporter
    image: prometheuscommunity/pgbouncer-exporter:{{ pgcluster_pgbouncer_exporter_version }}
    restart: true
    restart_policy: always
    ports:
      - 9127:9127
    command: "{{ pgcluster_pgbouncer_exporter_con_str }}"

- name: Create a second pgbouncer-exporter docker container
  community.docker.docker_container:
    name: pgbouncer-exporter_ro
    image: prometheuscommunity/pgbouncer-exporter:{{ pgcluster_pgbouncer_exporter_version }}
    restart: true
    restart_policy: always
    ports:
      - 9128:9127
    command: "{{ pgcluster_pgbouncer_exporter_con_str_ro }}"
  when: pgcluster_second_pgbouncer_install
