---

- name: <PERSON>reate LDAP config
  ansible.builtin.template:
    src: ldap.conf.j2
    dest: "/etc/ldap/ldap.conf"
    mode: '0644'
    owner: root
    group: root
    backup: true

- name: Install Dependencies
  ansible.builtin.apt:
    name: ['libldap2-dev', 'libsasl2-dev', 'python3-dev', 'python3-pip', 'python3-ldap', 'python3-pkg-resources', 'python3-psycopg2', 'python3-yaml']
    state: latest
    update_cache: true

- name: Install python requirements on Ubuntu 24.04
  ansible.builtin.pip:
    name:
      - psycopg2-binary
    extra_args: "--upgrade --break-system-packages"
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'

- name: Install python requirements on Ubuntu < 24.04
  ansible.builtin.pip:
    name:
      - psycopg2-binary
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'
