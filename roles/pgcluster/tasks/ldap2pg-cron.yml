---
- name: Added crontab jobs for sync tasks
  ansible.builtin.cron:
    name: "{{ item.name }}"
    state: present
    disabled: false
    cron_file: "{{ item.cron_file }}"
    backup: true
    minute: "{{ item.minute }}"
    hour: "{{ item.hour }}"
    day: "{{ item.day }}"
    month: "{{ item.month }}"
    weekday: "{{ item.weekday }}"
    user: "{{ item.user }}"
    job: "{{ item.job }}"
  loop: "{{ pgcluster_ldap2pg_cron }}"
  become: true

- name: Added crontab vars
  ansible.builtin.cron:
    name: "{{ item.key }}"
    cron_file: "{{ pgcluster_ldap2pg_cron_file }}"
    backup: true
    job: "{{ item.value }}"
    env: true
    user: "{{ pgcluster_ldap2pg_cron_user }}"
  loop: "{{ lookup('ansible.builtin.dict', pgcluster_ldap2pg_cron_env) }}"
  become: true
