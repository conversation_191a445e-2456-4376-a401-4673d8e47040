---
- name: Ansible block patroni
  tags: patroni
  block:
   - name: Add env variable for Patroni
     ansible.builtin.lineinfile:
      dest: /etc/environment
      state: present
      regexp: "^PATRONICTL_CONFIG_FILE="
      line: "PATRONICTL_CONFIG_FILE={{ pgcluster_patroni_config_file }}"

   - name: Change file ownership and group
     ansible.builtin.file:
      path: "{{ pgcluster_mount_path }}"
      owner: "{{ pgcluster_user }}"
      group: "{{ pgcluster_user }}"

   - name: Install Patroni with deps consul on Ubuntu 24.04
     ansible.builtin.pip:
      name: "{{ item }}"
      extra_args: "--upgrade --break-system-packages"
     loop:
      - psycopg2
      - patroni[consul]=={{ pgcluster_patroni_version }}
      - cdiff
     when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'
     tags: patroni-install

   - name: Install Patroni with deps consul on Ubuntu < 24.04
     ansible.builtin.pip:
      name: "{{ item }}"
     loop:
      - psycopg2
      - patroni[consul]=={{ pgcluster_patroni_version }}
      - cdiff
     when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'
     tags: patroni-install

   - name: Install patroni packages on Ubuntu 24.04
     ansible.builtin.pip:
      name: "{{ item }}"
      extra_args: "--upgrade --break-system-packages"
     loop: "{{ pgcluster_pip_packages }}"
     when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'

   - name: Install patroni packages on Ubuntu < 24.04
     ansible.builtin.pip:
      name: "{{ item }}"
     loop: "{{ pgcluster_pip_packages }}"
     when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'

   - name: Create directory for DATA postgresql
     ansible.builtin.file:
      path: "{{ pgcluster_path_data_psql }}"
      state: directory
      owner: "{{ pgcluster_user }}"
      group: "{{ pgcluster_user }}"
      mode: '700'

   - name: Create directory for LOGGING postgresql
     ansible.builtin.file:
      path: "{{ pgcluster_path_log_dir }}"
      state: directory
      owner: "{{ pgcluster_user }}"
      group: "{{ pgcluster_user }}"
      mode: '700'

   - name: Touch a file for LOGGING
     ansible.builtin.file:
      path: "{{ pgcluster_path_log_dir }}/{{ pgcluster_name_log_file }}"
      state: touch
      owner: "{{ pgcluster_user }}"
      group: "{{ pgcluster_user }}"
      mode: '600'

   - name: Create dir for patroni YAML file
     ansible.builtin.file:
      path: "{{ pgcluster_patroni_yml_path }}"
      state: directory
      owner: "{{ pgcluster_user }}"
      group: "{{ pgcluster_user }}"
      mode: '755'

   - name: Create template YAML file for create name_cluster
     ansible.builtin.template:
      src: postgres.yml.j2
      dest: "{{ pgcluster_patroni_yml_path }}/postgres.yml"
      mode: '644'
      backup: true
     tags: patroni-config

   - name: Copy service patroni
     ansible.builtin.template:
      src: patroni.service.j2
      dest: "/etc/systemd/system/patroni.service"
      mode: '644'

   - name: Restarting service patroni with create HA cluster PostgreSQL"
     become_method: ansible.builtin.sudo
     ansible.builtin.systemd_service:
      name: patroni
      state: restarted
      enabled: true
      daemon_reload: true
     tags: patroni-restart

   - name: Sleep for 20 seconds
     ansible.builtin.pause:
      seconds: 20

   - name: Call API to apply some parameters
     ansible.builtin.uri:
      url: "http://{{ ansible_ssh_host }}:8008/config"
      method: PATCH
      body:
       postgresql:
        parameters:
         max_connections: "{{ pgcluster_postgresql_max_connections }}"
       failsafe_mode: "{{ pgcluster_patroni_failsafe_mode }}"
       ttl: "{{ pgcluster_patroni_ttl }}"
       loop_wait: "{{ pgcluster_patroni_loop_wait }}"
       retry_timeout: "{{ pgcluster_patroni_retry_timeout }}"
      body_format: json
     run_once: true

   - name: Call API to restart nodes if need
     ansible.builtin.uri:
      url: "http://{{ ansible_ssh_host }}:8008/restart"
      method: POST
      body_format: json
      timeout: 1200
      body:
       restart_pending: true
      status_code: 503
