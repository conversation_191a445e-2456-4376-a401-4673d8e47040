---
- name: Create ldap_sync user
  community.postgresql.postgresql_user:
    db: "{{ pgcluster_postgres_sync_ldap_db }}"
    login_host: "{{ vip_address }}"
    login_password: "{{ pass_pg_postgres }}"
    login_port: "{{ pgcluster_postgres_sync_ldap_port_vip }}"
    name: "{{ postgres_sync_ldap_user }}"
    password: "{{ postgres_sync_ldap_pass }}"
    role_attr_flags: SUPERUSER
  run_once: true

- name: Create work dir
  ansible.builtin.file:
    path: "{{ pgcluster_ldap2pg_work_dir }}"
    state: directory
    mode: '755'
    recurse: false

- name: Temlate config file ldap2pg
  ansible.builtin.template:
    force: true
    backup: true
    dest: "{{ pgcluster_ldap2pg_conf_file }}"
    src: ldap2pg.yml.j2
    mode: '600'
