---
- name: Install software-properties-common
  ansible.builtin.apt:
    name: software-properties-common
    state: latest
    install_recommends: false
    cache_valid_time: 86400

- name: Add ppa haproxy repository
  ansible.builtin.apt_repository:
    repo: ppa:vbernat/haproxy-2.8
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version == '20.04'

- name: Install HAProxy package (on Debian)
  ansible.builtin.apt:
    name: haproxy
    cache_valid_time: 86400
    state: latest

- name: Enable net.ipv4.ip_nonlocal_bind
  ansible.posix.sysctl:
    name: net.ipv4.ip_nonlocal_bind
    value: '1'
    sysctl_set: true
    state: present
    reload: true

- name: Restart haproxy service
  ansible.builtin.systemd:
    name: haproxy
    state: restarted
    enabled: true
