---
- name: Add repository apt-key
  ansible.builtin.apt_key:
    url: "{{ item.key }}"
    state: present
  loop: "{{ pgcluster_apt_repository_keys }}"
  when: pgcluster_apt_repository_keys | length > 0

- name: Add repository
  ansible.builtin.apt_repository:
    repo: "{{ item.repo }}"
    state: present
    update_cache: true
  loop: "{{ pgcluster_apt_repository }}"
  when: pgcluster_apt_repository | length > 0

- name: Ensure postgresql database-cluster manager package
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: true
    allow_downgrade: true
  loop:
    - postgresql-client-common
    - postgresql-common

- name: Disable initializing of a default postgresql cluster
  ansible.builtin.replace:
    path: /etc/postgresql-common/createcluster.conf
    replace: create_main_cluster = false
    regexp: ^#?create_main_cluster.*$

- name: Disable log rotation with logrotate for postgresql
  ansible.builtin.file:
    dest: /etc/logrotate.d/postgresql-common
    state: absent

- name: Install PostgreSQL packages
  ansible.builtin.apt:
    name: "{{ item }}"
    state: latest
    allow_downgrade: true
  loop: "{{ pgcluster_postgresql_packages }}"

- name: Add an Apt timescaledb signing key
  ansible.builtin.apt_key:
    url: https://packagecloud.io/timescale/timescaledb/gpgkey
    state: present
  when: pgcluster_timescaledb
  tags: never

- name: Add timescaledb repository
  ansible.builtin.apt_repository:
    repo: "{{ pgcluster_postgresql_timescaledb_repo }}"
    state: present
    filename: timescaledb
  when: pgcluster_timescaledb

- name: Install timescaledb packages
  ansible.builtin.apt:
    name: "{{ item }}"
    state: latest
  loop: "{{ pgcluster_postgresql_timescaledb_packages }}"
  when: pgcluster_timescaledb
