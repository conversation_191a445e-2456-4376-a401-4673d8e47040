---
- name: Check if /dev/vdb exists
  ansible.builtin.stat:
    path: /dev/vdb
  register: vdb_stat

- name: Create a ext4 filesystems on devices
  community.general.filesystem:
    fstype: ext4
    dev: "{{ item }}"
  loop:
    - /dev/vdb
  when: vdb_stat.stat.exists

- name: Create fstab entries and mount filesystems
  ansible.posix.mount:
    backup: true
    boot: true
    fstype: ext4
    opts: noatime
    path: "{{ item.path }}"
    src: "{{ item.dev }}"
    state: "{{ item.state }}"
  loop:
    - {path: "{{ pgcluster_mount_path }}", dev: /dev/vdb, state: mounted}
  when: vdb_stat.stat.exists

- name: Update apt cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600

- name: Install system packages (on Debian)
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
  loop: "{{ pgcluster_system_packages }}"

- name: Check if ufw package is installed.
  ansible.builtin.command: systemctl status ufw
  register: ufw_installed
  failed_when: false
  changed_when: false
  check_mode: false

- name: Disable the ufw firewall if configured
  ansible.builtin.systemd:
    name: ufw
    state: stopped
    enabled: false
  when:
    - ufw_installed.rc == 0

- name: Cluster nodes in /etc/hosts
  ansible.builtin.template:
    src: hosts.j2
    dest: /etc/hosts
    mode: '655'

- name: Disable swap
  ansible.builtin.command: swapoff -a
  changed_when: true

- name: Disable swap in fstab
  ansible.posix.mount:
    state: absent_from_fstab
    path: none
    src: /swap.img
    backup: true
