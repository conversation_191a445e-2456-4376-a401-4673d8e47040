---
- name: Create consul-template directory structure
  ansible.builtin.file:
    state: directory
    path: "{{ item }}"
    owner: "consul"
    group: "consul"
    mode: '755'
  with_items:
    - "{{ pgcluster_consul_tpl_dirs }}"

- name: Check archive stat
  ansible.builtin.stat:
    path: "{{ pgcluster_consul_tpl_area }}/{{ pgcluster_consul_tpl_archive_file }}"
  register: consul_tpl_archive_stat

- name: Download consul-template binary
  ansible.builtin.get_url:
    url: "{{ pgcluster_consul_tpl_download_url }}"
    dest: "{{ pgcluster_consul_tpl_area }}"
    mode: '644'
  when: not consul_tpl_archive_stat.stat.exists
  check_mode: false

- name: Unzip the downloaded package
  ansible.builtin.unarchive:
    src: "{{ pgcluster_consul_tpl_area }}/{{ pgcluster_consul_tpl_archive_file }}"
    dest: "/usr/local/bin/"
    owner: "root"
    group: "root"
    copy: false

- name: Copy consul-template systemd service configuration
  ansible.builtin.template:
    backup: true
    src: "{{ pgcluster_consul_tpl_name_service }}.service.j2"
    dest: "/etc/systemd/system/{{ pgcluster_consul_tpl_name_service }}.service"
    mode: '755'

- name: Consul-template config file
  ansible.builtin.template:
    src: "{{ pgcluster_consul_tpl_name_service }}.cfg.j2"
    dest: "{{ pgcluster_consul_tpl_home }}/config/{{ pgcluster_consul_tpl_name_service }}.cfg"
    mode: '755'

- name: Consul-template config file
  ansible.builtin.template:
    src: haproxy.ctmpl.j2
    dest: "{{ pgcluster_consul_tpl_home }}/templates/haproxy.ctmpl"
    mode: '644'

- name: Restart consul-template sevice
  ansible.builtin.systemd_service:
    name: "{{ pgcluster_consul_tpl_name_service }}"
    state: restarted
    enabled: true
    daemon_reload: true
