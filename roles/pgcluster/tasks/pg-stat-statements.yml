---
- name: Create extention pg_stat_statements
  community.postgresql.postgresql_query:
    db: "postgres"
    login_host: "{{ vip_address }}"
    login_port: "5432"
    login_password: "{{ pass_pg_postgres }}"
    query: CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
  become: true
  become_user: postgres
  run_once: true
  tags:
    - extension
    - extension_pg-stat-statements

- name: Create extention pg_stat_monitor
  community.postgresql.postgresql_query:
    db: "postgres"
    login_host: "{{ vip_address }}"
    login_port: "5432"
    login_password: "{{ pass_pg_postgres }}"
    query: CREATE EXTENSION IF NOT EXISTS pg_stat_monitor;
  become: true
  become_user: postgres
  run_once: true
  tags:
    - extension
    - extension_pg-stat-monitor
  when: pgcluster_pg_stat_monitor
