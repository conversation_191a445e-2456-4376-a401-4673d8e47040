---
- name: Install keepalived package (on Debian)
  ansible.builtin.apt:
    name: keepalived

- name: Replace keepalived.conf as template file
  ansible.builtin.template:
    src: keepalived.conf.j2
    dest: "/etc/keepalived/keepalived.conf"
    mode: '644'
    backup: true

- name: Restarting keepalived service
  ansible.builtin.systemd:
    name: keepalived
    state: restarted
    enabled: true
