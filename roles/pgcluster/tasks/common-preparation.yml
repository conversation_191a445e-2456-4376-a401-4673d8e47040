---
- name: Check all playing hosts have exactly three group included 'pgcluster' in name
  ansible.builtin.assert:
    that: group_names | select('search', 'pgcluster') | list | length == 3
    fail_msg: Host must have exactly one group with 'pgcluster' in name
    success_msg: "Ok: Host have exactly one group with 'pgcluster' in name"

- name: Set cluster name
  ansible.builtin.set_fact:
    cluster_name: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgcluster"

- name: Generate passwords
  ansible.builtin.set_fact:
    "generated_pass_{{item}}": "{{ lookup('password', '/dev/null length=15 chars=ascii_letters,digits') }}"
  loop: "{{ pgcluster_pass_array }}"
  delegate_facts: true
  delegate_to: 'localhost'
  run_once: true
  tags:
    - skip_ansible_lint

- name: Ansible check file exists.
  ansible.builtin.stat:
    path: /etc/patroni/postgres.yml
  register: patroni_config

- name: Get content of patroni config
  ansible.builtin.command: cat /etc/patroni/postgres.yml
  register: postgres_yaml
  when: patroni_config.stat.exists
  changed_when: false
  check_mode: false

- name: Import passwords from existing config
  ansible.builtin.set_fact:
    pass_pg_replicator: "{{ pass_pg_replicator | \
                        default((postgres_yaml.stdout | \
                        from_yaml)['postgresql']['authentication']['replication']['password']) }}"
    pass_pg_admin: "{{ pass_pg_admin | default((postgres_yaml.stdout | from_yaml)['bootstrap']['users']['admin']['password']) }}"
    pass_pg_postgres: "{{ pass_pg_postgres | default((postgres_yaml.stdout | from_yaml)['postgresql']['authentication']['superuser']['password']) }}"
  when: patroni_config.stat.exists

- name: Define pass_* if undefined
  ansible.builtin.set_fact:
    "{{item}}": "{{ hostvars['localhost']['generated_pass_' + item] }}"
  loop: "{{ pgcluster_pass_array }}"
  when: vars[item] is not defined
  tags:
    - skip_ansible_lint

- name: Set goal passwords
  ansible.builtin.set_fact:
    "goal_pass_{{item}}": "{{ vars[item] }}"
  loop: "{{ pgcluster_pass_array }}"
  delegate_facts: true
  delegate_to: 'localhost'
  run_once: true
  tags:
    - skip_ansible_lint

- name: Check passwords for hosts is the same
  ansible.builtin.assert:
    that: vars[item] is match ( hostvars['localhost']['goal_pass_' + item] )
    fail_msg: Password is different. Check variables in inventory.
    success_msg: "Ok: Password is match with goal password"
  loop: "{{ pgcluster_pass_array }}"

- name: Show cluster's name
  ansible.builtin.debug:
    msg:
      - "Cluster name: {{ cluster_name }}"

- name: Show passwords
  ansible.builtin.debug:
    msg:
      - "Host: {{ item }}: {{ vars[item] }}"
      - "LHst: {{ item }}: {{ hostvars['localhost']['goal_pass_' + item] }}"
  loop: "{{ pgcluster_pass_array }}"

- name: Decision about installation secondary pgbouncer
  ansible.builtin.set_fact:
    pgcluster_second_pgbouncer_install: "{{
      true if
          pgcluster_pgbouncer_app_user_ro is defined
        and
          vip_address_ro is defined
        and
          vrouter_id_ro is defined
      else false }}"

- name: Show information about install second pgbouncer
  ansible.builtin.debug:
    msg: "Install second pgbouncer: {{ pgcluster_second_pgbouncer_install }}"
