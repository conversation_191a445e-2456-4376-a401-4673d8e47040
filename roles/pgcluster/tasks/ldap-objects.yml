---
- name: Add app postgres user to ldap and reset password
  freeipa.ansible_freeipa.ipauser:
    ipaadmin_password: "{{ admin_password }}"
    name: "{{ item }}"
    givenname: "{{ pgcluster_postgres_app_user }}"
    sn: "{{ pgcluster_postgres_app_user }}"
    email: "{{ global_devops_email }}"
    passwordexpiration: "{{ pgcluster_ldap_password_expiration }}"
    password: "{{ postgres_app_password }}"
    update_password: always
    ipaapi_context: client
  delegate_facts: true
  run_once: true
  when: postgres_app_password is defined
  loop:
    - "{{ pgcluster_postgres_app_user }}"
    - "{{ pgcluster_postgres_app_user_ro }}"

- name: Add app postgres user to ldap and reset password
  freeipa.ansible_freeipa.ipauser:
    ipaadmin_password: "{{ admin_password }}"
    name: "{{ item }}"
    givenname: "{{ pgcluster_postgres_app_user }}"
    sn: "{{ pgcluster_postgres_app_user }}"
    email: "{{ global_devops_email }}"
    passwordexpiration: "{{ pgcluster_ldap_password_expiration }}"
    password: "{{ postgres_app_password }}"
    update_password: on_create
    ipaapi_context: client
  delegate_facts: true
  run_once: true
  when: postgres_app_password is defined
  loop:
    - "{{ pgcluster_postgres_app_user }}"
    - "{{ pgcluster_postgres_app_user_ro }}"

- name: Add groups to FreeIPA
  community.general.ipa_group:
    description: "{{ item.description | default(omit) }}"
    name: "{{ item.name }}"
    state: present
    append: true
    ipa_host: "{{ ipa_server_hostname }}.sl.local"
    ipa_user: "{{ ipa_admin_user }}"
    ipa_pass: "{{ admin_password }}"
    user: "{{ item.user | default([]) }}"
  loop:
    - name: postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_owner
      description: Access to {{ postgres_db_env }}_{{ postgres_cluster_name }} postgre as owner
      user:
        - "{{ pgcluster_postgres_app_user }}"
        - "{{ pgcluster_postgres_app_user_ro }}"
    - name: postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_read
      description: Access to {{ postgres_db_env }}_{{ postgres_cluster_name }} postgre as reader
      user:
        - "{{ pmm_client_postgres_user }}"
        - "{{ pguser_zbx_monitor }}"
    - name: postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_write
      description: Access to {{ postgres_db_env }}_{{ postgres_cluster_name }} postgre as writer
  run_once: true

- name: Add member to group
  freeipa.ansible_freeipa.ipagroup:
    ipaadmin_password: "{{ admin_password }}"
    name: "{{ ldap_technical_users }}"
    user:
      - "{{ pgcluster_postgres_app_user }}"
      - "{{ pgcluster_postgres_app_user_ro }}"
    action: member
  delegate_facts: true
  run_once: true
  when: postgres_app_password is defined
