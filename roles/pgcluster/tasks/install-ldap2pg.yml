---
- name: Download pg2ldap binary
  ansible.builtin.get_url:
    url: "{{ pgcluster_ldap2pg_download_url }}"
    dest: "{{ pgcluster_tmp_directory }}"
    mode: '644'

- name: Unarchive pg2ldap binary
  ansible.builtin.unarchive:
    src: "{{ pgcluster_tmp_directory }}/{{ pgcluster_ldap2pg_archive_file }}"
    dest: "/usr/local/bin/"
    owner: "root"
    group: "root"
    include:
      - ldap2pg
    remote_src: true
    mode: "755"
