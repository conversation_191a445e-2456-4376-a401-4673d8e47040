---
- name: Add pmm user
  community.postgresql.postgresql_user:
    name: "{{ pmm_client_postgres_user }}"
    login_port: "5432"
    password: "{{ pmm_client_postgres_user_password }}"
    login_host: "{{ vip_address }}"
    login_password: "{{ pass_pg_postgres }}"
  become: true
  become_user: "{{ pgcluster_user }}"
  run_once: true

- name: GRANT pg_monitor
  community.postgresql.postgresql_query:
    db: "postgres"
    login_host: "{{ vip_address }}"
    login_port: "5432"
    login_password: "{{ pass_pg_postgres }}"
    query: GRANT pg_monitor to {{ pmm_client_postgres_user }};
  become: true
  become_user: "{{ pgcluster_user }}"
  run_once: true

- name: Set limit connections to pmm user
  community.postgresql.postgresql_query:
    db: "postgres"
    login_host: "{{ vip_address }}"
    login_port: "5432"
    login_password: "{{ pass_pg_postgres }}"
    query: ALTER USER {{ pmm_client_postgres_user }} CONNECTION LIMIT {{ pmm_client_postgres_con_limit }};
  become: true
  become_user: postgres
  run_once: true

- name: Create a data container
  community.docker.docker_container:
    name: pmm-client-data
    pull: true
    image: "{{ pmm_agent_image }}"
    volumes:
      - /srv
    command:
      - /bin/true

- name: Run runtime container
  community.docker.docker_container:
    image: "{{ pmm_agent_image }}"
    name: pmm-client
    hostname: "{{ inventory_hostname }}"
    state: started
    pull: true
    restart: true
    restart_policy: always
    tty: true
    interactive: true
    volumes_from:
      - pmm-client-data
    cpu_period: "{{ pmm_cpu_period }}"
    cpu_quota: "{{ pmm_cpu_quota }}"
    memory: "{{ pmm_memory }}"
    env:
      PMM_AGENT_SERVER_ADDRESS: "{{ pmm_server_ip }}:{{ pmm_server_port }}"
      PMM_AGENT_SERVER_USERNAME: "{{ pmm_server_user }}"
      PMM_AGENT_SERVER_PASSWORD: "{{ pmm_server_user_password }}"
      PMM_AGENT_SERVER_INSECURE_TLS: "1"
      PMM_AGENT_CONFIG_FILE: "config/pmm-agent.yaml"
    comparisons:
      '*': strict

- name: Pause for 20 seconds to start container
  ansible.builtin.pause:
    seconds: 20

- name: Register client node with PMM Server
  community.docker.docker_container_exec:
    container: pmm-client
    command: >
            pmm-admin config
            --server-insecure-tls
            --server-url=https://{{ pmm_server_user }}:{{ pmm_server_user_password }}@{{ pmm_server_ip }}:{{ pmm_server_port }}
            --force
  register: command_result

- name: Pause for 20 seconds to start container
  ansible.builtin.pause:
    seconds: 20


- name: Add PostgreSQL service
  community.docker.docker_container_exec:
    container: pmm-client
    command: >
            pmm-admin add postgresql
            --username={{ pmm_client_postgres_user }}
            --host={{ ansible_ssh_host }}
            --password={{ pmm_client_postgres_user_password }} --query-source={{ 'pgstatmonitor' if pgcluster_pg_stat_monitor else 'pgstatements' }}
            --port 6543 {{ inventory_hostname }}
