---
- name: Install PGBackRest
  ansible.builtin.apt:
    name: pgbackrest
    state: latest
    update_cache: true
    allow_change_held_packages: true
    cache_valid_time: 86400
  become: true

- name: Create directory for pgbackrest spool
  ansible.builtin.file:
    path: "{{ pgcluster_pgbackrest_spool_path }}"
    state: directory
    owner: "{{ pgcluster_user }}"
    group: "{{ pgcluster_user }}"
    mode: '700'

- name: Create config from template and copy it destination node
  ansible.builtin.template:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    backup: true
    mode: '640'
    owner: "{{ pgcluster_user }}"
    group: "{{ pgcluster_user }}"
  loop:
    - {src: pgbackrest.j2, dest: /etc/pgbackrest.conf}
    - {src: pgbackrest-old.j2, dest: /etc/pgbackrest-old.conf}
