---
- name: Consul | Ensure Consul group
  ansible.builtin.group:
    name: "consul"

- name: Consul | Ensure Consul user
  ansible.builtin.user:
    name: "consul"
    group: "consul"
    createhome: false

- name: Output versions
  ansible.builtin.debug:
    var: "{{ item }}"
  loop:
    - pgcluster_postgres_consul_version
    - pgcluster_consul_release
    - pgcluster_consul_archive_file
    - pgcluster_consul_area
    - pgcluster_consul_download_url

- name: Check archive stat
  ansible.builtin.stat:
    path: "{{ pgcluster_consul_area }}/{{ pgcluster_consul_archive_file }}"
  register: consul_archive_stat

- name: Download consul binary
  ansible.builtin.get_url:
    url: "{{ pgcluster_consul_download_url }}"
    dest: "{{ pgcluster_consul_area }}"
    mode: '644'
  when: not consul_archive_stat.stat.exists

- name: Unzip the downloaded package
  ansible.builtin.unarchive:
    src: "{{ pgcluster_consul_area }}/{{ pgcluster_consul_archive_file }}"
    dest: "/usr/local/bin/"
    owner: "root"
    group: "root"
    copy: false

- name: Create a directory if it does not exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '755'
  with_items:
    - "{{ pgcluster_consul_require_dirs }}"

- name: Update consul permissions
  ansible.builtin.file:
    path: "{{ pgcluster_consul_data_dir }}"
    state: directory
    owner: consul
    group: consul
    mode: '755'

- name: "Generate uniq key for consul config"
  ansible.builtin.command: /usr/local/bin/consul keygen
  register: consul_uniq_key
  run_once: true
  changed_when: true
  when: pgcluster_consul_uniq_key is undefined

- name: "Add uniq key for consul to dummy host"
  ansible.builtin.add_host:
    name: "CONSUL_DUMMY_HOLDER"
    consul_uniq_key: "{{ pgcluster_consul_uniq_key | default(consul_uniq_key['stdout']) }}"
  run_once: true

- name: Copy config for consul
  ansible.builtin.template:
    src: config.json.j2
    dest: "{{ pgcluster_consul_common_dir }}/config.json"
    mode: '755'

- name: Create service for consul
  ansible.builtin.template:
    src: consul.service.j2
    dest: "/etc/systemd/system/consul.service"
    mode: '644'

- name: Enable and restart consul
  ansible.builtin.systemd:
    name: consul
    state: restarted
    enabled: true
