---
# Repository
pgcluster_apt_repository_keys:
  - key: "https://www.postgresql.org/media/keys/ACCC4CF8.asc"  # postgresql repository apt key
pgcluster_apt_repository:
  - repo: "deb http://apt.postgresql.org/pub/repos/apt/ {{ ansible_distribution_release }}-pgdg main"

# Packages (for apt repo)
pgcluster_system_packages:
  - python3
  - python3-dev
  - python3-psycopg2
  - python3-setuptools
  - python3-pip
  - gcc
  - unzip

pgcluster_postgresql_packages:
  - postgresql-{{ pgcluster_postgresql_version }}
  - postgresql-server-dev-{{ pgcluster_postgresql_version }}
  - postgresql-client-{{ pgcluster_postgresql_version }}

pgcluster_postgresql_timescaledb_packages:
  - timescaledb-2-loader-postgresql-{{ pgcluster_postgresql_version }}
  - timescaledb-toolkit-postgresql-{{ pgcluster_postgresql_version }}
  - timescaledb-2-postgresql-{{ pgcluster_postgresql_version }}

pgcluster_postgresql_bin_dir: "/usr/lib/postgresql/{{ pgcluster_postgresql_version }}/bin"

pgcluster_pass_array:
  - pass_pg_replicator
  - pass_pg_admin
  - pass_pg_postgres
  - pass_keepalived

pgcluster_port: 6543
pgcluster_vip_port: 5432
pgcluster_prometheus_exporter_host: localhost
pgcluster_user: postgres

pgcluster_zbx_monitor_con_limit: 200
