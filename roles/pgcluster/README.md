pgcluster-role
=========

Create postgre cluster based on consul, patroni, haproxy, keepalived

Requirements
------------
* Group of ubuntu servers with name includes 'pgcluster' with odd numbers of servers.
* Servers must be membershiped strongly one group with 'pgcluster' in name
* Collection freeipa.ansible_freeipa (tested version: 1.11.1)
  #:   ansible-galaxy collection install freeipa.ansible_freeipa
* Some quantity of brain

Role Variables
--------------

Example of incoming variables:

  # Global settings for HA
  vip_address: 192.168.x.xxx
  vrouter_id: 48

  # PostgreSQL variables
  postgresql_version: "14"


Per host variables:

hz-xxx-yyy-01  ansible_host=192.168.xx.xx ansible_port=2202 priority_num=255 priority_num_ro=200
hz-xxx-yyy-02  ansible_host=192.168.xx.xx ansible_port=2202 priority_num=200 priority_num_ro=255
hz-xxx-yyy-03  ansible_host=192.168.xx.xx ansible_port=2202 priority_num=100 priority_num_ro=100

LDAP
=========

In group_vars set postgres_db_env, postgres_cluster_name

Example:
  postgres_db_env: prod
  postgres_cluster_name: bas

In LDAP automatically creates groups for db:
  postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_owner
  postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_write
  postgresql_{{ postgres_db_env }}_{{ postgres_cluster_name }}_read

TO DO:
=========
* Separate tasks from prepare_pgbackrest.yml to different file and choose correct places to import - correct archieving need to correctly up cluster, but for create stanza it's need cluster already be correctly running