---
# Prometheus exporter vars
pgcluster_ignore_err: false
pgcluster_postgres_prom_exporter_version: v0.11.1
pgcluster_postgres_prom_exporter_cpu_period: '100000'
pgcluster_postgres_prom_exporter_cpu_quota: '50000'
pgcluster_postgres_prom_exporter_memory: '512m'

# Global vars
pgcluster_postgresql_version: "14"

pgcluster_postgresql_timescaledb_package_version: "2.11.2~ubuntu20.04"
pgcluster_postgresql_timescaledb_toolkit_package_version: "1:1.17.0~ubuntu20.04"
pgcluster_postgresql_timescaledb_repo: "deb https://packagecloud.io/timescale/timescaledb/ubuntu/ focal main"
pgcluster_postgresql_max_connections: 5000
pgcluster_patroni_path_bin: "/usr/local/bin"
pgcluster_patroni_yml_path: "/etc/patroni"
pgcluster_path_data_psql: "/data/patroni"
pgcluster_path_log_dir: "/var/log/postgresql"
pgcluster_name_log_file: "postgresql.log"

# Setting yaml file
# General recommendation to set the shared_buffers is as follows.
#
#    Below 2GB memory, set the value of shared_buffers to 20% of total system memory.
#
#    Below 32GB memory, set the value of shared_buffers to 25% of total system memory.
#
#    Above 32GB memory, set the value of shared_buffers to 8GB

pgcluster_patroni_version: 4.0.3
pgcluster_patroni_sb: 750MB
pgcluster_patroni_no_failover: []
pgcluster_patroni_ttl: 60
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 20
pgcluster_patroni_failsafe_mode: true
pgcluster_mount_path: "/data"
pgcluster_patroni_config_file: "/etc/patroni/postgres.yml"

pgcluster_pip_packages:
   - patroni=={{ pgcluster_patroni_version }}
   - psycopg2
   - kazoo
   - requests

pgcluster_python_ver: 38
pgcluster_required_pack:
   - "python{{ pgcluster_python_ver }}"
   - "python{{ pgcluster_python_ver }}-devel"
   - gcc
   - unzip

pgcluster_tmp_directory: "/tmp"
pgcluster_consul_name_service: consul
pgcluster_postgres_consul_version: "1.19.2"
pgcluster_consul_release: "consul_{{ pgcluster_postgres_consul_version }}_linux_amd64"
pgcluster_consul_archive_file: "{{ pgcluster_consul_release }}.zip"
pgcluster_consul_area: "/tmp"
pgcluster_consul_download_url: "https://releases.hashicorp.com/\
   {{ pgcluster_consul_name_service }}/\
   {{ pgcluster_postgres_consul_version }}/\
   {{ pgcluster_consul_archive_file }}"

pgcluster_consul_data_dir: "/var/consul"
pgcluster_consul_common_dir: "/etc/consul.d"
pgcluster_consul_require_dirs:
   - "{{ pgcluster_consul_common_dir }}/scripts"
   - "{{ pgcluster_consul_data_dir }}"
pgcluster_consul_tpl_name_service: consul-template
pgcluster_consul_tpl_version: "0.39.1"
pgcluster_consul_tpl_release: "consul-template_{{ pgcluster_consul_tpl_version }}_linux_amd64"
pgcluster_consul_tpl_archive_file: "{{ pgcluster_consul_tpl_release }}.zip"
pgcluster_consul_tpl_area: "/tmp"
pgcluster_consul_tpl_download_url: "https://releases.hashicorp.com/{{ pgcluster_consul_tpl_name_service }}/\
   {{ pgcluster_consul_tpl_version }}/{{ pgcluster_consul_tpl_archive_file }}"

pgcluster_consul_tpl_home: "/opt/{{ pgcluster_consul_tpl_name_service }}"
pgcluster_consul_log_file: "/var/log/{{ pgcluster_consul_tpl_name_service }}"
pgcluster_consul_tpl_dirs:
   - "{{ pgcluster_consul_tpl_home }}/templates"
   - "{{ pgcluster_consul_tpl_home }}/config"
   - "{{ pgcluster_consul_log_file }}"
pgcluster_postgres_consul_log_level: "info"

pgcluster_postgres_pack:
   - "postgresql{{ pgcluster_postgresql_version }}-server"
   - "postgresql{{ pgcluster_postgresql_version }}-contrib"
   - "postgresql{{ pgcluster_postgresql_version }}-devel"

# LDAP
pgcluster_postgres_sync_ldap_host: "{{ vip_address }}"
pgcluster_postgres_sync_ldap_port: "6543"
pgcluster_postgres_sync_ldap_port_vip: "5432"
pgcluster_postgres_sync_ldap_db: postgres
pgcluster_postgres_sync_ldap_soket: /tmp

pgcluster_ldap2pg_ver: 6.2-beta1
pgcluster_ldap2pg_work_dir: "/opt/ldap2pg/"
pgcluster_ldap2pg_conf_file: "{{ pgcluster_ldap2pg_work_dir }}ldap2pg.yml"
pgcluster_ldap2pg_archive_file: ldap2pg_{{ pgcluster_ldap2pg_ver }}_linux_amd64.tar.gz
pgcluster_ldap2pg_download_url: "https://github.com/dalibo/ldap2pg/releases/download/\
 v{{ pgcluster_ldap2pg_ver }}/\
 ldap2pg_{{ pgcluster_ldap2pg_ver }}_linux_amd64.tar.gz"
pgcluster_ldap2pg_cron_file: pg-ldap-sync
pgcluster_ldap2pg_cron_user: root
pgcluster_ldap2pg_cron:
   - name: pg-ldap-sync
     cron_file: "{{ pgcluster_ldap2pg_cron_file }}"
     minute: "*/5"
     hour: "*"
     day: "*"
     month: "*"
     weekday: "*"
     user: "{{ pgcluster_ldap2pg_cron_user }}"
     job: "echo '{{ ldap_bind_password }}' | kinit {{ ldap_bind_user }} && /usr/local/bin/ldap2pg -R -c {{ pgcluster_ldap2pg_conf_file }}"

pgcluster_ldap2pg_cron_env:
   LDAPBINDDN: "{{ ldap_bind_dn }}"
   LDAPPASSWORD: "{{ ldap_bind_password }}"
   PGHOST: "127.0.0.1"
   PGPORT: "{{ pgcluster_postgres_sync_ldap_port }}"
   PGUSER: "{{ postgres_sync_ldap_user }}"
   PGPASSWORD: "{{ postgres_sync_ldap_pass }}"
   PGDATABASE: postgres

# app users
pgcluster_postgres_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_postgres_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"

# install timescaledb
pgcluster_timescaledb: false

# pgbouncer
pgcluster_pgbouncer_admin_user: badmin
pgcluster_pgbouncer_admin_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37343162616132373766333166353663623935646261386338303565613535316163336633323462
          6134326637633966326431663134383738346233356364640a643234623838633366326237316163
          34373564383963363434663537656137636261313530373932613665613566663136623662633934
          3865626235316463620a326566643631313463323661383461643435623635616235623034373339
          3531
pgcluster_pgbouncer_listen_port: 6432
pgcluster_pgbouncer_ro_listen_port: 6440
pgcluster_pgbouncer_max_client_conn: 2000
pgcluster_pgbouncer_default_pool_size: 2000
pgcluster_pgbouncer_ro_max_client_conn: 2000
pgcluster_pgbouncer_ro_default_pool_size: 2000
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_pool_mode: transaction

pgcluster_pgbackrest_spool_path: "/data/pgbackrest-spool"
pgcluster_pgbackrest_version: "2.53.1-1.pgdg20.04+1"
pgcluster_min_wal_size: 5GB
pgcluster_max_wal_size: 20GB

pgcluster_md5: false
pgcluster_ldap_password_expiration: "20550505050505"

pgcluster_pg_stat_monitor: false
pgcluster_percona_release_url: https://repo.percona.com/apt/percona-release_latest.generic_all.deb
pgcluster_pg_stat_monitor_package_version: latest

pgcluster_pgbouncer_exporter_version: v0.8.0
pgcluster_pgbouncer_exporter_con_str:
   "--pgBouncer.connectionString=\"postgres://{{ pgcluster_pgbouncer_admin_user }}:\
   {{ pgcluster_pgbouncer_admin_pass }}@{{ vip_address }}:6432/pgbouncer?sslmode=disable\""
pgcluster_pgbouncer_exporter_con_str_ro:
   "--pgBouncer.connectionString=\"postgres://{{ pgcluster_pgbouncer_admin_user }}:\
   {{ pgcluster_pgbouncer_admin_pass }}@{{ vip_address_ro }}:{{ pgcluster_pgbouncer_ro_listen_port }}/pgbouncer?sslmode=disable\""

pgcluster_consul_exporter_version: "0.9.0-debian-11-r17"
pgcluster_consul_exporter_image: bitnami/consul-exporter:{{ pgcluster_consul_exporter_version }}

pgcluster_pgbackrest_compress_type: zst
pgcluster_pgbackrest_compress_level: 3
pgcluster_pgbackrest_bundle_size: 1024MiB
pgcluster_pgbackrest_bundle_limit: 128MiB

pgcluster_exporter_download_url: https://gitlab.sl.local/api/v4/projects/177/jobs/artifacts/main/download?job=build
pgcluster_exporter_dir: "/opt/pgcluster-exporter"
pgcluster_exporter_gitlab_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62343434353838663261343431333231383439356162633866636534303039643435326166363165
          3030316562343931353436353936346338353163333030390a343930353532346132363536306337
          35356636653930616132343664636534656134383636633138366137666632333266366138646332
          3333383337323534650a393532663437313237323965383333343861646565393330636132616534
          61393531656461303036346634656132623130623563313766353535303237363630
pgcluster_exporter_archive_file: artifacts.zip
pgcluster_exporter_file: pgcluster-exporter
pgcluster_exporter_name_service: pgcluster-exporter

pgcluster_backup: 'true'
