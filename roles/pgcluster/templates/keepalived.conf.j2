vrrp_script chk_haproxy {
    script "pgrep haproxy"
    interval 2
}

vrrp_instance VI_1 {
    interface {{ postgres_cluster_vip_interface | default(hostvars[inventory_hostname]['ansible_default_ipv4']['interface']) }}
    state {% if priority_num  == 255 %}MASTER{% else %}BACKUP{% endif %}

    priority {{ priority_num }}

    virtual_router_id {{ vrouter_id }}
    unicast_src_ip {{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}
    unicast_peer {
{% for i in groups[cluster_name] %}
{% if hostvars[inventory_hostname]['ansible_default_ipv4']['address'] != hostvars[i]['ansible_host'] %}	    {{ hostvars[i]['ansible_host'] }}
{% endif %}
{% endfor %}    }

    authentication {
        auth_type PASS
        auth_pass {{ pass_keepalived }}
    }

    track_script {
        chk_haproxy
    }

    virtual_ipaddress {
        {{ vip_address }} dev {{ postgres_cluster_vip_interface | default(hostvars[inventory_hostname]['ansible_default_ipv4']['interface']) }}
    }
}

{% if pgcluster_second_pgbouncer_install %}
vrrp_instance VI_2 {
    interface {{ postgres_cluster_vip_interface | default(hostvars[inventory_hostname]['ansible_default_ipv4']['interface']) }}
    
    state {% if priority_num_ro  == 255 %}MASTER{% else %}BACKUP{% endif %}
    
    priority {{ priority_num_ro }}

    virtual_router_id {{ vrouter_id_ro }}
    unicast_src_ip {{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}
    unicast_peer {
{% for i in groups[cluster_name] %}
{% if hostvars[inventory_hostname]['ansible_default_ipv4']['address'] != hostvars[i]['ansible_host'] %}	    {{ hostvars[i]['ansible_host'] }}
{% endif %}
{% endfor %}    }

    authentication {
        auth_type PASS
        auth_pass {{ pass_keepalived }}
    }

    track_script {
        chk_haproxy
    }

    virtual_ipaddress {
        {{ vip_address_ro }} dev {{ postgres_cluster_vip_interface | default(hostvars[inventory_hostname]['ansible_default_ipv4']['interface']) }}
    }
}
{% endif %}
