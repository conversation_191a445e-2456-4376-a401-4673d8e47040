{% set ns = namespace() %}
{% for i in groups[cluster_name] %}
{% if ( i | regex_search('\d$')) == '1' %}
{% set ns.node1_ip = hostvars[i]['ansible_host'] %}
{% elif ( i | regex_search('\d$')) == '2' %}
{% set ns.node2_ip = hostvars[i]['ansible_host'] %}
{% elif ( i | regex_search('\d$')) == '3' %}
{% set ns.node3_ip = hostvars[i]['ansible_host'] %}
{% endif %}
{% endfor %}
[{{ cluster_name }}]
pg1-path=/data/patroni
pg1-socket-path=/tmp
pg1-port={{ pgcluster_port }}
{% if ( inventory_hostname | regex_search('\d$')) == '3' %}
pg2-path=/data/patroni
pg2-port={{ pgcluster_port }}
pg2-host={{ ns.node1_ip }}
pg2-host-port={{ infra_ssh_port }}
pg2-socket-path=/tmp
pg3-path=/data/patroni
pg3-port={{ pgcluster_port }}
pg3-host={{ ns.node2_ip }}
pg3-host-port={{ infra_ssh_port }}
pg3-socket-path=/tmp
{% endif %}

[global]
archive-async=y
archive-get-queue-max=20GiB
archive-push-queue-max=100TiB
spool-path=/data/pgbackrest-spool
log-level-file=detail
archive-timeout=7200
db-timeout=7200
{% if ( inventory_hostname | regex_search('\d$')) in ['1', '2'] %}
repo1-host={{ ns.node3_ip }}
repo1-host-user={{ pgcluster_user }}
repo1-host-port={{ infra_ssh_port }}
{% else %}
backup-standby=y
repo1-path=/backup/pgbackrest
repo1-retention-full=5
repo1-bundle=y
repo1-bundle-size={{ pgcluster_pgbackrest_bundle_size }}
repo1-bundle-limit={{ pgcluster_pgbackrest_bundle_limit }}
{% endif %}
compress-type={{ pgcluster_pgbackrest_compress_type }}
compress-level={{ pgcluster_pgbackrest_compress_level}}
start-fast=y

[global:archive-get]
process-max=2

[global:archive-push]
process-max=2
