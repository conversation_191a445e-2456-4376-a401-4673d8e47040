scope: {{ cluster_name }}
name: {{ inventory_hostname }}

restapi:
  listen: 0.0.0.0:8008
  connect_address: {{ ansible_default_ipv4.address }}:8008

consul:
  host: 127.0.0.1:8500

bootstrap:
  dcs:
    ttl: {{ pgcluster_patroni_ttl }}
    loop_wait: {{ pgcluster_patroni_loop_wait }}
    retry_timeout: {{ pgcluster_patroni_retry_timeout }}
    maximum_lag_on_failover: 1048576
    synchronous_mode: false
    failsafe_mode: {{ pgcluster_patroni_failsafe_mode }}
    postgresql:
      use_pg_rewind: true
      use_slots: true
      parameters:
        max_connections: {{ pgcluster_postgresql_max_connections }}
        archive_timeout: 1800s
        hot_standby: "on"
        wal_log_hints: "on"
        wal_keep_segments: 12
        max_replication_slots: 10
        shared_preload_libraries: {% if pgcluster_timescaledb is defined and pgcluster_timescaledb %} pg_stat_statements, timescaledb{% else %}pg_stat_statements{% endif +%}
{% if pgcluster_max_worker_processes is defined %}        max_worker_processes: {{ pgcluster_max_worker_processes }}{% endif +%}
        config_file: {{ pgcluster_path_data_psql }}/postgresql.conf
      recovery_conf:
{#        restore_command: cp ../wal_archive/%f %p #}
        restore_command: pgbackrest --stanza={{ cluster_name }} --log-level-file=info archive-get %f "%p"
  initdb:
    - encoding: UTF8
    - data-checksums
    - wal-segsize: "1024"

  # Some additional users users which needs to be created after initializing new cluster
  users:
    replicator:
      password: {{ pass_pg_replicator }}
      options:
        - replication
    admin:
      password: {{ pass_pg_admin }}
      options:
        - createrole
        - createdb
    {{ postgres_sync_ldap_user }}:
      password: {{ postgres_sync_ldap_pass }}
      options:
        - superuser
        - createdb
        - createrole
postgresql:
  basebackup:
    checkpoint: fast
  listen: 0.0.0.0:{{ pgcluster_port }}
  connect_address: {{ ansible_default_ipv4.address }}:{{ pgcluster_port }}
  data_dir: {{ pgcluster_path_data_psql }}
  bin_dir: {{ pgcluster_postgresql_bin_dir }}
  config_dir: {{ pgcluster_path_data_psql }}
  restore: /usr/bin/patroni_wale_restore
  pgpass: /tmp/.pgpass
  pg_hba:
    - "local all {{ pgcluster_user }} peer"
    - host replication replicator 0.0.0.0/0 md5
{% if pgcluster_md5 %}    - host all all 0.0.0.0/0 md5{% endif +%}
    - host all replicator 0.0.0.0/0 md5
    - host all {{ pgcluster_user }} 0.0.0.0/0 md5
    - host all admin 0.0.0.0/0 md5
    - host all zbx_monitor 0.0.0.0/0 md5
    - host all {{postgres_sync_ldap_user}} 0.0.0.0/0 md5
    - host all all 0.0.0.0/0 ldap ldapserver={{ ipa_vip_name }} ldapbasedn="cn=accounts,dc=sl,dc=local" ldapsearchattribute=uid ldapport=389

  authentication:
    rewind:
      username: {{ pgcluster_user }}
      password: {{ pass_pg_postgres }}
    replication:
      username: replicator
      password: {{ pass_pg_replicator }}
    superuser:
      username: {{ pgcluster_user }}
      password: {{ pass_pg_postgres }}
  parameters:
{% if pgcluster_idle_in_transaction_session_timeout is defined %}    idle_in_transaction_session_timeout: "{{ pgcluster_idle_in_transaction_session_timeout }}"{% endif +%}
{% if pgcluster_idle_session_timeout is defined %}    idle_session_timeout: "{{ pgcluster_idle_session_timeout }}"{% endif +%}
    archive_mode: {% if pgcluster_archive_mode is defined %}'{{ pgcluster_archive_mode }}'{% else %}"on"{% endif +%}
    archive_command: {% if pgcluster_archive_command is defined %}'{{ pgcluster_archive_command }}'{% else %}pgbackrest --stanza={{ cluster_name }} --log-level-file=info archive-push %p{% endif +%}
    unix_socket_directories: '/tmp'
    shared_buffers: {{ pgcluster_patroni_sb }}
    work_mem: {% if pgcluster_work_mem is defined %}{{ pgcluster_work_mem }}{% else %}32768kB{% endif +%}
    checkpoint_timeout: 30min
    constraint_exclusion: partition
    maintenance_work_mem: {% if pgcluster_maintenance_work_mem is defined %}{{ pgcluster_maintenance_work_mem }}{% else %}2GB{% endif +%}
    effective_cache_size: {% if pgcluster_effective_cache_size is defined %}{{ pgcluster_effective_cache_size }}{% else %}6GB{% endif +%}
    wal_buffers: -1
    max_wal_senders: 10
    min_wal_size: {{ pgcluster_min_wal_size }}
    max_wal_size: {{ pgcluster_max_wal_size }}
    wal_level: replica
    checkpoint_completion_target: "{% if pgcluster_checkpoint_completion_target is defined %}{{ pgcluster_checkpoint_completion_target }}{% else %}0.7{% endif +%}"
    default_statistics_target: "{% if pgcluster_default_statistics_target is defined %}{{ pgcluster_default_statistics_target }}{% else %}100{% endif +%}"
    autovacuum: "on"
    autovacuum_work_mem: 1GB
    autovacuum_vacuum_threshold: "{% if pgcluster_autovacuum_vacuum_threshold is defined %}{{ pgcluster_autovacuum_vacuum_threshold }}{% else %}50{% endif +%}"
    autovacuum_vacuum_cost_limit: 2000
    autovacuum_vacuum_scale_factor: 0.05
    autovacuum_analyze_threshold: "{% if pgcluster_autovacuum_analyze_threshold is defined %}{{ pgcluster_autovacuum_analyze_threshold }}{% else %}50{% endif +%}"
    autovacuum_analyze_scale_factor: 0.05
{% if pgcluster_autovacuum_max_workers is defined %}    autovacuum_max_workers: "{{ pgcluster_autovacuum_max_workers }}"{% endif +%}
{% if pgcluster_autovacuum_naptime is defined %}    autovacuum_naptime: "{{ pgcluster_autovacuum_naptime }}"{% endif +%}
    logging_collector: "on"
    log_connections: "on"
    log_statement: "ddl"
    log_duration: "off"
    log_destination: "csvlog,stderr"
    log_line_prefix: '[%t][%p][%c-%l][%x][%e]%q (%u, %d, %r, %a)'
    log_directory: "/var/log/postgresql"
    log_filename: 'postgresql.log'
    log_truncate_on_rotation: "off"
    log_rotation_age: 0
    log_rotation_size: 0
    log_min_duration_statement: '-1'
    log_checkpoints: "on"
    log_lock_waits: "on"
    log_temp_files: 0
    log_timezone: "UTC"
    datestyle: "iso, mdy"
    timezone: "UTC"
    default_text_search_config: "pg_catalog.english"
{% if pgcluster_max_standby_archive_delay is defined %}    max_standby_archive_delay: "{{ pgcluster_max_standby_archive_delay }}"{{'\n'}}{% endif %}
{% if pgcluster_max_standby_streaming_delay is defined %}    max_standby_streaming_delay: "{{ pgcluster_max_standby_streaming_delay }}"{{'\n'}}{% endif %}
{% if pgcluster_max_worker_processes is defined %}    max_worker_processes: "{{ pgcluster_max_worker_processes }}"{{'\n'}}{% endif %}
{% if pgcluster_max_parallel_workers_per_gather is defined %}    max_parallel_workers_per_gather: "{{ pgcluster_max_parallel_workers_per_gather }}"{{'\n'}}{% endif %}
{% if pgcluster_max_parallel_workers is defined %}    max_parallel_workers: "{{ pgcluster_max_parallel_workers }}"{{'\n'}}{% endif %}
{% if pgcluster_timescaledb_max_background_workers is defined %}    timescaledb.max_background_workers: "{{ pgcluster_timescaledb_max_background_workers }}"{{'\n'}}{% endif %}
{% if pgcluster_random_page_cost is defined %}    random_page_cost: "{{ pgcluster_random_page_cost }}"{{'\n'}}{% endif %}
{% if pgcluster_effective_io_concurrency is defined %}    effective_io_concurrency: "{{ pgcluster_effective_io_concurrency }}"{{'\n'}}{% endif %}
    shared_preload_libraries: {% if pgcluster_timescaledb is defined and pgcluster_timescaledb %}pg_stat_statements, timescaledb{% elif pgcluster_pg_stat_monitor %}pg_stat_statements, pg_stat_monitor{% else %}pg_stat_statements{% endif +%}

watchdog:
  mode: off

tags:
    nofailover: {{ (inventory_hostname[-1] in pgcluster_patroni_no_failover) | ternary("true","false") }}
    noloadbalance: false
    clonefrom: false
    nosync: false
