global
    maxconn 1000

defaults
    log global
    mode tcp
    retries 2
    timeout client 30m
    timeout connect 4s
    timeout server 30m
    timeout check 5s

listen stats
    mode http
    bind {{ vip_address }}:7000
    stats enable
    stats uri /

listen metrics
    mode http
    bind {{ ansible_host }}:8404
    stats enable
    stats uri /stats
    stats refresh 10s
    http-request use-service prometheus-exporter if { path /metrics }

listen writer
    bind {{ vip_address }}:5432
    option httpchk
    http-check expect status 200{% raw %}{{ if keyExists "service/{% endraw %}{{ cluster_name }}{% raw %}/leader" }}{{$leader_fqdn := key "service/{% endraw %}{{ cluster_name }}{% raw %}/leader"}}
    server {{$leader_fqdn}} {{with $d := printf "service/{% endraw %}{{ cluster_name }}{% raw %}/members/%s" $leader_fqdn | key | parseJSON}}{{$d.conn_url | regexReplaceAll "postgres://(.*)/postgres" "$1"}}{{end}} check port {{with $d := printf "service/{% endraw %}{{ cluster_name }}{% raw %}/members/%s" $leader_fqdn | key | parseJSON}}{{$d.api_url | regexReplaceAll "http://*.*.*.*:(.*)/patroni" "$1"}}{{end}} inter 3s fall 3 rise 2{{end}}{% endraw %}

listen reader
    bind {{ vip_address }}:5440
    {% if vip_address_ro is defined %}bind {{ vip_address_ro }}:5440
{% endif %}
    option httpchk /health
    http-check expect status 200{%raw%}{{range ls "service/{% endraw %}{{ cluster_name }}{% raw %}/members"}}{{$leader_fqdn := key "service/{% endraw %}{{ cluster_name }}{% raw %}/leader"}}{{if ne .Key $leader_fqdn}}
    server {{.Key}} {{with $d := printf "service/{% endraw %}{{ cluster_name }}{% raw %}/members/%s" .Key | key | parseJSON}}{{$d.conn_url | regexReplaceAll "postgres://(.*)/postgres" "$1"}}{{end}} check port {{with $d := printf "service/{% endraw %}{{ cluster_name }}{% raw %}/members/%s" $leader_fqdn | key | parseJSON}}{{$d.api_url | regexReplaceAll "http://*.*.*.*:(.*)/patroni" "$1"}}{{end}} inter 3s fall 3 rise 2{{end}}{{end}}{% endraw %}
