---
# tasks file for roles/consul-agents

- name: Check condition for install
  ansible.builtin.assert:
    fail_msg: This server is not for install ClamAV
    success_msg: This server is for install ClamAV
    that:
      - "{{ clamav_install | default(false) }}"

- name: Install clamav clamav_packages
  ansible.builtin.apt:
    name: "{{ item }}"
    cache_valid_time: "{{ clamav_apt_cache_valid_time }}"
  loop: "{{ clamav_packages }}"
