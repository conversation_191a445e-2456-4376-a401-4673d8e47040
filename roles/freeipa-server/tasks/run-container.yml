---
- name: Create container
  community.docker.docker_container:
    image: "{{ ipa_container_image }}"
    pull: true
    name: freeipa-server
    state: started
    hostname: "{{ inventory_hostname }}.sl.local"
    restart: true
    restart_policy: always
    tty: true
    interactive: true
    etc_hosts: >
      {
        "{{ ipa_replica_hostname }}": "{{ ipa_replica_ip }}"
      }
    network_mode: host
    cgroupns_mode: host
    volumes:
      - /app/ipadata:/data:Z
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
    capabilities: SYS_TIME
    command:
      - ipa-server-install
      - --unattended
      - --realm={{ ipa_realm }}
      - --no-ntp
      - --ds-password={{ ds_password }}
      - --admin-password={{ admin_password }}
      - --ip-address={{ ansible_host }}
      - --domain={{ ipa_domain }}
      - --setup-dns
      - --allow-zone-overlap
      - --forwarder={{ dns1 }}
      - --forwarder={{ dns2 }}
      - --ssh-trust-dns
      - --auto-forwarders
      - --netbios-name=SELLERLOGIC
      - --auto-reverse
      - --no-dnssec-validation
