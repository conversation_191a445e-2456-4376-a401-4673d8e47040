---
- name: Add php key
  ansible.builtin.apt_key:
    url: https://packages.sury.org/php/apt.gpg
    id: 15058500A0235D97F5D10063B188E2B695BD4743
    state: present

- name: Added nginx key
  ansible.builtin.apt_key:
    url: https://packages.sury.org/nginx/apt.gpg
    state: present

- name: Added php repo
  ansible.builtin.apt_repository:
    repo: ppa:ondrej/php
    state: present
    update_cache: true

- name: Added nginx repo
  ansible.builtin.apt_repository:
    repo: ppa:ondrej/nginx
    state: present
    update_cache: true

- name: Install php packages
  ansible.builtin.apt:
    name: "php{{ php_install_version }}-{{ item }}"
    state: present
  loop: "{{ php_packages }}"

- name: Fix default php version
  community.general.alternatives:
    name: php
    path: /usr/bin/php{{ php_install_version }}

- name: Add groups
  ansible.builtin.group:
    name: "{{ item }}"
    state: present
  loop: "{{ php_users }}"

- name: Add users
  ansible.builtin.user:
    name: "{{ item }}"
    groups: "{{ item }}"
    state: present
  loop: "{{ php_users }}"
