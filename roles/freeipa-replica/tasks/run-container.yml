---
- name: Create container
  community.docker.docker_container:
    image: "{{ ipa_container_image }}"
    name: freeipa-replica
    pull: true
    state: started
    dns_servers:
      - "{{ ipa_server_ip }}"
      - 127.0.0.1
      - "{{ dns1 }}"
      - "{{ dns2 }}"
    tty: true
    interactive: true
    restart: true
    restart_policy: always
    etc_hosts: >
      {
        "{{ ipa_server_hostname }}.sl.local": "{{ ipa_server_ip }}"
      }
    capabilities: SYS_TIME
    network_mode: host
    cgroupns_mode: host
    volumes:
      - /app/ipadata:/data:Z
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
    command:
      - ipa-replica-install
      - --domain={{ ipa_domain }}
      - --ip-address={{ ipa_bind_addr }}
      - --server={{ ipa_server_hostname }}.sl.local
      - --admin-password={{ admin_password }}
      - --no-host-dns
      - --setup-dns
      - --auto-forwarders
      - --allow-zone-overlap
      - --unattended
      - --skip-connchec
      - --setup-ca
      - --auto-reverse
      - --no-dnssec-validation
