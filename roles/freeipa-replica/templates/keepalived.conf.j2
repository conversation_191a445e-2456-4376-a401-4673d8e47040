vrrp_script chk_ldap {
    script "{{ ipa_keepalived_etc_dir }}/check.sh"
    interval 2
    fall 2
}

vrrp_instance VI_1 {
    interface ens18
    state BACKUP
    priority 100

    virtual_router_id 48
    unicast_src_ip {{ ansible_ssh_host }}
    unicast_peer {
                 {{ ansible_ssh_host }}
                 {{ ipa_replica_ip }}
                 {{ ipa_server_ip }}
            }

    authentication {
        auth_type PASS
        auth_pass {{ ipa_keepalived_pass }}
    }

    track_script {
        chk_ldap
    }

    virtual_ipaddress {
        {{ ipa_vip }}
    }
}
