---
- name: Create kea-ctrl-agent.conf
  ansible.builtin.template:
    src: kea-ctrl-agent.conf.j2
    dest: "{{ kea_dhcp_server_api_config }}"
    mode: 0644
  notify: Restart kea-ctrl-agent
  tags: kea-ctrl-agent-config

- name: Create kea-ctrl-agent.service
  ansible.builtin.template:
    src: kea-ctrl-agent.service.j2
    dest: /etc/systemd/system/kea-ctrl-agent.service
    mode: 0644
  notify: Restart kea-ctrl-agent
  tags: kea-ctrl-agent-config

- name: Start kea-ctrl-agent
  ansible.builtin.systemd:
    name: kea-ctrl-agent
    state: started
    enabled: true
    daemon_reload: true
  tags: kea-ctrl-agent-config
