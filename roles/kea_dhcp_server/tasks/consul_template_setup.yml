---
- name: Download Consul Template binary
  ansible.builtin.get_url:
    url: "{{ kea_dhcp_server_consul_template_url }}"
    dest: "/tmp/consul-template.zip"
    mode: 0644
  tags: consul-template-setup

- name: Unzip Consul Template binary
  ansible.builtin.unarchive:
    src: "/tmp/consul-template.zip"
    dest: "{{ kea_dhcp_server_consul_template_bin_path | dirname }}"
    remote_src: true
  when: not ansible_check_mode
  tags: consul-template-setup

- name: Make Consul Template executable
  ansible.builtin.file:
    path: "{{ kea_dhcp_server_consul_template_bin_path }}"
    mode: "a+x"
  tags: consul-template-setup

- name: Create Consul Template config directory
  ansible.builtin.file:
    path: "{{ kea_dhcp_server_consul_template_conf_dir }}"
    state: directory
    mode: 0755
  tags: consul-template-setup

- name: Configure consul-template service
  ansible.builtin.template:
    src: consul-template.service.j2
    dest: /etc/systemd/system/consul-template.service
    mode: 0644
  tags: consul-template-setup

- name: Start and enable consul-template service
  ansible.builtin.systemd:
    name: consul-template
    state: restarted
    enabled: true
    daemon_reload: true
  tags: consul-template-setup

- name: Install python-consul on Ubuntu 24.04
  ansible.builtin.pip:
    name: python-consul
    state: present
    extra_args: "--break-system-packages"
  tags: consul-template-setup
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'

- name: Install python-consul
  ansible.builtin.pip:
    name: python-consul
    state: present
  tags: consul-template-setup
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'
