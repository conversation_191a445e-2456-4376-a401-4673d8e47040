---
- name: <PERSON><PERSON> <PERSON>a DHCP config to Consul
  community.general.consul_kv:
    key: "infra-dhcp/config"
    value: "{{ lookup('template', 'kea-dhcp4.conf.j2') | to_nice_json }}"
    token: "{{ consul_cluster_acl_token }}"
    host: "{{ consul_api_host }}"
    port: "{{ consul_api_port }}"
  tags: kea-config

- name: Configure consul-template config file
  ansible.builtin.template:
    src: consul-template.hcl.j2
    dest: "{{ kea_dhcp_server_consul_template_conf_dir }}/consul-template.hcl"
    mode: 0644
  notify: Restart consul-template
  tags: kea-config

- name: Configure consul-template dhcp config file
  ansible.builtin.template:
    src: dhcp.tpl.j2
    dest: "{{ kea_dhcp_server_consul_template_conf_dir }}/dhcp.tpl"
    mode: 0644
  notify: Restart consul-template
  tags: kea-config
