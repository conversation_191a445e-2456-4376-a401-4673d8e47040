{
    "Dhcp4": {
        "match-client-id": false,
        "loggers": [
        {
            "name": "kea-dhcp4",
            "severity": "{{ kea_dhcp_server_log_level }}",
            "output_options": [
            {
                "output": "{{ kea_dhcp_server_log_path }}/kea-dhcp4.log",
                "maxver": {{ kea_dhcp_server_log_count }}
            }
            ]
        },
        {
            "name": "kea-dhcp4.dhcpsrv",
            "severity": "{{ kea_dhcp_server_log_level }}",
            "output_options": [
            {
                "output": "{{ kea_dhcp_server_log_path }}/kea-dhcp4-dhcpsrv.log",
                "maxver": {{ kea_dhcp_server_log_count }}
            }
            ]
        },
        {
            "name": "kea-dhcp4.leases",
            "severity": "DEBUG",
            "debuglevel": 50,
            "output_options": [
            {
                "output": "{{ kea_dhcp_server_log_path }}/kea-dhcp4-leases.log",
                "maxver": {{ kea_dhcp_server_log_count }}
            }
            ]
        }
        ],
        "option-data": [
            {
                "space": "dhcp4",
                "name": "domain-name",
                "code": 15,
                "data": "sl.local"
            },
            {
                "space": "dhcp4",
                "name": "domain-name-servers",
                "code": 6,
                "data": "*************, ************, ************, *************"
            }
        ],
        "interfaces-config": {
            "interfaces": [ "ens18" ],
            "service-sockets-max-retries": 5,
            "service-sockets-retry-wait-time": 5000
        },
        "valid-lifetime": 518400,
        "max-valid-lifetime": 1440000,
        "authoritative": true,
        "control-socket": {
            "socket-type": "unix",
            "socket-name": "/run/kea/kea4-ctrl-socket"
        },
        "subnet4": [
            {
                "id": 1,
                "subnet": "192.168.2.0/23",
                "option-data": [
                    {
                        "space": "dhcp4",
                        "name": "routers",
                        "code": 3,
                        "data": "192.168.2.1"
                    },
                    {
                        "space": "dhcp4",
                        "name": "subnet-mask",
                        "code": 1,
                        "data": "255.255.254.0"
                    },
                    {
                        "space": "dhcp4",
                        "name": "domain-search",
                        "code": 119,
                        "data": "sl.local"
                    },
                    {
                        "space": "dhcp4",
                        "name": "domain-name-servers",
                        "code": 6,
                        "data": "*************, ************, ************, *************"
                    },
                    {
                        "space": "dhcp4",
                        "name": "time-offset",
                        "code": 2,
                        "data": "-18000"
                    }
                ],
                "pools": [
                    {
                        "pool": "************* - *************"
                    }
                ]
            }
        ],
        "host-reservation-identifiers": [
            "hw-address"
        ],
        "reservation-mode": "global",
        "reservations": [
            {% for host in groups['all_server'] %}
            {% if 'mac_address' in hostvars[host] %}
            {
                "hostname": "{{ host }}",
                "hw-address": "{{ hostvars[host]['mac_address'] }}",
                "ip-address": "{{ hostvars[host]['ansible_host'] }}"
            }{% if not loop.last %},{% endif %}
            {% endif %}
            {% endfor %}
        ]
    }
}
