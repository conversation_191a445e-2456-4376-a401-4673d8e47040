log_level = "debug"

consul {
  address = "{{ consul_api_url }}"
  token   = "{{ consul_cluster_acl_token }}"
}

log_file {
  path = "/var/log/default/consul-template.log"
  log_rotate_bytes = 1024000
  log_rotate_duration = "3h"
  log_rotate_max_files = 10
}

template {
  source = "{{ kea_dhcp_server_consul_template_conf_dir }}/dhcp.tpl"
  destination = "/etc/kea/kea-dhcp4.conf"
  command = "systemctl restart kea-dhcp4-server.service"
}
