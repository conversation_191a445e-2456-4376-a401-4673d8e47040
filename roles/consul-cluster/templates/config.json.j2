{
    "server": true,
    "enable_debug": true,
    "datacenter": "hz",
    "bind_addr": "{{ ansible_default_ipv4.address }}",
    "domain": "{{ consul_cluster_domain }}",
    "data_dir": "/bitnami/consul",
    "pid_file": "/opt/bitnami/consul/tmp/consul.pid",
    "ui_config": {
        "enabled": true
    },
    "bootstrap_expect": {{ consul_bootstrap }},
    "enable_script_checks": true,
    "encrypt": "{{ consul_cluster_gossip_encryption_key }}",
    "addresses": {
        "http": "0.0.0.0"
    },
    "leave_on_terminate": true,
    "rejoin_after_leave": true,
    "retry_join": [
        {% for i in groups[consul_cluster_join_address] %}
        "{{ hostvars[i]['ansible_default_ipv4']['address'] }}"
        {% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    "ports": {
        "http": 7500,
        "dns": 7600,
        "serf_lan": 7301,
        "server": 7300
    },
    "serf_lan": "0.0.0.0",
	"acl": { 
		"enabled": true,
		"default_policy": "allow",
		"enable_token_persistence": true,
        "enable_token_replication": true,
        "down_policy": "extend-cache",
		"tokens": {
            "initial_management": "{{ consul_cluster_acl_token}}",
			"master": "{{ consul_cluster_acl_token}}",
			"agent": "{{ consul_cluster_acl_token}}"
		}
	},
    "connect": {
        "enabled": true
    },
    "limits": {
        "http_max_conns_per_client": {{ consul_http_max_conns_per_client }}
    }
}
