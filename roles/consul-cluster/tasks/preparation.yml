---
# tasks file for roles/consul-cluster
- name: Create a directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: "{{ item.state }}"
    mode: 0777
  loop:
    - {path: "{{ consul_cluster_dir }}", state: directory}
    - {path: "{{ consul_cluster_data_dir }}", state: directory}
    - {path: "{{ consul_cluster_config_dir }}", state: directory}
- name: Copy config for consul
  ansible.builtin.template:
    src: config.json.j2
    dest: "{{ consul_cluster_config_dir }}/consul.json"
    mode: 0644
