---
# tasks file for roles/consul-cluster
- name: Create a Consul container
  community.docker.docker_container:
    image: "{{ consul_image }}"
    name: consul-server
    state: started
    pull: true
    restart: true
    restart_policy: always
    network_mode: host
    mounts:
      - source: "{{ consul_cluster_data_dir }}"
        target: /bitnami
        type: bind
      - source: "{{ consul_cluster_config_dir }}"
        target: /opt/bitnami/consul/conf
        type: bind
    comparisons:
      mounts: strict
