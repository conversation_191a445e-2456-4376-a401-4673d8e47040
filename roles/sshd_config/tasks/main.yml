---
- name: Setup sshd_config
  ansible.builtin.template:
    dest: /etc/ssh/sshd_config
    src: sshd_config.j2
    owner: root
    group: root
    mode: 0644

- name: Restart service sshd
  ansible.builtin.systemd:
    name: sshd
    state: restarted
    enabled: true
    daemon_reload: true
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04'

- name: Restart service ssh
  ansible.builtin.systemd:
    name: ssh
    state: restarted
    enabled: true
    daemon_reload: true
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'
