---
- name: Run container
  community.docker.docker_container:
    image: "{{ keycloak_container_image }}"
    name: keycloak
    state: started
    pull: true
    restart: true
    restart_policy: always
    tty: true
    interactive: true
    ports:
      - "80:8080"
      - "443:8443"
    volumes:
      - "{{ keycloak_cert_dir }}/{{ kc_cert_file }}:/etc/x509/https/tls.crt"
      - "{{ keycloak_cert_dir }}/{{ kc_cert_key }}:/etc/x509/https/tls.key"
      - "{{ keycloak_cert_dir }}/{{ kc_key_store_file }}:/etc/x509/https/keystore.jks"
      - "{{ keycloak_cert_dir }}/{{ kc_trust_store_file }}:/etc/x509/https/truststore.jks"
    env:
      KEYCLOAK_USER: "{{ keycloak_admin }}"
      KEYCLOAK_PASSWORD: "{{ keycloak_admin_password }}"
      KEYCLOAK_ADMIN: "{{ keycloak_admin }}"
      KEYCLOAK_ADMIN_PASSWORD: "{{ keycloak_admin_password }}"
      KC_DB: "{{ kc_db_type }}"
      KC_DB_USERNAME: "{{ kc_db_user }}"
      KC_DB_PASSWORD: "{{ kc_db_password }}"
      KC_DB_URL_DATABASE: "{{ kc_db_name }}"
      KC_DB_URL_HOST: "{{ kc_db_host }}"
      KC_DB_URL_PORT: "{{ kc_db_port }}"
      KC_HOSTNAME: "{{ nginx_reverse_hostname }}"
      JAVA_OPTS_APPEND: -Dkeycloak.migration.strategy=OVERWRITE_EXISTING, -Dkeycloak.profile.feature.scripts=enabled
      KC_HTTPS_CERTIFICATE_KEY_FILE: /etc/x509/https/tls.key
      KC_HTTPS_CERTIFICATE_FILE: /etc/x509/https/tls.crt
      KC_HTTPS_KEY_STORE_FILE: /etc/x509/https/keystore.jks
      KC_HTTPS_KEY_STORE_PASSWORD: "{{ kc_key_store_pass }}"
      KC_HTTPS_TRUST_STORE_FILE: /etc/x509/https/truststore.jks
      KC_HTTPS_TRUST_STORE_PASSWORD: "{{ kc_trust_store_pass }}"
      PROXY_ADDRESS_FORWARDING: "true"
    command:
      - start
      - --auto-build
      - --db={{ kc_db_type }}
      - --proxy edge
      - --spi-truststore-file-file=/etc/x509/https/truststore.jks
      - --spi-truststore-file-password={{ kc_trust_store_pass }}
      - --spi-truststore-file-hostname-verification-policy=ANY
      - --http-relative-path=/auth
