---
- name: Import trusted CA from SSL certificate
  community.general.java_cert:
    cert_path: "{{ kc_freeipa_ca }}"
    keystore_path: "{{ keycloak_cert_dir }}/{{ kc_trust_store_file }}"
    keystore_pass: "{{ kc_trust_store_pass }}"
    keystore_create: true
    state: present
    cert_alias: FREEIPA_CA
    trust_cacert: true
- name: Create private key (RSA, 4096 bits)
  community.crypto.openssl_privatekey:
    path: "{{ keycloak_cert_dir }}/{{ kc_cert_key }}"
- name: Create certificate signing request (CSR)
  community.crypto.openssl_csr_pipe:
    privatekey_path: "{{ keycloak_cert_dir }}/{{ kc_cert_key }}"
    common_name: "{{ inventory_hostname }}.sl.local"
    organization_name: Sellerlogic
    subject_alt_name:
      - "DNS:{{ inventory_hostname }}.sl.local"
      - "DNS:www.{{ inventory_hostname }}.sl.local"
  register: csr
- name: Create self-signed certificate from CSR
  community.crypto.x509_certificate:
    path: "{{ keycloak_cert_dir }}/{{ kc_cert_file }}"
    csr_content: "{{ csr.csr }}"
    privatekey_path: "{{ keycloak_cert_dir }}/{{ kc_cert_key }}"
    provider: selfsigned
- name: Create a keystore
  community.general.java_keystore:
    name: "{{ inventory_hostname }}"
    certificate_path: "{{ keycloak_cert_dir }}/{{ kc_cert_file }}"
    private_key_path: "{{ keycloak_cert_dir }}/{{ kc_cert_key }}"
    password: "{{ kc_key_store_pass }}"
    dest: "{{ keycloak_cert_dir }}/{{ kc_key_store_file }}"
- name: Fix permissions
  ansible.builtin.file:
    path: "{{ keycloak_cert_dir }}"
    mode: "0775"
    state: directory
    recurse: true
