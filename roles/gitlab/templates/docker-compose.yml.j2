version: '3.7'
services:
  gitlab:
    image: 'gitlab/gitlab-ce:{{ gitlab_version }}'
    restart: always
    container_name: gitlab
    hostname: '{{ gitlab_domain }}'
    volumes:
      - '{{ gitlab_data_dir }}:/var/opt/gitlab'
      - '{{ gitlab_log_dir }}:/var/log/gitlab'
      - '{{ gitlab_config_dir }}:/etc/gitlab'
      - '{{ gitlab_ssl_dir }}:/etc/gitlab/ssl'
      - '{{ ipa_ca_path }}:/etc/gitlab/ssl/ca.crt:ro'
      - '{{ ipa_ca_path }}:/etc/gitlab/trusted-certs/ca-freeipa.crt'
      - '{{ gitlab_backup_dir }}:/var/opt/gitlab/backups'
      - '{{ gitlab_root_dir }}/hook_block_confidentials.sh:/opt/gitlab/embedded/service/gitlab-shell/hooks/pre-receive.d/hook_block_confidentials.sh'
    shm_size: '{{ gitlab_docker_shm_size }}'
    networks:
      gitlab-network:
        ipv4_address: {{ gitlab_internal_ip }}
    logging:
      driver: {{ gitlab_docker_log_driver }}
      options:
        max-size: "{{ gitlab_docker_log_max_size }}"
        max-file: "{{ gitlab_docker_log_max_file }}"

networks:
  gitlab-network:
    name: gitlab-network
    driver: macvlan
    driver_opts:
      parent: "{{ gitlab_network_interface }}"
      ipvlan_mode: "{{ gitlab_network_ipvlan_mode }}"
    ipam:
      config:
        - subnet: "{{ gitlab_network_subnet }}"
          ip_range: "{{ gitlab_network_ip_range }}"
          gateway: "{{ gitlab_network_gateway }}"
