---
- name: Install prometheus package
  ansible.builtin.pip:
    name: prometheus-pve-exporter
    state: present
- name: Create folder
  ansible.builtin.file:
    path: /etc/prometheus
    state: directory
    mode: '0755'
- name: Copy template
  ansible.builtin.template:
    src: pve.yml.j2
    dest: /etc/prometheus/pve.yml
    mode: '0644'
- name: Copye template service
  ansible.builtin.template:
    src: pve-exporter.service.j2
    dest: /etc/systemd/system/prometheus-pve-exporter.service
    mode: '0644'
- name: Start service
  ansible.builtin.systemd:
    name: prometheus-pve-exporter
    state: started
    enabled: true
- name: <PERSON>p install
  ansible.builtin.pip:
    name: prometheus-pve-exporter
    state: present
