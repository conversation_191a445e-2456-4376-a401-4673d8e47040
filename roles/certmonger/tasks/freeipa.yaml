---
- name: Create services
  freeipa.ansible_freeipa.ipaservice:
        ipaadmin_password: "{{ admin_password }}"
        name: "HTTP/{{ item.url }}"
        skip_host_check: true
        force: true
  loop: "{{ certmonger }}"

- name: Add hosts to services
  ansible.builtin.shell: >-
    echo "{{ admin_password }}" | kinit {{ ipa_admin_user }}
    && ipa service-add-host --host {{ inventory_hostname }} HTTP/{{ item.url }}
  loop: "{{ certmonger }}"
  register: host_to_service
  ignore_errors: true
  failed_when: not (( host_to_service.rc == 0 ) or ( host_to_service.rc == 1 and "This entry is already a member" in host_to_service.stdout))
  changed_when: host_to_service.rc == 0
