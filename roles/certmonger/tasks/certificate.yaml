---
- name: Define and set variable certificate_requests for linux-system-roles.certificate role
  ansible.builtin.set_fact:
        certificate_requests: >-
          {{ certificate_requests +
          [{'name': item.location,
          'dns': item.url,
          'ca': 'ipa',
          'principal': 'HTTP/' + item.url + '@' + ipa_realm}]
          }}
  loop: "{{ certmonger }}"
  vars:
        certificate_requests: []

- name: Import linux-system-roles.certificate role
  ansible.builtin.import_role:
        name: linux-system-roles.certificate
