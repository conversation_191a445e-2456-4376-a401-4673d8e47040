---
- name: Install roles from global requirements.yml
  ansible.builtin.command:
    cmd: ansible-galaxy install -r requirements.yml
    chdir: ..
  run_once: true
  tags: galaxy
  become: false
  register: galaxy_out
  delegate_to: localhost
  changed_when: '" was installed successfully" in galaxy_out.stdout'
  failed_when: >
    (galaxy_out.rc !=0) or
    ('is already installed, skipping' not in galaxy_out.stdout and ' was installed successfully' not in galaxy_out.stdout)
