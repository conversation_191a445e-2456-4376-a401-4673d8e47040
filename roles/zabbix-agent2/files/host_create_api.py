import csv
import json
import os
import sys
from pyzabbix import ZabbixAPI, ZabbixAPIException

ZABBIX_API_URL = sys.argv[1]
AUTHTOKEN = sys.argv[2]
HOST_IP = sys.argv[3]
agent_port = '10050'
proxy_name = 'hz-infra-zabbix-proxy-02.sl.local'
group_name = 'Discovered hosts'
template_name = 'Website certificate by Zabbix agent 2'
path = '/etc/nginx/sites-enabled/'
files = '/tmp/hosts.csv'

zapi = ZabbixAPI(ZABBIX_API_URL)
zapi.login(api_token=AUTHTOKEN)

proxy_id = zapi.proxy.get(filter={"host": proxy_name})[0]['proxyid']
group_id = zapi.hostgroup.get(filter={"name": group_name})[0]['groupid']
template_id = zapi.template.get(filter={"host": template_name})[
    0]['templateid']

for line in os.listdir(path):
    if line != 'default.conf':
        line = line[:-5] + '\n'
        with open(files, 'a+') as f:
            f.write(line)

print("\nCreate hosts")
f = csv.reader(open(files))
for [hostname] in f:
    try:
        existing_hosts = zapi.host.get(filter={"host": hostname})
        if existing_hosts:
            print(f"Host '{hostname}' already exists. Skipping...")
            continue

        host_create_params = {
            "host": hostname,
            "interfaces": [
                {
                    "type": 1,
                    "main": 1,
                    "useip": 1,
                    "ip": HOST_IP,
                    "dns": "",
                    "port": agent_port,
                    "proxy_hostid": proxy_id
                }
            ],
            "groups": [{"groupid": group_id}],
            "templates": [{"templateid": template_id}],
            "macros": [
                {
                    "macro": "{$CERT.WEBSITE.HOSTNAME}",
                    "value": hostname
                },
                {
                    "macro": "{$CERT.WEBSITE.IP}",
                    "value": hostname
                }
            ],
            "proxy_hostid": proxy_id
        }

        result = zapi.host.create(host_create_params)
        print(json.dumps(result, indent=4, sort_keys=True))
    except ZabbixAPIException as e:
        print(f"Failed to create host '{hostname}': {e}")

os.remove(files)
