# ZFS discovery and configuration
# original template from p<PERSON><PERSON> (source = https://www.zabbix.com/forum/showthread.php?t=43347), modified by Slash <<EMAIL>>


# pool discovery
UserParameter=zfs.pool.discovery,/sbin/zpool list -H -o name | sed -e '$ ! s/\(.*\)/{"{#POOLNAME}":"\1"},/' | sed -e '$  s/\(.*\)/{"{#POOLNAME}":"\1"}]}/' | sed -e '1  s/\(.*\)/{ \"data\":[\1/'
# dataset discovery, called "fileset" in the zabbix template for legacy reasons
UserParameter=zfs.fileset.discovery,/sbin/zfs list -H -o name | sed -e '$ ! s/\(.*\)/{"{#FILESETNAME}":"\1"},/' | sed -e '$  s/\(.*\)/{"{#FILESETNAME}":"\1"}]}/' | sed -e '1  s/\(.*\)/{ \"data\":[\1/'
# vdev discovery
UserParameter=zfs.vdev.discovery,/sbin/zpool list -Hv | grep '^[[:blank:]]' | egrep -v 'mirror|raidz' | awk '{print $1}' | sed -e '$ ! s/\(.*\)/{"{#VDEV}":"\1"},/' | sed -e '$  s/\(.*\)/{"{#VDEV}":"\1"}]}/' | sed -e '1  s/\(.*\)/{ \"data\":[\1/'

# pool health
UserParameter=zfs.zpool.health[*],/sbin/zpool list -H -o health $1

# get any fs option
UserParameter=zfs.get.fsinfo[*],/sbin/zfs get -o value -Hp $2 $1

# compressratio need special treatment because of the "x" at the end of the number
UserParameter=zfs.get.compressratio[*],/sbin/zfs get -o value -Hp compressratio $1 | sed "s/x//"

# memory used by ZFS: sum of the SPL slab allocator's statistics
# "There are a few things not included in that, like the page cache used by mmap(). But you can expect it to be relatively accurate."
UserParameter=zfs.memory.used,echo $(( `cat /proc/spl/kmem/slab | tail -n +3 | awk '{ print $3 }' | tr "\n" "+" | sed "s/$/0/"` ))

# get any global zfs parameters
UserParameter=zfs.get.param[*],cat /sys/module/zfs/parameters/$1

# ARC stats from /proc/spl/kstat/zfs/arcstats
UserParameter=zfs.arcstats[*],awk '/^$1/ {printf $$3;}' /proc/spl/kstat/zfs/arcstats

# detect if a scrub is in progress, 0 = in progress, 1 = not in progress
UserParameter=zfs.zpool.scrub[*],/sbin/zpool status $1 | grep "scrub in progress" > /dev/null ; echo $?

# vdev state
UserParameter=zfs.vdev.state[*],/sbin/zpool status | grep -m 1 "$1" | awk '{ print $$2 }'
# vdev READ error counter
UserParameter=zfs.vdev.error_counter.read[*],/sbin/zpool status | grep -m 1 "$1" | awk '{ print $$3 }' | numfmt --from=si
# vdev WRITE error counter
UserParameter=zfs.vdev.error_counter.write[*],/sbin/zpool status | grep -m 1 "$1" | awk '{ print $$4 }' | numfmt --from=si
# vdev CHECKSUM error counter
UserParameter=zfs.vdev.error_counter.cksum[*],/sbin/zpool status | grep -m 1 "$1" | awk '{ print $$5 }' | numfmt --from=si

# Get zpool iostats 
UserParameter=zfs.zpool.iostat[*],/sbin/zpool iostat $1 -H -p 1 2 | tail -n 1
