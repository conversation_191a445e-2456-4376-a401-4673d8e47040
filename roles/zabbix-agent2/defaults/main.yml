---
zabbix_agent_pid_file: '/tmp/zabbix_agent2.pid'
secret_zabbix: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          36666564306237636339643765333131326539366434363632393038653736353163323464326265
          3239663266353666343331616666353864353530396430630a356665333933663564376439383838
          66646633383934313737393230303539366535623562376530643932383363353363646130643338
          3731343231353431620a653037303139346638373364663961343530376466306161613738323439
          31393431643931393135373137623733316539643863343737376134643366393164

zabbix_agent_user: zabbix
zabbix_users_group:
  - name: "{{ zabbix_agent_user }}"
    groups: docker
zabbix_agent2_override_directory: /etc/systemd/system/zabbix-agent2.service.d
zabbix_agent2_override_file: override.conf
zabbix_agent2_service: zabbix-agent2
