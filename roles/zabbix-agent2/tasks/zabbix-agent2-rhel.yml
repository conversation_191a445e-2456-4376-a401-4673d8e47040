---
- name: Download zabbix repo package
  ansible.builtin.get_url:
    url: "https://repo.zabbix.com/zabbix/{{ zabbix_version }}/rhel/7/x86_64/\
      zabbix-agent2-{{ zabbix_version_patch }}-release1.el7.x86_64.rpm"
    dest: /tmp/zabbix.rpm
    mode: 0644
- name: Install zabbix repo
  become: true
  ansible.builtin.package:
    name: /tmp/zabbix.rpm
    state: present
- name: Install zabbix agent
  become: true
  ansible.builtin.package:
    name: zabbix-agent2
    state: present
- name: Create zabbix config file from template
  become: true
  ansible.builtin.template:
    dest: /etc/zabbix/zabbix_agent2.conf
    src: zabbix_agent2.conf.j2
    mode: 0644
- name: Start service zabbix-agent
  become: true
  ansible.builtin.service:
    name: zabbix-agent2
    state: restarted
    enabled: true
