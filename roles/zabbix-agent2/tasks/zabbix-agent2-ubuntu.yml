---
- name: Download zabbix repo package
  ansible.builtin.get_url:
    url:
      "https://repo.zabbix.com/zabbix/{{ zabbix_version }}/ubuntu/pool/main/z/zabbix-release/\
      zabbix-release_{{ zabbix_version_release }}+ubuntu{{ ansible_facts['distribution_version'] }}_all.deb"
    dest: /tmp/zabbix.deb
    mode: 0644
- name: Install zabbix repo
  become: true
  ansible.builtin.apt:
    deb: /tmp/zabbix.deb
    state: present
    update_cache: true
- name: Install zabbix agent and plugins
  become: true
  ansible.builtin.apt:
    name:
      - "zabbix-agent2=1:{{ zabbix_agent2_version }}-1+ubuntu{{ ansible_facts['distribution_version'] }}"
      - "zabbix-agent2-plugin-postgresql=1:{{ zabbix_agent2_version }}-1+ubuntu{{ ansible_facts['distribution_version'] }}"
    state: present
    update_cache: true
- name: Create zabbix config file from template
  become: true
  ansible.builtin.template:
    dest: /etc/zabbix/zabbix_agent2.conf
    src: zabbix_agent2.conf.j2
    mode: 0644
- name: Copy userparams file for zfs monitoring
  become: true
  ansible.builtin.copy:
    src: userparams_zol_without_sudo.conf
    dest: /etc/zabbix/zabbix_agent2.d
    mode: u=rw,g=r,o=r
- name: Start service zabbix-agent
  become: true
  ansible.builtin.service:
    name: zabbix-agent2
    state: restarted
    enabled: true
