---
- name: Find group on server
  ansible.builtin.shell: |
    set -o pipefail &&
    getent group | cut -d: -f1
  args:
    executable: /bin/bash
  changed_when: false
  register: unix_groups

- name: Add zabbix user to groups
  ansible.builtin.user:
    name: "{{ item.name }}"
    groups: "{{ item.groups }}"
    append: true
  when: "item.groups in unix_groups.stdout_lines"
  with_items: "{{ zabbix_users_group }}"
  notify: Restart zabbix-agent2

- name: Add smartctl for zabbix user to sudoers
  community.general.sudoers:
    commands: /usr/sbin/smartctl
    name: zabbix-smartctl
    user: zabbix
