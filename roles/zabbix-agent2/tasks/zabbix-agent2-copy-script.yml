---
- name: Create directory
  become: true
  ansible.builtin.file:
    path: /etc/zabbix/scripts
    state: directory
    mode: 0755

- name: Install pyzabbix on Ubuntu 24.04 and Debian
  become: true
  ansible.builtin.pip:
    name: pyzabbix
    state: present
    extra_args: "--break-system-packages"
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04' or ansible_distribution == 'Debian'
- name: Install pyzabbix
  become: true
  ansible.builtin.pip:
    name: pyzabbix
    state: present
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04'

- name: Copy script host_create_api.py
  become: true
  ansible.builtin.copy:
    src: files/host_create_api.py
    dest: /etc/zabbix/scripts/host_create_api.py
    mode: a+x
  when: "'lb' in inventory_hostname"
