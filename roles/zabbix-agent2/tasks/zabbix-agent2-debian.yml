---
- name: Download zabbix repo package
  ansible.builtin.get_url:
    url:
      "https://repo.zabbix.com/zabbix/{{ zabbix_version }}/debian/pool/main/z/zabbix/\
      zabbix-agent2_{{ zabbix_version_patch }}-1%2Bdebian{{ ansible_distribution_major_version }}_amd64.deb"
    dest: /tmp/zabbix.deb
    mode: 0644
- name: Install zabbix repo
  become: true
  ansible.builtin.apt:
    deb: /tmp/zabbix.deb
    state: present
- name: Install zabbix agent
  become: true
  ansible.builtin.apt:
    name: zabbix-agent2
    state: present
    update_cache: true
- name: Create zabbix config file from template
  become: true
  ansible.builtin.template:
    dest: /etc/zabbix/zabbix_agent2.conf
    src: zabbix_agent2.conf.j2
    mode: 0644
- name: Copy userparams file for zfs monitoring
  become: true
  ansible.builtin.copy:
    src: userparams_zol_without_sudo.conf
    dest: /etc/zabbix/zabbix_agent2.d
    mode: u=rw,g=r,o=r
- name: Start service zabbix-agent
  become: true
  ansible.builtin.service:
    name: zabbix-agent2
    state: restarted
    enabled: true
