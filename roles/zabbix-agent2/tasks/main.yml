---
- name: Install zabbix-agent2 "Ubuntu"
  ansible.builtin.import_tasks: zabbix-agent2-ubuntu.yml
  when: ansible_distribution == "Ubuntu"

- name: Install zabbix-agent2 "Debian"
  ansible.builtin.import_tasks: zabbix-agent2-debian.yml
  when: ansible_distribution == "Debian"

- name: Install zabbix-agent Rhel
  ansible.builtin.import_tasks: zabbix-agent2-rhel.yml
  when: ansible_distribution == "CentOS" or ansible_distribution == "RedHat"

- name: Copy script
  ansible.builtin.import_tasks: zabbix-agent2-copy-script.yml

- name: Add zabbix user to specific group
  ansible.builtin.import_tasks: add-to-group.yml
  tags: user

- name: Add permission setting during start zabbix-agent2 service
  ansible.builtin.import_tasks: set-permission.yml
  tags: set-permission
