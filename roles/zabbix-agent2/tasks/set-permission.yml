---
- name: Create directory for override zabbix-agent2 systemd service
  become: true
  ansible.builtin.file:
    path: "{{ zabbix_agent2_override_directory }}"
    state: directory
    mode: u=rwx,g=rx,o=rx

- name: Copy override zabbix-agent2 systemd service
  become: true
  ansible.builtin.copy:
    src: "{{ zabbix_agent2_override_file }}"
    dest: "{{ zabbix_agent2_override_directory }}/{{ zabbix_agent2_override_file }}"
    mode: u=rw,g=r,o=r

- name: Restart zabbix-agent2 service
  ansible.builtin.systemd_service:
    daemon_reload: true
    name: "{{ zabbix_agent2_service }}"
    state: restarted
