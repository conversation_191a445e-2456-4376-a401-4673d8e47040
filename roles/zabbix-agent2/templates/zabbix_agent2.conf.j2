PidFile={{zabbix_agent_pid_file}}
LogFile=/var/log/zabbix/zabbix_agent2.log
LogFileSize=0
Server={% if '-prod-' in inventory_hostname %}{{ zabbix_proxy_primary }}{% elif '-infra-' in inventory_hostname %}{{ zabbix_proxy_primary }}{% else %}{{ zabbix_proxy_secondary }}{% endif %} 
ServerActive={% if '-prod-' in inventory_hostname %}{{ zabbix_proxy_primary }}{% elif '-infra-' in inventory_hostname %}{{ zabbix_proxy_primary }}{% else %}{{ zabbix_proxy_secondary }}{% endif %} 
Hostname={{ inventory_hostname }}
ListenIP={{ ansible_host }}
ListenPort=10050
SourceIP={{ ansible_host }}
Include=/etc/zabbix/zabbix_agent2.d/*.conf
{% if inventory_hostname in groups['pgcluster'] %}
Include=/etc/zabbix/zabbix_agent2.d/plugins.d/postgresql.conf
{% endif %}
{% if inventory_hostname in groups['redis'] %}
Include=/etc/zabbix/zabbix_agent2.d/plugins.d/redis.conf
{% endif %}
AllowKey=system.run[*]
Timeout=20
