---
- name: Create wwww folder
  ansible.builtin.file:
    path: "{{ html_location }}"
    owner: www-data
    group: www-data
    state: directory
    mode: 0755

- name: Create folder
  ansible.builtin.file:
    path: "{{ log_location }}"
    owner: www-data
    group: www-data
    state: directory
    mode: 0755

- name: Install web
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: true
  loop: "{{ web_packages }}"

- name: Copy nginx condfig
  ansible.builtin.template:
    src: site.conf.j2
    dest: /etc/nginx/conf.d/{{ ansible_hostname }}.conf
    mode: 0644

- name: Disable the default site
  ansible.builtin.file:
    path: "{{ nginx_conf_dir }}/sites-enabled/default"
    state: absent

- name: Reload nginx
  ansible.builtin.systemd:
    name: nginx
    state: started
    enabled: true

- name: Reload nginx
  ansible.builtin.systemd:
    name: nginx
    state: reloaded
