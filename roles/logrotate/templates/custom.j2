{{ ansible_managed | comment }}

{% for definition in item.logrotate_definitions %}
{{ definition.logs | join("\n") }}
{

{% for option in definition.options %}
    {{ option }}
{% endfor %}
{% if definition.postrotate|default([]) %}
    postrotate
{% for line in definition.postrotate %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
{% if definition.preremove|default([]) %}
    preremove
{% for line in definition.preremove %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
{% if definition.lastaction|default([]) %}
    lastaction
{% for line in definition.lastaction %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
{% if definition.firstaction|default([]) %}
    firstaction
{% for line in definition.firstaction %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
}
{% endfor %}
