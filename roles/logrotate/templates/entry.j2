{{ ansible_managed | comment }}

{{ item.value.path}}
{

{% for option in logrotate_definitions.options %}
    {{ option }}
{% endfor %}
{% if logrotate_definitions.postrotate|default([]) %}
    postrotate
{% for line in logrotate_definitions.postrotate %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
{% if logrotate_definitions.preremove|default([]) %}
    preremove
{% for line in logrotate_definitions.preremove %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
{% if logrotate_definitions.lastaction|default([]) %}
    lastaction
{% for line in logrotate_definitions.lastaction %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
{% if logrotate_definitions.firstaction|default([]) %}
    firstaction
{% for line in logrotate_definitions.firstaction %}
        {{ line }}
{% endfor %}
    endscript
{% endif %}
}
