{{ ansible_managed | comment }}

# see "man logrotate" for details
# rotate log files weekly
{{ logrotate_frequency }}

# use the syslog group by default, since this is the owning group
# of /var/log/syslog.
su {{ logrotate_user }} {{ logrotate_group }}

# keep 4 weeks worth of backlogs
{{ logrotate_keep }}

# create new (empty) log files after rotating old ones
create

# add dateext
{% if logrotate_dateext %}
dateext
{% else %}
#dateext
{% endif %}

# uncomment this if you want your log files compressed
{% if logrotate_compress %}
compress
{% else %}
#compress
{% endif %}

# packages drop log rotation information into this directory
include /etc/logrotate.d

# system-specific logs may be configured here
