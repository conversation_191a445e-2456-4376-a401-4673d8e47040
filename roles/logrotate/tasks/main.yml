---
- name: Install logrotate
  ansible.builtin.package:
    name: "{{ logrotate_packages }}"
    state: present

- name: Configure logrotate
  ansible.builtin.template:
    src: "{{ logrotate_config_file }}.j2"
    dest: "{{ logrotate_config_directory }}/{{ logrotate_config_file }}"
    mode: "0644"

- name: Generate general logrotate.d files
  ansible.builtin.template:
    src: entry.j2
    dest: "{{ logrotate_confd_directory }}/{{ item.value.name }}"
    mode: "0644"
  loop: "{{ lookup('ansible.builtin.dict', logrotate_general.logs) }}"

- name: Generate custom logrotate.d files
  ansible.builtin.template:
    src: custom.j2
    dest: "{{ logrotate_confd_directory }}/{{ item.name }}"
    mode: "0644"
  with_items:
    - "{{ logrotate_custom }}"

- name: Create default log dir
  ansible.builtin.file:
    path: "{{ logrotate_default_dir }}"
    state: directory
    mode: '0755'

- name: Tuning systemd timer to hourly
  ansible.builtin.lineinfile:
    path: /lib/systemd/system/logrotate.timer
    regexp: "{{ item.regexp }}"
    insertafter: "{{ item.insertafter }}"
    line: "{{ item.line }}"
  loop:
    - regexp: '^OnCalendar='
      line: OnCalendar=hourly
      insertafter: '^\[Timer\]'
    - regexp: '^AccuracySec='
      line: AccuracySec=1m
      insertafter: '^\[Timer\]'
    - regexp: '^RandomizedDelaySec='
      line: RandomizedDelaySec=10m
      insertafter: '^\[Timer\]'
