---
# defaults file for roles/logrotate

# How often to rotate logs, either daily, weekly or monthly.
logrotate_frequency: daily

# Max size of log to rotate regardless of frequency parameter
logrotate_maxsize: 1G

# How many files to keep.
logrotate_keep: 'rotate 7'

# Should rotated logs be compressed?
logrotate_compress: true

# Use date extension on log file names.
logrotate_dateext: false

# User/Group for rotated log files.
logrotate_user: root
logrotate_group: root

# Min file size.
logrotate_minsize: 'minsize 200M'

logrotate_notifempty: true
logrotate_missingok: true

logrotate_create_mode: '0640'

logrotate_packages:
  - logrotate

logrotate_config_directory: /etc

logrotate_config_file: logrotate.conf

logrotate_confd_directory: "{{ logrotate_config_directory }}/logrotate.d"

logrotate_default_dir: /var/log/default

# Users
logrotate_root_user: root
logrotate_apache_user: apache
logrotate_www_user: www-data
logrotate_postgres_user: postgres

# Logs to rotate.

logrotate_general:
  logs:
    name_default:
      name: default
      path: "{{ logrotate_default_dir }}/*/*.log"
    name_bkp_mysql:
      name: backup-mysql
      path: /var/log/backup-mysql.log
logrotate_definitions:
  options:
    - "{{ logrotate_frequency }}"
    - missingok
    - notifempty
    - compress
    - "{{ logrotate_keep }}"
    - "{{ logrotate_minsize }}"
    - "create 0664 {{ logrotate_root_user }} {{ logrotate_root_user }}"
    - copytruncate
    - maxsize {{ logrotate_maxsize }}
# postrotate:
#   - echo hello && date

logrotate_custom:
  - name: www_user_apps
    logrotate_definitions:
      - logs:
          - /var/log/openresty/*.log
          - /var/log/nextcloud.log
        options:
          - "{{ logrotate_frequency }}"
          - "{{ logrotate_keep }}"
          - missingok
          - notifempty
          - compress
          - copytruncate
          - maxsize {{ logrotate_maxsize }}
  - name: sl_apps
    logrotate_definitions:
      - logs:
          - /var/www/logs/app/*.log
          - /var/www/logs/api/*.log
          - /var/www/logs/auth/*.log
          - /var/www/logs/userapi/*.log
          - /var/www/logs/restapi/*.log
          - /var/log/restapi/*.log
          - /var/log/userapi/*.log
          - /var/log/auth/*.log
          - /var/log/api/*.log
          - /var/log/app/*.log
        options:
          - "{{ logrotate_frequency }}"
          - "{{ logrotate_keep }}"
          - missingok
          - notifempty
          - compress
          - copytruncate
          - maxsize {{ logrotate_maxsize }}
  - name: postgres
    logrotate_definitions:
      - logs:
          - /var/log/postgresql/*.log
          - /var/log/postgresql/*.csv
        options:
          - "{{ logrotate_frequency }}"
          - "{{ logrotate_keep }}"
          - missingok
          - notifempty
          - compress
          - copytruncate
          - su root root
          - maxsize {{ logrotate_maxsize }}
  - name: rabbitmq-server
    logrotate_definitions:
      - logs:
          - /var/log/rabbitmq/*.log
        options:
          - "{{ logrotate_frequency }}"
          - "{{ logrotate_keep }}"
          - missingok
          - notifempty
          - compress
          - copytruncate
          - maxsize {{ logrotate_maxsize }}
  - name: mysql-audit
    logrotate_definitions:
      - logs:
          - /data/var/logs/*.log
        options:
          - "{{ logrotate_frequency }}"
          - rotate 90
          - missingok
          - notifempty
          - compress
          - copytruncate
          - minsize 200M
          - create 0664 mysql mysql
          - copytruncate
          - maxsize 11G
          - dateext
          - dateformat .%Y-%m-%d
  - name: mysql
    logrotate_definitions:
      - logs:
          - /var/log/mysql/*.log
        options:
          - "{{ logrotate_frequency }}"
          - "{{ logrotate_keep }}"
          - missingok
          - notifempty
          - compress
          - copytruncate
          - minsize 200M
          - create 0664 mysql mysql
          - copytruncate
          - maxsize 1G
          - dateext
          - dateformat .%Y-%m-%d
