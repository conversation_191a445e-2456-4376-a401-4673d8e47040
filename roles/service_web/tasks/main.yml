---
# tasks file for roles/service_web
- name: Check if directory exists
  ansible.builtin.file:
    name: "{{ ssh_key_destination }}"
    state: directory
    owner: "{{ service_web_main_user }}"
    group: "{{ service_web_main_user }}"
    mode: '0755'

- name: Add SSH key
  ansible.builtin.copy:
    content: "{{ ssh_key_content }}"
    dest: "{{ ssh_key_destination }}/id_rsa"
    mode: '0600'
    owner: "{{ service_web_main_user }}"
    group: "{{ service_web_main_user }}"

- name: Install supervisor
  ansible.builtin.apt:
    name: supervisor
    state: present

- name: Fix Permissions on mount points
  ansible.builtin.file:
    path: "{{ item.mount_point }}"
    owner: "{{ service_web_mount_user }}"
    group: "{{ service_web_mount_user }}"
    mode: '0777'
  with_items: "{{ mount_disk_mount_disks }}"
