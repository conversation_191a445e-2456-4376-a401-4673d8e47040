---
- name: Create container
  community.docker.docker_container:
    image: "{{ container_image }}"
    pull: true
    name: gitlab-exporter
    state: started
    restart: true
    restart_policy: always
    tty: true
    interactive: true
    env:
      GCPE_GITLAB_TOKEN: "{{ gitlab_token }}"
      GCPE_CONFIG: "/etc/gitlab-ci-pipelines-exporter.yml"
      GCPE_INTERNAL_MONITORING_LISTENER_ADDRESS: "tcp://127.0.0.1:8082"
    ports:
      - "{{ ansible_host }}:8888:8080"
    volumes:
      - "{{ exporter_path_file }}:/etc/gitlab-ci-pipelines-exporter.yml"
      - "{{ exporter_ssl_certs_path }}:/etc/ssl/certs/ca-certificates.crt"
