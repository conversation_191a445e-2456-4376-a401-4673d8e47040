---
- name: Change hostname to inventory name
  ansible.builtin.hostname:
    name: "{{ inventory_hostname }}.sl.local"

- name: Install tools
  ansible.builtin.include_tasks: install_tools.yml

- name: Config systcl.conf
  ansible.builtin.import_tasks: sysctl.yml
  tags: sysctl

- name: Configure system services
  ansible.builtin.include_tasks: services-configure.yml
  when: ansible_virtualization_type != "lxc"

- name: Set limits
  ansible.builtin.include_tasks: limits.yml

- name: Configure systemd-networkd
  ansible.builtin.include_tasks: systemd-networkd.yml

- name: Disable unattended-upgrades
  ansible.builtin.include_tasks: unattended-upgrades.yml
  when: ansible_os_family == "Debian"
  tags: unattended-upgrades
