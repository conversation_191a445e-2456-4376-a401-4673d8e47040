---
- name: Install systemtools for Debian and Ubuntu
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: true
  with_items: "{{ system_tools }}"
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"

- name: Install systemtools for CentOS
  ansible.builtin.package:
    name: "{{ item }}"
    state: present
    update_cache: true
  with_items: "{{ system_tools }}"
  when: ansible_distribution == "CentOS"
