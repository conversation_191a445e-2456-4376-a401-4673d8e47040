---
- name: Systctl configure
  ansible.posix.sysctl:
    name: "{{ item.key }}"
    value: "{{ item.value }}"
    state: present
    reload: true
  with_dict: "{{ sysctl_conf }}"

- name: Systctl disable ipv6
  ansible.posix.sysctl:
    name: "{{ item.key }}"
    value: "{{ item.value }}"
    state: present
    reload: true
  with_dict: "{{ sysctl_ipv6_conf }}"
  when: not (ipv6_enabled | default(false))
