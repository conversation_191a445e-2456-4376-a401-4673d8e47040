# my global config
global:
  scrape_interval:     15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
  - static_configs:
    - targets:
      # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
# Here it's Prometheus itself.
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
            - targets: ['localhost:9090', 'localhost:9115']
  - job_name: "service-rabbitmq"
    static_configs:
      - targets:
        - *************:15692
  - job_name: 'repriser-api'
    static_configs:
      - targets:
        - *************:9100
  - job_name: 'proxmox'
    static_configs:
      - targets:
        - ************:9100
        - ************:9100
        - ************:9100
        - ************:9100
        - ***********:9100
        - ************:9100
        - ************:9100
  - job_name: 'db'
    static_configs:
            - targets: ['************:9100','************4:9100']
  - job_name: 'service'
    static_configs:
      - targets:
        - *************:9100
        - *************:9100
        - *************:9100
        - *************:9100
        - ************0:9100
        - ************1:9100
        - ************2:9100
        - ************3:9100
        - ************4:9100
        - *************:9100
  - job_name: 'redis'
    static_configs:
      - targets: 
        - ***********9:9100
        - ************2:9100
        - ************3:9100
        - ***********8:9100
  - job_name: 'spider'
    static_configs:
        - targets: ['192.168.2.202:9100','141.94.175.84:9100','************:9100','19**********:9100']
  - job_name: 'blackbox_http_user-api'
    metrics_path: /probe
    params:
      module: [http_user-api]
    static_configs:
      - targets: ['http://*************/v1/test/ping']
        labels:
          instance: 'User-API-Dev'
      - targets: ['http://*************/v1/test/ping']
        labels:
          instance: 'User-Api-Stage'
      - targets: ['http://*************/v1/test/ping']
        labels:
          instance: 'User-Api-Prod'
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [instance]
        target_label: instance
      - target_label: __address__
        replacement: 127.0.0.1:9115  # The blackbox exporter's real hostname:port.
  - job_name: 'blackbox_http'
    metrics_path: /probe
    params:
      module: [http]
    static_configs:
      - targets: ['http://restapi.sellerlogic.dev']
        labels:
          instance: 'restapi.sellerlogic.dev'
      - targets: ['https://sellerlogic.com']
        labels:
          instance: 'sellerlogic.com'
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [instance]
        target_label: instance
      - target_label: __address__
        replacement: 127.0.0.1:9115  # The blackbox exporter's real hostname:port.
  ## config for the multiple Redis targets that the exporter will scrape
  - job_name: 'redis_exporters'
    static_configs:
      - targets:
        - ***********9:9121
        - ************2:9121
        - ************3:9121
        - ***********8:9121
  - job_name: 'pve'
    static_configs:
      - targets:
        - ************:9221
        - ************:9221
        - ************:9221
        - ************:9221
        - ***********:9221
        - ************:9221
        - ************:9221
    metrics_path: /pve
    params:
      module: [default]

  - job_name: 'spider-prod'
    scrape_interval: 10s
    metrics_path: /metrics/prometheus
    static_configs:
      - targets: ['************:80']
    basic_auth:
      username: 'spider_common'
      password: {{ prometheus_spider_prod_pass }}
  - job_name: 'staging-bas'
    scrape_interval: 10s
    metrics_path: /prometheus/metrics
    scheme: https
    static_configs:
      - targets:
              - bas-api-staging.sellerlogic.com
    basic_auth:
      username: 'prom'
      password: {{ prometheus_staging_bas_pass }}
  - job_name: 'rc-bas'
    scrape_interval: 10s
    metrics_path: /prometheus/metrics
    scheme: https
    static_configs:
      - targets:
              - bas-api-rc.sellerlogic.com
    basic_auth:
      username: 'prom'
      password: {{ prometheus_rc_bas_pass }}
  - job_name: 'prod-bas'
    scrape_interval: 10s
    metrics_path: /prometheus/metrics
    scheme: https
    static_configs:
      - targets:
              - bas-api.sellerlogic.com
    basic_auth:
      username: 'prom'
      password: {{ prometheus_prod_bas_pass }}
  - job_name: 'mysql'
    metrics_path: /metrics
    static_configs:
      - targets:
        - 192.168.2.70:9104
        - 192.168.2.186:9104
        - 192.168.2.76:9104
        - 192.168.2.77:9104
        - ************:9104
  - job_name: 'mysql_node_exporter'
    metrics_path: /metrics
    static_configs:
      - targets:
        - 192.168.2.70:9100
        - 192.168.2.186:9100
        - 192.168.2.76:9100
        - 192.168.2.77:9100
        - ************:9100
  - job_name: 'postgre'
    metrics_path: /metrics
    static_configs:
      - targets:
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ***********3:9187
        - ***********4:9187
        - ***********5:9187
        - ***********6:9187
        - ************7:9187
        - ************8:9187
        - ************9:9187
        - ************0:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - ************:9187
        - *************:9187
        - *************:9187
        - *************:9187
        - *************:9187
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: DE010106
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-bas-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-bas-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-bas-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-bas-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-bas-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-bas-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-bas-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-bas-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-dev-ticket-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-dev-ticket-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-dev-ticket-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-dev-ticket-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ***********3:9187
            replacement: hz-spider-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ***********4:9187
            replacement: hz-spider-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ***********5:9187
            replacement: hz-spider-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ***********6:9187
            replacement: hz-spider-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************7:9187
            replacement: hz-stage-ticket-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************8:9187
            replacement: hz-stage-ticket-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************9:9187
            replacement: hz-stage-ticket-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************0:9187
            replacement: hz-stage-ticket-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-ticket-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-ticket-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-ticket-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-ticket-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-serviceapi-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-serviceapi-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-serviceapi-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-rc-serviceapi-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-serviceapi-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-serviceapi-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-serviceapi-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-stage-serviceapi-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-bas-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-bas-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-bas-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9187
            replacement: hz-prod-bas-db-vip
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9187
            replacement: hz-stage-bas-db2-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9187
            replacement: hz-stage-bas-db2-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9187
            replacement: hz-stage-bas-db2-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9187
            replacement: hz-stage-bas-db2-vip
  - job_name: 'stage BAS'
    static_configs:
      - targets:
          - *************:9100
          - *************:9100
          - *************:9100
          - *************:9100
          - *************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - *************:9100
          - *************:9100
          - *************:9100
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-node-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-clickhouse-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-clickhouse-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-clickhouse-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-clickhouse-04
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-stage-bas-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-stage-bas-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-stage-bas-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-db2-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-db2-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-stage-bas-db2-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-stage-bas-redis-01
 
  - job_name: 'rc BAS'
    static_configs:
      - targets:
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-node-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-clickhouse-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-clickhouse-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-clickhouse-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-clickhouse-04
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-rc-bas-redis-01
          
  - job_name: 'prod BAS'
    static_configs:
      - targets:
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - ************:9100
          - *************:9100
          - *************:9100
          - *************:9100
          - ************:9100
          - *************:9100
          - *************:9100
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-prod-bas-node-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-prod-bas-clickhouse-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-prod-bas-clickhouse-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-prod-bas-clickhouse-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-prod-bas-clickhouse-04
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-prod-bas-db-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-prod-bas-db-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-prod-bas-db-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: hz-prod-bas-redis-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-prod-bas-proc-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9100
            replacement: hz-prod-bas-proc-02  
  - job_name: 'stage BAS ClickHouse'
    static_configs:
      - targets:
          - *************:9363
          - *************:9363
          - *************:9363
          - *************:9363
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9363
            replacement: hz-stage-bas-clickhouse-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9363
            replacement: hz-stage-bas-clickhouse-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9363
            replacement: hz-stage-bas-clickhouse-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: *************:9363
            replacement: hz-stage-bas-clickhouse-04
  - job_name: 'rc BAS ClickHouse'
    static_configs:
      - targets:
          - ************:9363
          - ************:9363
          - ************:9363
          - ************:9363
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-rc-bas-clickhouse-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-rc-bas-clickhouse-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-rc-bas-clickhouse-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-rc-bas-clickhouse-04
  - job_name: 'prod BAS ClickHouse'
    static_configs:
      - targets:
          - ************:9363
          - ************:9363
          - ************:9363
          - ************:9363
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-prod-bas-clickhouse-01
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-prod-bas-clickhouse-02
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-prod-bas-clickhouse-03
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9363
            replacement: hz-prod-bas-clickhouse-04
  - job_name: 'gitlab-ci-pipelines-exporter'
    scrape_interval: 10s
    scrape_timeout: 5s
    static_configs:
      - targets: ['************:8888']
  - job_name: 'smartctl-test-db02'
    scrape_interval: 10s
    scrape_timeout: 5s
    static_configs:
      - targets: ['************:9902']
  - job_name: "rc_repricer_db"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["*************:9104"]
  - job_name: "dev_repricer_db"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["***********:9104"]
  - job_name: "stage_repricer_db"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["************4:9104"]
  - job_name: 'nas-01'
    static_configs:
      - targets:
          - ************:9100
    relabel_configs:
          - source_labels: [ __address__ ]
            target_label: instance
            regex: ************:9100
            replacement: nas-01
  - job_name: "bareos"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["*************:9625"]
  - job_name: "zabbix-proxy-01"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["************5:9100"]
  - job_name: "zabbix-proxy-02"
    scrape_interval: 15s
    metrics_path: /metrics
    static_configs:
      - targets: ["*************:9100"]
  - job_name: "zabbix-server-01"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["*************:9100"]
  - job_name: "DE010106"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["************:9100"]
  - job_name: "proxy-http"
    scrape_interval: 15s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["************8:9100"]
