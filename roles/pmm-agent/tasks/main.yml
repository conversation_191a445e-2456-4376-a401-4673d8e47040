---
# tasks file for roles/pmm-agent
- name: Create a data container
  community.docker.docker_container:
    name: pmm-agent-data
    pull: true
    image: "{{ pmm_agent_image }}"
    volumes:
      - /srv
    command:
      - /bin/true

- name: Run runtime container
  community.docker.docker_container:
    image: "{{ pmm_agent_image }}"
    name: pmm-agent
    hostname: "{{ inventory_hostname }}"
    state: started
    pull: true
    restart: true
    restart_policy: always
    tty: true
    interactive: true
    volumes_from:
      - pmm-agent-data
    cpu_period: "{{ pmm_cpu_period }}"
    cpu_quota: "{{ pmm_cpu_quota }}"
    memory: "{{ pmm_memory }}"
    env:
      PMM_AGENT_SERVER_ADDRESS: "{{ pmm_server_ip }}:{{ pmm_server_port }}"
      PMM_AGENT_SERVER_USERNAME: "{{ pmm_server_user }}"
      PMM_AGENT_SERVER_PASSWORD: "{{ pmm_server_user_password }}"
      PMM_AGENT_SERVER_INSECURE_TLS: "1"
      PMM_AGENT_CONFIG_FILE: "config/pmm-agent.yaml"
    comparisons:
      '*': strict
    mounts: "{{ pmm_agent_mounts }}"

- name: Pause for 10 seconds to start container
  ansible.builtin.pause:
    seconds: 10

- name: Register client node with PMM Server
  community.docker.docker_container_exec:
    container: pmm-agent
    command: >
            pmm-admin config
            --server-insecure-tls
            --server-url=https://{{ pmm_server_user }}:{{ pmm_server_user_password }}@{{ pmm_server_ip }}:{{ pmm_server_port }}
            --force

- name: Pause for 10 seconds to start container
  ansible.builtin.pause:
    seconds: 10
