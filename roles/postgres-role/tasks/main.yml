---
# tasks file for postgres-role
- name: Install postgresql
  when: lookup('community.general.collection_version', 'community.postgresql') is version('1.6.1', '>=')
  block:

    - name: Gather info about databases
      community.postgresql.postgresql_info:
        db: postgres
      register: postgres_db_info

    - name: Create and fill db_namespaces_list object
      ansible.builtin.set_fact:
        db_namespaces_list: >
          {{
            db_namespaces_list |
            default([]) +
              [
              {
                "db": item.key,
                "namespaces": item.value.namespaces.keys() | list | difference(['information_schema','pg_catalog','pg_toast'])
              }
              ]
          }}
      loop: "{{ postgres_db_info.databases | dict2items }}"
      when: item.key in postgre_db

    - name: Create RO roles
      become: true
      become_user: postgres
      community.postgresql.postgresql_user:
        db: "{{ item.db }}"
        name: "{{ item.db }}_RO"
        priv: "CONNECT/TEMPORARY"
        role_attr_flags: NOLOGIN,NOINHERIT
        state: present
      loop: "{{ db_namespaces_list }}"

    - name: Create RW roles
      become: true
      become_user: postgres
      community.postgresql.postgresql_user:
        db: "{{ item.db }}"
        name: "{{ item.db }}_RW"
        priv: "CONNECT/TEMPORARY/CREATE"
        role_attr_flags: NOLOGIN,NOINHERIT,REPLICATION
        state: present
      loop: "{{ db_namespaces_list }}"

    - name: Grant permission on schemas
      become: true
      become_user: postgres
      community.postgresql.postgresql_privs:
        database: "{{ item[0][0].db }}"
        grant_option: false
        objs: "{{ item[0][1] }}"
        privs: "{{ item[1].priv }}"
        roles: "{{ item[0][0].db }}{{ item[1].role }}"
        state: present
        type: schema
      loop: "{{ db_namespaces_list | subelements('namespaces') | product([{'priv': 'USAGE', 'role': '_RO'}, \
        {'priv': 'USAGE,CREATE', 'role': '_RW'}]) }}"
      loop_control:
        label: "Grant:{{ item[1].priv }} DB:{{ item[0][0].db }} Schema:{{ item[0][1] }} Role:{{ item[0][0].db }}{{ item[1].role }}"

    - name: Grant permission on objects in schemas
      become: true
      become_user: postgres
      community.postgresql.postgresql_privs:
        database: "{{ item[0][0].db }}"
        grant_option: false
        objs: ALL_IN_SCHEMA
        privs: "{{ item[1].priv }}"
        roles: "{{ item[0][0].db }}{{ item[1].role }}"
        state: present
        type: "{{ item[1].type }}"
      loop: "{{ db_namespaces_list | subelements('namespaces') | \
        product([{'priv': 'EXECUTE', 'type': 'function', 'role': '_RW'}, \
        {'priv': 'EXECUTE', 'type': 'procedure', 'role': '_RW'}, \
        {'priv': 'SELECT', 'type': 'sequence', 'role': '_RO'}, \
        {'priv': 'SELECT,UPDATE,USAGE', 'type': 'sequence', 'role': '_RW'}, \
        {'priv': 'SELECT', 'type': 'table', 'role': '_RO'}, \
        {'priv': 'INSERT,SELECT,UPDATE,DELETE,TRUNCATE,REFERENCES,TRIGGER', 'type': 'table', 'role': '_RW'}]) }}"
      loop_control:
        label: "Grant:{{ item[1].priv }} DB:{{ item[0][0].db }} \
          Schema:{{ item[0][1] }} Type:{{ item[1].type }} \
          Role:{{ item[0][0].db }}{{ item[1].role }} Priv:{{ item[1].priv }}"

    - name: Create users
      become: true
      become_user: postgres
      community.postgresql.postgresql_user:
        comment: "{{ item.comment }}"
        db: postgres
        expires: infinity
        name: "{{ item.name }}"
        password: "{{ item.password }}"
        role_attr_flags: LOGIN,INHERIT
        state: present
      loop: "{{ postgre_user }}"

    - name: Crete memberships
      become: true
      become_user: postgres
      community.postgresql.postgresql_membership:
        db: "{{ item[1] | community.general.json_query('keys(@)[0]') }}"
        groups: "{{ item[1] | community.general.json_query('keys(@)[0]') }}_{{ item[1] | community.general.json_query('[*][0]') }}"
        state: present
        target_roles: "{{ item[0].name }}"
      loop: "{{ postgre_user | subelements('db_access') }}"
