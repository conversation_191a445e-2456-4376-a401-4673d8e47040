postgres-role
=========

Create postgres users and roles.

Requirements
------------

Collection community.postgres >= 1.6.1
  community.postgresql 1.6.0 have bug with incorrect listing shemas and 
  may critical affect to product instance of postgresql with use thise role.

Role Variables
--------------

Example of incoming variables:

  postgre_db:
    - postgres
    - mydb
    - test 
  postgre_user:
    - name: user1
      password: md5d011966da94d776cf59bf6dbde240e5d
      comment: this is user1
      db_access:
        - postgres: RO
        - mydb: RW
    - name: user2
      password: md5d011966da94d776cf59bf6dbde240e5d
      comment: this is user2
      db_access:
        - mydb: RW