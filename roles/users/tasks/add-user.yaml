---
- name: Add group sudo
  ansible.builtin.group:
    name: sudo
    state: present
  become: true
- name: Add group admin
  ansible.builtin.group:
    name: admin
    state: present
  become: true

- name: Add group docker
  ansible.builtin.group:
    name: docker
    state: present
  become: true

- name: Add group app_admins
  ansible.builtin.group:
    name: app_admins
    state: present
  become: true

- name: "Allow admin users to sudo without a password"
  ansible.builtin.template:
    dest: "/etc/sudoers.d/90-sudo-users"
    src: 90-sudo-users.j2
    mode: 0644

- name: Add admin users
  ansible.builtin.user:
    name: "{{ item.name }}"
    shell: "{{ item.shell | default(users_admin_users_default_shell) }}"
    groups: admin,adm,docker
  with_items: "{{ admin_users }}"
  become: true

- name: Add admin authorized key
  ansible.posix.authorized_key:
    user: "{{ item.name }}"
    key: "{{ item.key }}"
    exclusive: true
    state: present
  when: item.state | default(users_admin_users_default_state) != 'absent'
  with_items: "{{ admin_users }}"
  become: true

- name: Added service users
  ansible.builtin.user:
    name: "{{ item.name }}"
    shell: "{{ item.shell | default(users_service_users_default_shell) }}"
    groups: users,docker
  with_items: "{{ service_users }}"
  when: service_users is defined and
        ( item.state | default(users_service_users_default_state) != 'absent' )

- name: Add service authorized key
  ansible.posix.authorized_key:
    user: "{{ item.name }}"
    key: "{{ item.key }}"
    exclusive: true
    state: present
  with_items: "{{ service_users }}"
  become: true
  when: service_users is defined and
        ( item.state | default(users_service_users_default_state) != 'absent')

- name: Added application admins users
  ansible.builtin.user:
    name: "{{ item.name }}"
    shell: "{{ item.shell | default(users_app_admins_default_shell) }}"
    groups: app_admins,docker,admin
  with_items: "{{ app_admins }}"
  become: true
  when: app_admins is defined and
        ( item.state | default(users_app_admins_default_state) != 'absent' )

- name: Add application authorized key
  ansible.posix.authorized_key:
    user: "{{ item.name }}"
    key: "{{ item.key }}"
    exclusive: true
    state: present
  with_items: "{{ app_admins }}"
  become: true
  when: app_admins is defined and
        ( item.state | default(users_app_admins_default_state) != 'absent' )
