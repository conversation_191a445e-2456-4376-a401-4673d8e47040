---
- name: Only run "update_cache=yes" if the last one is more than 3600 seconds ago
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 43200
    name:
      - zfsutils-linux
      - nfs-kernel-server
  tags: install

- name: Set ZFS parameters
  community.general.zfs:
    name: "{{ item['name'] }}"
    state: present  # !!! attention: 'absent' will destroy file system and data, not parameter
    extra_zfs_properties:
      sharenfs: "{{ item['sharenfs'] }}"
  loop: "{{ zfs_nfs_shares }}"
  tags: params
