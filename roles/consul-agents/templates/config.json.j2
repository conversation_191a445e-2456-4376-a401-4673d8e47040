{"server": false, "datacenter": "hz", "data_dir": "{{ consul_agent_data_dir }}", "encrypt": "{{ consul_cluster_gossip_encryption_key }}", "log_level": "{{ consul_agent_log_level }}", "bind_addr": "{{ consul_agent_bind_addr | default(ansible_host) }}", "enable_syslog": true, "leave_on_terminate": true, "rejoin_after_leave": true, "enable_script_checks": true, "retry_join": ["{{ groups['consul_servers'] | map('extract', hostvars, ['ansible_host']) | join('", "') }}"], "ports": {"http": 7500, "dns": 7600, "serf_lan": 7301, "server": 7300}}