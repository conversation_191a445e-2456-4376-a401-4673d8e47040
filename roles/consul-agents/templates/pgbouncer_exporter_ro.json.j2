{"service": {"name": "pgbouncer_exporter_ro", "port": 9128, "tags": ["_hostname={{ inventory_hostname }}", "_environment={% if 'prod-' in inventory_hostname %}prod{% elif 'infra-' in inventory_hostname %}infra{% elif 'dev-' in inventory_hostname %}dev{% elif 'stage-' in inventory_hostname %}stage{% elif 'rc-' in inventory_hostname %}rc{% elif 'spider-' in inventory_hostname %}spider{% elif 'prox-' in inventory_hostname %}prod{% else %}other{% endif %}", "exporter_service=pgbouncer_exporter_ro"], "meta": {"metrics_path": "/metrics", "metrics_scheme": "http"}}}