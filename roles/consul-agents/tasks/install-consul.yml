---
# tasks file for roles/consul-agents
- name: Consul | Ensure Consul group
  ansible.builtin.group:
    name: "{{ consul_agent_group }}"

- name: Consul | Ensure Consul user
  ansible.builtin.user:
    name: "{{ consul_agent_user }}"
    group: "{{ consul_agent_group }}"
    createhome: false

- name: Download consul binary
  ansible.builtin.get_url:
    url: "{{ consul_agent_download_url }}"
    dest: "{{ consul_agent_area }}"
    mode: 0755
  environment:
    http_proxy: "{{ consul_agent_http_proxy | default(None) }}"
    https_proxy: "{{ consul_agent_http_proxy | default(None) }}"

- name: Unzip temporarily bin
  ansible.builtin.unarchive:
    src: "{{ consul_agent_area }}/{{ consul_agent_archive_file }}"
    dest: "{{ consul_agent_area }}"
    include: "{{ consul_agent_bin }}"
    owner: "root"
    group: "root"
    copy: false

- name: Remove archive
  ansible.builtin.file:
    path: "{{ consul_agent_area }}/{{ consul_agent_archive_file }}"
    state: absent

- name: Copy consul bin to target location
  ansible.builtin.copy:
    src: "{{ consul_agent_area }}/{{ consul_agent_bin }}"
    dest: "{{ consul_agent_target_dir }}/{{ consul_agent_target_bin }}"
    mode: u=rwx,g=rx,o=rx
    owner: root
    group: root
    remote_src: true

- name: Remove temporarily bin
  ansible.builtin.file:
    path: "{{ consul_agent_area }}/{{ consul_agent_bin }}"
    state: absent

- name: Create a directory if it does not exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    owner: "{{ consul_agent_user }}"
    group: "{{ consul_agent_group }}"
    mode: 0755
  with_items:
    - "{{ consul_agent_require_dirs }}"

- name: Update consul permissions
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    owner: "{{ consul_agent_user }}"
    group: "{{ consul_agent_group }}"
    mode: 0755
  with_items:
    - "{{ consul_agent_require_dirs }}"

- name: Copy config for consul
  ansible.builtin.template:
    src: config.json.j2
    dest: "{{ consul_agent_common_dir }}/config.json"
    mode: 0644
    owner: "{{ consul_agent_user }}"
    group: "{{ consul_agent_group }}"
  notify: Restart consul-agent

- name: Create service for consul
  ansible.builtin.template:
    src: consul-agent.service.j2
    dest: "/etc/systemd/system/consul-agent.service"
    mode: 0644

- name: Enable and start consul
  ansible.builtin.systemd_service:
    name: consul-agent
    state: restarted
    enabled: true
    daemon_reload: true
