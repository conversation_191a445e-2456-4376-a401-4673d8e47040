---
- name: Populate service facts
  ansible.builtin.service_facts:

- name: Populate running docker pgbouncer-exporter container info
  community.docker.docker_container_info:
    name: pgbouncer-exporter
  register: pgbouncer_exporter_info

- name: Populate running docker pgbouncer-exporter_ro container info
  community.docker.docker_container_info:
    name: pgbouncer-exporter_ro
  register: pgbouncer_exporter_ro_info

- name: Populate running docker consul-exporter container info
  community.docker.docker_container_info:
    name: consul-exporter
  register: consul_exporter_info

- name: Populate running docker kea-exporter container info
  community.docker.docker_container_info:
    name: kea-exporter
  register: kea_exporter_info

- name: Add services exporters
  ansible.builtin.template:
    src: "{{ item.name }}.json.j2"
    dest: "{{ consul_agent_common_dir }}/{{ item.name }}.json"
    mode: 0644
    owner: "{{ consul_agent_user }}"
    group: "{{ consul_agent_group }}"
  notify: Restart consul-agent
  loop: "{{ consul_agent_exporters }}"
  when: "{{ item['when'] | default(true) }}"  # noqa: no-jinja-when
