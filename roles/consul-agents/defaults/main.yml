---
# defaults file for roles/consul-agents
consul_agent_log_level: INFO
consul_agent_data_dir: /opt/consul-agent/data
consul_agent_common_dir: /opt/consul-agent/consul.d
consul_agent_user: consul
consul_agent_group: consul
consul_agent_version: "1.19.2"
consul_agent_release: "consul_{{ consul_agent_version }}_linux_amd64"
consul_agent_archive_file: "{{ consul_agent_release }}.zip"
consul_agent_area: "/tmp"
consul_agent_target_bin: consul-agent
consul_agent_target_dir: /usr/local/bin
consul_agent_bin: consul
consul_agent_name_service: consul
consul_agent_download_url: "https://releases.hashicorp.com/{{ consul_agent_name_service }}/{{ consul_agent_version }}/{{ consul_agent_archive_file }}"
consul_agent_require_dirs:
    - "{{ consul_agent_common_dir }}"
    - "{{ consul_agent_data_dir }}"

consul_agent_exporters:
    - name: prometheus_exporter
    - name: postgres_exporter
      when: ansible_facts.services['patroni.service'] is defined
    - name: pgcluster_exporter
      when: ansible_facts.services['patroni.service'] is defined
    - name: mysql_exporter
      when: ansible_facts.services['mysql.service'] is defined
    - name: redis_exporter
      when: ansible_facts.services['redis-server'] is defined
    - name: gitlab_exporter
      when: ansible_facts.services['gitlab-runner.service'] is defined
    - name: rabbitmq_custom_exporter
      when: ansible_facts.services['rabbitmq-server'] is defined
    - name: rabbitmq_exporter
      when: ansible_facts.services['rabbitmq-server'] is defined
    - name: haproxy_exporter
      when: ansible_facts.services['haproxy.service'] is defined
    - name: pgbouncer_exporter
      when: pgbouncer_exporter_info.exists
    - name: consul_exporter
      when: consul_exporter_info.exists
    - name: pgbouncer_exporter_ro
      when: pgbouncer_exporter_ro_info.exists
    - name: kea_exporter
      when: kea_exporter_info.exists
