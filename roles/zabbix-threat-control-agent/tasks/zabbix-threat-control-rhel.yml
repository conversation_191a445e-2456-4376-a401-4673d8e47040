---
- name: Install Dependencies
  ansible.builtin.package:
    name: ['python3-pip', 'python3-setuptools', 'git']
    state: latest
    update_cache: true
- name: Install python requirements
  ansible.builtin.pip:
    name:
      - pyzabbix
      - jpath
      - vulners
- name: Clone a github repository
  ansible.builtin.git:
    repo: https://github.com/vulnersCom/zabbix-threat-control.git
    dest: /tmp/zabbix-threat-control/
    clone: true
    update: true
    version: master
- name: Mkdir
  ansible.builtin.file:
    path: "/opt/monitoring/"
    owner: zabbix
    group: zabbix
    mode: '0755'
    recurse: true
- name: Copy directory
  ansible.builtin.command: cp -R /tmp/zabbix-threat-control/os-report /opt/monitoring/
  changed_when: false
- name: Fix permissions
  ansible.builtin.file:
    path: "/opt/monitoring/os-report"
    owner: zabbix
    group: zabbix
    mode: '0755'
    recurse: true
- name: Remove repo
  ansible.builtin.file:
    path: /tmp/zabbix-threat-control
    state: absent
