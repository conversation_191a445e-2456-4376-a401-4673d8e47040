---
- name: Install Dependencies
  ansible.builtin.apt:
    name: ['python3-pip', 'python3-setuptools', 'git']
    state: latest
    update_cache: true

- name: Install python requirements on Ubuntu 24.04
  ansible.builtin.pip:
    name:
      - pyzabbix
      - jpath
      - vulners
    extra_args: "--break-system-packages"
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version >= '24.04'

- name: Install python requirements
  ansible.builtin.pip:
    name:
      - pyzabbix
      - jpath
      - vulners
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version < '24.04' or ansible_distribution == 'Debian'

- name: Clone a github repository
  ansible.builtin.git:
    repo: https://github.com/vulnersCom/zabbix-threat-control.git
    dest: /tmp/zabbix-threat-control/
    clone: true
    update: true
    version: master
- name: Mkdir
  ansible.builtin.file:
    path: "/opt/monitoring/"
    owner: zabbix
    group: zabbix
    mode: '0755'
    recurse: true
# - name: Copy directory
#   command: cp -R /tmp/zabbix-threat-control/os-report /opt/monitoring/
- name: Synchronising
  ansible.posix.synchronize:
    src: "/tmp/zabbix-threat-control/os-report"
    dest: "/opt/monitoring/"
  delegate_to: "{{ inventory_hostname }}"
- name: Fix permissions
  ansible.builtin.file:
    path: "/opt/monitoring/os-report"
    owner: zabbix
    group: zabbix
    mode: '0755'
    recurse: true
- name: Remove repo
  ansible.builtin.file:
    path: /tmp/zabbix-threat-control
    state: absent
