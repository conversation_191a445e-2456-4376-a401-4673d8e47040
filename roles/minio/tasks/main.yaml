---
- name: Create minio container
  community.docker.docker_container:
    name: minio
    image: "{{ minio_image }}"
    ports: "{{ minio_ports }}"
    volumes:
      - "{{ minio_docker_host_path_data }}:{{ minio_docker_path_data }}"
      - "{{ minio_docker_host_path_certs }}:{{ minio_docker_path_certs }}"
    env:
      MINIO_ROOT_USER: "{{ minio_root_user }}"
      MINIO_ROOT_PASSWORD: "{{ minio_root_password }}"
      MINIO_SCHEME: "{{ minio_scheme }}"
      BITNAMI_DEBUG: "{{ minio_bitnami_debug }}"
      MINIO_SERVER_URL: "{{ minio_server_url }}"
      MINIO_SKIP_CLIENT: "{{ minio_skip_client }}"
    comparisons:
      '*': strict
  tags: install
