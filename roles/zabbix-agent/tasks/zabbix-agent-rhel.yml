---
- name: Download zabbix repo package
  ansible.builtin.get_url:
    url:
      "https://repo.zabbix.com/zabbix/{{ zabbix_version }}/debian/pool/main/\
      z/zabbix-release/zabbix-release_{{ zabbix_version }}-1%2Bdebian10_all.deb"
    dest: /tmp/zabbix.deb
    mode: 0644
- name: Install zabbix repo
  become: true
  ansible.builtin.package:
    name: /tmp/zabbix.deb
    state: present
- name: Install zabbix agent
  become: true
  ansible.builtin.package:
    name: zabbix-agent
    state: present
- name: Stop service zabbix-agent
  become: true
  ansible.builtin.service:
    name: zabbix-agent
    state: stopped
- name: Remove zabbix config file
  become: true
  ansible.builtin.file:
    path: /etc/zabbix/zabbix_agentd.conf
    state: absent
- name: Create new zabbix config file from template
  become: true
  ansible.builtin.template:
    src: "zabbix_agentd.conf.j2"
    dest: "/etc/zabbix/zabbix_agentd.conf"
    mode: 0644
