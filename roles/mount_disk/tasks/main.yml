---
- name: Check if disk exists
  ansible.builtin.stat:
    path: "{{ item.device }}"
  register: disk
  with_items: "{{ mount_disk_mount_disks }}"

- name: Create a filesystem on disk
  community.general.filesystem:
    fstype: "{{ item.item.filesystem_type }}"
    dev: "{{ item.item.device }}"
  when: item.stat.exists
  with_items: "{{ disk.results }}"

- name: Mount device
  ansible.posix.mount:
    path: "{{ item.item.mount_point }}"
    src: "{{ item.item.device }}"
    fstype: "{{ item.item.filesystem_type }}"
    opts: defaults
    state: mounted
  when: item.stat.exists
  with_items: "{{ disk.results }}"
