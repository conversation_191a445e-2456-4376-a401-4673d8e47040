---
- name: Check condition for install
  ansible.builtin.assert:
    fail_msg: This server is not for install Vector
    success_msg: This server is for install Vector
    that:
      - vector_install

- name: Add user "vector"
  ansible.builtin.user:
    name: "{{ vector_app_user }}"
    groups:
      - root
      - docker
    shell: /sbin/nologin
    create_home: false
    append: true
    state: present
  become: true

- name: Install Vector (Debian)
  ansible.builtin.apt:
    deb: "https://packages.timber.io/vector/{{ vector_version }}/vector_{{ vector_version }}-1_{{ arch }}.deb"
    allow_downgrades: true
  vars:
    arch: "{{ vector_debian_arch[ansible_machine] }}"
  when: ansible_os_family == 'Debian'

- name: Install Vector (RedHat)
  ansible.builtin.package:
    name: "https://packages.timber.io/vector/{{ vector_version }}/vector-{{ vector_version }}.{{ arch }}.rpm"
    state: present
    disable_gpg_check: true
  vars:
    arch: "{{ vector_redhat_arch[ansible_machine] }}"
  when: ansible_os_family == 'RedHat'

- name: Check if this service web node
  ansible.builtin.stat:
    path: "{{ vector_service_web_app_log }}"
  register: service_app_log

- name: Check if this a gitlab node
  ansible.builtin.stat:
    path: "{{ vector_gitlab_log_dir }}"
  register: service_gitlab_log

- name: Check if this a dhcp node
  ansible.builtin.stat:
    path: "{{ vector_dhcp_log_dir }}"
  register: service_dhcp_log

- name: Copy config
  ansible.builtin.template:
    src: "{{ vector_template }}"
    dest: "{{ vector_config_file }}"
    mode: 0644
  notify: Restart vector

- name: Start Vector
  ansible.builtin.systemd:
    state: started
    enabled: true
    name: vector
    daemon_reload: true

- name: "Make sure the entry in"
  ansible.builtin.lineinfile:
    regexp: "AmbientCapabilities=CAP_NET_BIND_SERVICE"
    path: "{{ vector_systemd_file }}"
    line: "AmbientCapabilities=CAP_DAC_READ_SEARCH"
    state: present
  notify: Restart vector

- name: "Make sure the entry in"
  ansible.builtin.lineinfile:
    path: "{{ vector_systemd_file }}"
    insertafter: "^AmbientCapabilities=CAP_DAC_READ_SEARCH+"
    line: "CapabilityBoundingSet=CAP_DAC_READ_SEARCH"
    firstmatch: true
    state: present
  notify: Restart vector

- name: Set MemoryLimit to {{ vector_memory_limit }}
  ansible.builtin.lineinfile:
    path: "{{ vector_systemd_file }}"
    insertafter: "^CapabilityBoundingSet=CAP_DAC_READ_SEARCH+"
    line: "MemoryLimit={{ vector_memory_limit }}"
    firstmatch: true
    state: present
  notify: Restart vector

- name: Set CPUQuota to {{ vector_cpu_quota }}
  ansible.builtin.lineinfile:
    path: "{{ vector_systemd_file }}"
    insertafter: "^MemoryLimit={{ vector_memory_limit }}+"
    line: "CPUQuota={{ vector_cpu_quota }}"
    firstmatch: true
    state: present
  notify: Restart vector

- name: Use the right config_file
  ansible.builtin.lineinfile:
    path: /etc/default/vector
    line: "VECTOR_CONFIG={{ vector_config_file }}"
    state: present

- name: Set up vector stdout to logs
  community.general.ini_file:
    dest: /lib/systemd/system/vector.service
    owner: root
    group: root
    mode: 0644
    section: Service
    option: StandardOutput
    value: "file:{{ vector_log_file }}"
  when: ansible_distribution == "Debian" or ansible_distribution == "Ubuntu"

- name: Change LOG LEVEL
  ansible.builtin.lineinfile:
    path: /lib/systemd/system/vector.service
    regexp: "^(.*)ExecStart=/usr/bin/vector(.*)$"
    line: "ExecStart=/usr/bin/vector -q"
    backrefs: true
  notify: Restart vector

- name: Change Restart policy
  ansible.builtin.lineinfile:
    path: /lib/systemd/system/vector.service
    regexp: "^(.*)Restart=no(.*)$"
    line: "Restart=always"
    backrefs: true
  notify: Restart vector

- name: Change Restart sec
  ansible.builtin.lineinfile:
    path: "{{ vector_systemd_file }}"
    insertafter: "^Restart=always+"
    line: "RestartSec=5"
    firstmatch: true
    state: present
  notify: Restart vector

# - name: Find group on server
#   ansible.builtin.shell: "getent group | cut -d: -f1"
#   register: unix_groups

# - name: Add vector user to groups
#   ansible.builtin.user:
#     name: "{{ item.name }}"
#     groups: "{{ item.groups }}"
#     append: true
#   when: "item.groups in unix_groups.stdout_lines"
#   with_items: "{{ vector_users_group }}"
#   notify: restart vector

# - name: Restart Vector
#   ansible.builtin.systemd:
#     state: restarted
#     enabled: true
#     name: vector
#     daemon_reload: true
