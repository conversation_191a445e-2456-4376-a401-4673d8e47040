api:
  enabled: false
  address: 127.0.0.1:8686
  playground: true
schema:
  enabled: false
  validation: false
data_dir: {{ vector_data_dir }}
healthchecks:
  enabled: true
  require_healthy: false
sources:
  varlogs:
    type: file
    include:
    - /var/log/**/*.log
{% if '-ldap-' in inventory_hostname %}
    - /app/ipadata/var/log/**/*.log
    - /app/ipadata/var/log/dirsrv/slapd-SL-LOCAL/*
{% endif %}
{% if service_app_log.stat.exists %}
    - {{ vector_service_web_app_log }}
{% endif %}
{% if service_dhcp_log.stat.exists %}
    - {{ vector_dhcp_log_dir }}/*.log
{% endif %}
{% if clamav_install | default(false) %}
    - {{ clamav_log_path }}
{% endif %}
    exclude:
    - /var/log/daemon.log
    - /var/log/nginx/*.*
    - /var/log/sssd/*.*
    file_key: file
    read_from: end
  docker:
    type: docker_logs
    host_key: host
    docker_host: /var/run/docker.sock
    partial_event_marker_field: _partial
    auto_partial_merge: true
    retry_backoff_secs: 2
  nginx:
    type: file
    include:
    - /var/log/nginx/json_access.log
    exclude: []
    file_key: file
    read_from: end
{% if service_app_log.stat.exists %}
  service_app_log:
    type: file
    include:
    - {{ vector_service_web_app_log }}
    exclude:
    - /var/log/daemon.log
    - /var/log/nginx/*.*
    file_key: file
    read_from: end
{% endif %}
{% if clamav_install | default(false) %}
  clamav_log:
    type: file
    include:
    - {{ clamav_log_path }}
    file_key: file
    read_from: end
{% endif %}
{% if service_gitlab_log.stat.exists %}
  service_gitlab_log:
    type: file
    include:
    - {{ vector_gitlab_log_dir }}/*/current
    - {{ vector_gitlab_log_dir }}/nginx/*_error.log
    - {{ vector_gitlab_log_dir }}/nginx/*_access.log
    - {{ vector_gitlab_log_dir }}/gitlab-rails/*.log
    - {{ vector_gitlab_log_dir }}/puma/*.log
    file_key: file
    read_from: end
{% endif %}
{% if service_dhcp_log.stat.exists %}
  service_dhcp_log:
    type: file
    include:
    - {{ vector_dhcp_log_dir }}/*.log
    file_key: file
    read_from: end
{% endif %}
{% if inventory_hostname in groups['infra_ext_lb'] %}
  nginx_external:
    type: file
    include:
    - /var/log/nginx/json_access_external.log
    exclude: []
    file_key: file
    read_from: end
{% endif %}
{% if inventory_hostname in groups['infra_int_lb'] %}
  nginx_internal:
    type: file
    include:
    - /var/log/nginx/json_access_internal.log
    exclude: []
    file_key: file
    read_from: end
{% endif %}
{% if '-repricer-api-' in inventory_hostname %}
  restapi:
    type: file
    include:
      - /var/log/restapi/*.json
    read_from: end
{% endif %}
{% if '-repricer-api-' in inventory_hostname %}
transforms:
  parse_json:
    type: remap
    inputs: ["restapi"]
    source: |
      message, err = parse_json(.message)
      if err == null {
        .user = message.user
        .state = message.state
        .object_group = message.object_group
        .name = message.name
        .created_at = message.created_at
        .entityId = message.entityId
        .location = message.location
        .type = message.type
      } else {
        log(err)
      }

  add_timestamp:
    type: remap
    inputs: ["parse_json"]
    source: |
      . = merge(., { "@timestamp": .created_at })

  filter_unwanted_fields:
    type: remap
    inputs: ["add_timestamp"]
    source: |
      del(.file)
      del(.source_type)
      del(.debug_message)
{% endif %}
sinks:
{% if inventory_hostname in groups['infra_int_lb'] or inventory_hostname in groups['infra_ext_lb'] %}
  out:
    inputs:
    - nginx_replace_value
    healthcheck:
      enabled: true
    type: console
    target: stdout
    encoding:
      codec: json
{% endif %}
  loki:
    inputs:
    - varlogs
    - docker
    - nginx
{% if service_app_log.stat.exists %}
    - service_app_log
{% endif %}
{% if clamav_install | default(false) %}
    - clamav_log
{% endif %}
{% if service_gitlab_log.stat.exists %}
    - service_gitlab_log
{% endif %}
{% if service_dhcp_log.stat.exists %}
    - service_dhcp_log
{% endif %}
{% if inventory_hostname in groups['infra_int_lb'] %}
    - nginx_internal
{% endif %}
{% if inventory_hostname in groups['infra_ext_lb'] %}
    - nginx_external
{% endif %}
{% if inventory_hostname in groups['infra_int_lb'] or inventory_hostname in groups['infra_ext_lb'] %}
    - nginx_replace_value
{% endif %}
    type: loki
    endpoint: {{ vector_endpoint_host }}
    encoding:
      codec: text
      timestamp_format: unix
    labels:
{% if inventory_hostname in groups['infra_int_lb'] %}
      forwarder: {{ inventory_hostname }}-internal{% elif inventory_hostname in groups['infra_ext_lb'] %}
      forwarder: {{ inventory_hostname }}-external{% else %}
      forwarder: {{ inventory_hostname }}{% endif %}    
      key: value
{% if 'service-web' in inventory_hostname %}
      service: service-web{% elif 'repricer-api' in inventory_hostname %}
      service: repricer-api{% else %}
      service: other{% endif %}

      env: {% if 'prod-' in inventory_hostname %}prod
      {% elif 'infra-' in inventory_hostname %}infra
      {% elif 'dev-' in inventory_hostname %}dev
      {% elif 'stage-' in inventory_hostname %}stage
      {% elif 'rc-' in inventory_hostname %}rc
      {% elif 'prox-' in inventory_hostname %}prod
      {% else %}other
{% endif %}

      file: "{{ "{{" }} file {{ "}}" }}"
      job: "{{ "{{" }} source_type {{ "}}" }}"

{% if '-repricer-api-' in inventory_hostname %}
  elasticsearch:
    type: elasticsearch
    inputs: ["filter_unwanted_fields"]
    bulk:
      action: index
      index: "{{ vector_elastic_index }}"
    endpoints:
{% for endpoint in vector_elastic_endpoints %}
      - {{ endpoint }}
{% endfor %}
    auth:
      strategy: basic
      user: "{{ vector_elastic_user }}"
      password: "{{ vector_elastic_password }}"
    tls:
      verify_certificate: false
{% endif %}
{% if inventory_hostname in groups['infra_int_lb'] or inventory_hostname in groups['infra_ext_lb'] %}
transforms:
  nginx_replace_value:
    inputs:
{% if inventory_hostname in groups['infra_int_lb'] %}
    - nginx_internal
{% endif %}
{% if inventory_hostname in groups['infra_ext_lb'] %}
    - nginx_external
{% endif %}
    type: remap
    source: |
         if .gzip_ratio == "-" || .gzip_ratio == "" {
            .gzip_ratio = "0.0"
         } else {
            .gzip_ratio = to_float!(.gzip_ratio)
         }

         if .upstream_cache_status == "-" || .upstream_cache_status == "" {
            .upstream_cache_status = "Disable"
         }

         if .http_cf_connecting_ip == "-" || .http_cf_connecting_ip == "" {
            .http_cf_connecting_ip = "0.0.0.0"
         }

         if .http_x_forwarded_for == "-" || .http_x_forwarded_for == "" {
            .http_x_forwarded_for = "0.0.0.0"
         }

         if .http_x_forwarded_proto == "-" || .http_x_forwarded_proto == "" {
            .http_x_forwarded_proto = "http"
         }

         if .upstream == "-" || .upstream == "" {
            .upstream = "0.0.0.0"
         }

         if .http_cf_ipcountry == "-" || .http_cf_ipcountry == "" {
            .http_cf_ipcountry = "unknown"
         }

         if .http_referer == "-" || .http_referer == "" {
            .http_referer = "unknown"
         }

         if .http_x_current_version == "-" || .http_x_current_version == "" {
            .http_x_current_version = "unknown"
         }

         if .remote_user == "-" || .remote_user == "" {
            .remote_user = "no_user"
         }

         if .ssl_protocol == "-" || .ssl_protocol == "" {
            .ssl_protocol = "no_ssl"
         }

         if .upstream_response_length == "-" || .upstream_response_length == "" {
            .upstream_response_length = "0"
         }

         if .upstream_response_time == "-" || .upstream_response_time == "" {
            .upstream_response_time = "0.0"
         } else {
            .upstream_response_time = to_float!(.upstream_response_time)
         }

    metric_tag_values: single
    drop_on_error: false
    drop_on_abort: true
    reroute_dropped: false
    runtime: ast
{% endif %}

enrichment_tables: {}
tests: []
secret: {}
