---
vector_install: true
vector_template: vector.yaml.j2
vector_systemd_file: /etc/systemd/system/multi-user.target.wants/vector.service
vector_config_file: /etc/vector/vector.yaml
vector_version: 0.36.1
vector_endpoint_host: https://loki.sl.local
vector_app_user: vector
vector_log_file: /var/log/default/vector.log
vector_data_dir: /var/lib/vector
vector_memory_limit: 512M
vector_cpu_quota: 20%

# vector_users_group:
#   - name: "{{ vector_app_user }}"
#     groups: docker
#   - name: '{{ vector_app_user }}'
#     groups: systemd-journal
#   - name: '{{ vector_app_user }}'
#     groups: adm
#   - name: '{{ vector_app_user }}'
#     groups: mysql
#   - name: '{{ vector_app_user }}'
#     groups: zabbix
#   - name: '{{ vector_app_user }}'
#     groups: www-data

vector_service_web_app_log: /var/www/html/console/runtime/logs/app.log
vector_gitlab_log_dir: /data/gitlab/logs
vector_dhcp_log_dir: /var/log/kea
