---
- name: <PERSON><PERSON> | Import a key from a url
  ansible.builtin.rpm_key:
    state: present
    key: https://packages.wazuh.com/key/GPG-KEY-WAZUH

- name: Redhat | Add repository
  ansible.builtin.yum_repository:
    name: wazuh_repo
    description: Wazuh repository
    baseurl: https://packages.wazuh.com/{{ wazuh_version }}/yum/
    gpgcheck: true
    gpgkey: https://packages.wazuh.com/key/GPG-KEY-WAZUH
    enabled: true
    protect: true

- name: Determine WAZUH_AGENT_GROUP
  ansible.builtin.set_fact:
    wazuh_agent_group:
      "{% if '-prod-' in inventory_hostname %}prod\
      {% elif '-dev-' in inventory_hostname %}dev\
      {% elif '-stage-' in inventory_hostname %}stage\
      {% elif '-rc-' in inventory_hostname %}rc\
      {% elif '-test-' in inventory_hostname %}test{% else %}infra{% endif %}"

- name: Redhat | Install wazuh-agent with defined package version
  ansible.builtin.package:
    name: "wazuh-agent-{{ wazuh_release_package }}"
    state: present
  environment:
    WAZUH_MANAGER: "{{ wazuh_manager_ip }}"
    WAZUH_REGISTRATION_PASSWORD: "{{ wazuh_registration_password }}"
    WAZUH_AGENT_GROUP: "{{ wazuh_agent_group }}"
    WAZUH_AGENT_NAME: "{{ inventory_hostname }}.sl.local"
