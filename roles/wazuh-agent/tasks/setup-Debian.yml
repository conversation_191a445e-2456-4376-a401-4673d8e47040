---
- name: "<PERSON><PERSON> | Install gnupg"
  ansible.builtin.apt:
    name: "gnupg"
    state: present

- name: "<PERSON><PERSON> | Add wazuh apt signing key"
  ansible.builtin.apt_key:
    url: http://packages.wazuh.com/key/GPG-KEY-WAZUH
    state: present

- name: "<PERSON><PERSON> | Add wazuh repo to apt list"
  ansible.builtin.apt_repository:
    repo: "deb http://packages.wazuh.com/{{ wazuh_version }}/apt/ stable main"
    state: present
    filename: wazuh

- name: Determine WAZUH_AGENT_GROUP
  ansible.builtin.set_fact:
    wazuh_agent_group:
      "{% if '-prod-' in inventory_hostname %}prod\
      {% elif '-dev-' in inventory_hostname %}dev\
      {% elif '-stage-' in inventory_hostname %}stage\
      {% elif '-rc-' in inventory_hostname %}rc\
      {% elif '-test-' in inventory_hostname %}\
      test{% else %}infra{% endif %}"

- name: <PERSON><PERSON> | Install wazuh-agent with defined package version
  ansible.builtin.apt:
    name: "wazuh-agent={{ wazuh_release_package }}-1"
    state: present
    update_cache: true
  environment:
    WAZUH_MANAGER: "{{ wazuh_manager_ip }}"
    WAZUH_REGISTRATION_PASSWORD: "{{ wazuh_registration_password }}"
    WAZUH_AGENT_GROUP: "{{ wazuh_agent_group }}"
    WAZUH_AGENT_NAME: "{{ inventory_hostname }}.sl.local"
