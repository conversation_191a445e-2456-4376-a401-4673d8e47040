---
- name: "RedHat | Install wazuh-agent"
  ansible.builtin.include_tasks: setup-RedHat.yml
  when: ansible_os_family == "RedHat" or ansible_os_family == "Rocky"

- name: "<PERSON>bian | Install wazuh-agent"
  ansible.builtin.include_tasks: setup-Debian.yml
  when: ansible_os_family == "Debian"

- name: "Start wazuh-agent with service"
  ansible.builtin.systemd:
    name: wazuh-agent
    state: started
    enabled: true
