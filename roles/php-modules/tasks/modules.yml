---
- name: Update and upgrade apt packages
  become: true
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 86400
  when: ansible_distribution == "Debian" or ansible_distribution == 'Ubuntu'

- name: Update and upgrade yum packages
  become: true
  ansible.builtin.apt:
    update_cache: true
  when: ansible_distribution == "Centos" or ansible_os_family == "RedHat"

- name: Install php modules
  become: true
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: true
  with_items: "{{ php_modules }}"
  when: ansible_distribution == "Debian" or ansible_distribution == 'Ubuntu'

- name: Install php modules
  become: true
  ansible.builtin.package:
    name: "{{ item }}"
    state: present
    update_cache: true
  with_items: "{{ centos_php_modules }}"
  when: ansible_distribution == "Centos" or ansible_os_family == "RedHat"
