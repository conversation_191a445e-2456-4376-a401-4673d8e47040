---
# defaults file for clickhouse
clickhouse_version: 22.3.15.33
clickhouse_keeper_version: 22.12.3.5-alpine

# defaults variable for user management:
clickhouse_default_network: "192.168.2.0/23"
clickhouse_default_profile: "default"
clickhouse_access_management: 0
clickhouse_present: 1

# defaults for perfomance settings:
clickhouse_distributed_ddl_pool_size: 1
clickhouse_logger_level: debug
clickhouse_max_concurrent_queries: 0
clickhouse_max_server_memory_usage: 0
clickhouse_max_thread_pool_size: 10000
clickhouse_max_server_memory_usage_to_ram_ratio: 0.9
clickhouse_total_memory_profiler_step: 4194304
clickhouse_total_memory_tracker_sample_probability: 0
clickhouse_uncompressed_cache_size: 8589934592
clickhouse_mark_cache_size: 5368709120
clickhouse_mmap_cache_size: 1000
clickhouse_compiled_expression_cache_size: 134217728
clickhouse_compiled_expression_cache_elements_size: 10000
clickhouse_background_fetches_pool_size: 16
clickhouse_background_pool_size: 16
clickhouse_replicated_max_parallel_fetches_for_host: 15
clickhouse_background_schedule_pool_size: 256

# defaults for log settings:
clickhouse_system_log_table_ttl_days: 7
clickhouse_asynchronous_metric_log_table_ttl_hours: 4
clickhouse_query_log_table_ttl_hours: 4
clickhouse_query_views_log_table_ttl_hours: 4

# path variables:
clickhouse_mounted_device: "/dev/vdb"
clickhouse_path_mounted_disk: "/data"
clickhouse_keeper_mounted_device: "/dev/vdc"
clickhouse_keeper_path_mounted_disk: "/data2"
clickhouse_path_db: "{{ clickhouse_path_mounted_disk }}/clickhouse/db"
clickhouse_path_config: "{{ clickhouse_path_mounted_disk }}/clickhouse/config"
clickhouse_path_configd: "{{ clickhouse_path_mounted_disk }}/clickhouse/config/config.d"
clickhouse_path_usersd: "{{ clickhouse_path_mounted_disk }}/clickhouse/config/users.d"
clickhouse_path_log: "/var/log/clickhouse-server"
clickhouse_path_script: "/script"
clickhouse_path_config_file: "{{ clickhouse_path_mounted_disk }}/clickhouse/config/config.xml"
clickhouse_path_script_file: "{{ clickhouse_path_script }}/backup_clickhouse.sh"
clickhouse_path_config_users_file: "{{ clickhouse_path_mounted_disk }}/clickhouse/config/users.xml"
clickhouse_path_docker_related_config_file: "{{ clickhouse_path_configd }}/docker_related_config.xml"
clickhouse_keeper_path_config: "{{ clickhouse_keeper_path_mounted_disk }}/clickhouse-keeper/config"
clickhouse_keeper_path_config_file: "{{ clickhouse_keeper_path_config }}/keeper_config.xml"
clickhouse_keeper_path_db: "{{ clickhouse_keeper_path_mounted_disk }}/clickhouse-keeper/lib"
clickhouse_keeper_path_log: "/var/log/clickhouse-keeper"

# zabbix intgegration
clickhouse_zabbix_templates:
      - ClickHouse root

# repo variables:
clickhouse_repo_key_id: '8919F6BD2B48D754'
