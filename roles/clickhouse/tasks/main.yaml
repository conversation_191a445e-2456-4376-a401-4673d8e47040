---
# tasks file for clickhouse
- name: Include tasks for OS environment preparation
  ansible.builtin.import_tasks: clickhouse_vars.yaml
  tags: always

- name: Include tasks for OS environment preparation
  ansible.builtin.include_tasks: env.yaml
  tags: env

- name: Include tasks for configuration preparation
  ansible.builtin.include_tasks: clickhouse_config.yaml
  tags: config

- name: Include tasks for install ClickHouse server
  ansible.builtin.include_tasks: clickhouse_server.yaml
  tags: server

- name: Include tasks for ClickHouse client installation
  ansible.builtin.include_tasks: clickhouse_client.yaml
  tags: client

- name: Include tasks for user management
  ansible.builtin.include_tasks: clickhouse_users.yaml
  tags: user

- name: Include tasks for UFW
  ansible.builtin.include_tasks: ufw.yaml
  tags: ufw

- name: Include tasks for Zabbix link templates
  ansible.builtin.include_tasks: zabbix.yaml
  tags: zabbix

- name: Include tasks for create DB roles
  ansible.builtin.include_tasks: clickhouse_roles.yaml
  tags: roles
