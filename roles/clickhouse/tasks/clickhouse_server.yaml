---
- name: Create a Clickhouse keeper docker container
  community.docker.docker_container:
    name: clickhouse-keeper
    image: "clickhouse/clickhouse-keeper:{{ clickhouse_keeper_version }}"
    ulimits: nofile:262144:262144
    mounts:
      - source: "{{ clickhouse_keeper_path_config }}"
        target: /etc/clickhouse-keeper
        type: bind
      - source: "{{ clickhouse_keeper_path_db }}"
        target: /var/lib/clickhouse
        type: bind
      - source: "{{ clickhouse_keeper_path_log }}"
        target: /var/log/clickhouse-keeper
        type: bind
    network_mode: host
    restart: false
    restart_policy: always
    comparisons:
      '*': strict
  tags: server

- name: Create a Clickhouse docker container
  community.docker.docker_container:
    name: clickhouse
    image: "clickhouse/clickhouse-server:{{ clickhouse_version }}"
    ulimits: nofile:262144:262144
    mounts:
      - source: "{{ clickhouse_path_db }}"
        target: /var/lib/clickhouse
        type: bind
      - source: "{{ clickhouse_path_config }}"
        target: /etc/clickhouse-server
        type: bind
      - source: "{{ clickhouse_path_log }}"
        target: /var/log/clickhouse-server
        type: bind
    network_mode: host
    restart: false
    restart_policy: always
    comparisons:
      '*': strict
  tags: server

# published_ports:
#  - 2181:2181 # Zoo clients
#  - 8123:8123 # ClickHouse HTTP client
#  - 9000:9000 # ClickHouse native client
#  - 9363:9363 # Prometheus exporter
#  - 9444:9444 # ClickHouse Keeper cluster (Zoo Cluster analog)
