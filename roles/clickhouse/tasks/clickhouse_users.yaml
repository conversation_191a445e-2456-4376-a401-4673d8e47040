---
- name: Template a ClickHouse user config files
  ansible.builtin.template:
    src: user.j2
    dest: "{{ clickhouse_path_usersd }}/{{- item.name }}.xml"
    mode: '644'
    backup: true
  loop: "{{ clickhouse_user }}"
  tags: user
  when: item.clickhouse_present | default(clickhouse_present)  == 1

- name: Delete absent user
  ansible.builtin.file:
    path: "{{ clickhouse_path_usersd }}/{{- item.name }}.xml"
    state: absent
  when: item.clickhouse_present | default(clickhouse_present)  == 0
  loop: "{{ clickhouse_user }}"
  tags: user
