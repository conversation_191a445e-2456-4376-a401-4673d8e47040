---
# - name: Generate intercluster secret
#   ansible.builtin.set_fact:
#     intercluster_secret: "{{lookup('community.general.random_string', length=20, special=false )}}"
#   delegate_facts: true
#   delegate_to: localhost
#   run_once: true
#   tags: config

- name: Create config from template and copy it destination node
  ansible.builtin.template:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    mode: "{{ mode | default('') }}"
    backup: true
  loop:
    - {src: config.j2, dest: "{{ clickhouse_path_config_file }}"}
    - {src: keeper_config.j2, dest: "{{ clickhouse_keeper_path_config_file }}"}
    - {src: backup_clickhouse.sh.j2, dest: "{{ clickhouse_path_script_file }}", mode: 'u=rx,go='}
  tags: config

- name: Copy config files
  ansible.builtin.copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    mode: "{{ item.mode }}"
    backup: true
  loop:
    - {src: users.xml, dest: "{{ clickhouse_path_config_users_file }}", mode: 'u=rx,go=r'}
    - {src: config.d/docker_related_config.xml, dest: "{{ clickhouse_path_docker_related_config_file }}", mode: 'u=rx,go=r'}
  tags: config
