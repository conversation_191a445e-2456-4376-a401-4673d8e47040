---
- name: UFW tuning for ClickHouse cluster
  community.general.ufw:
    from_ip: "{{ hostvars[item]['ansible_host'] }}"
    port: 2181,9009,9181,9234,9444
    proto: tcp
    rule: allow
  loop: "{{ clickhouse_cluster.nodes | list }}"
  tags: ufw

- name: UFW tuning for OS
  community.general.ufw:
    port: 22,2202,8123,9000,9100,9363,10050,9102,7301,7500,7600
    proto: tcp
    rule: allow
    state: enabled
  tags: ufw

- name: UFW service enabled and restarted
  ansible.builtin.systemd:
    enabled: true
    name: ufw.service
    state: restarted
  tags: ufw
# 2181 - ZooKeeper client
# 8123 - HTTP ClickHouse
# 9000 - native ClickHouse
# 9100 - Prometheus node_exporter
# 9181 - Clickhouse-keeper server port
# 9234 - Clickhouse-keeper RAFT port
# 9363 - ClickHouse Prometheus exporter
# 9444 - ClickHouse Keeper cluster
