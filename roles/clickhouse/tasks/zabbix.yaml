---
- name: Link template for Clickhouse host in zabbix
  become: false
  community.zabbix.zabbix_host:
    host_name: "{{ item }}"
    link_templates: "{{ clickhouse_zabbix_templates }}"
    macros:
      - macro: '{$CLICKHOUSE.HOST}'
        value: '{{ hostvars[item]["ansible_host"] }}'
    interfaces:
      - type: agent
        main: 1
        useip: 1
        ip: '{{ hostvars[item]["ansible_host"] }}'
        port: "{{ zabbix_agent_port }}"
  loop: "{{ ansible_play_hosts_all }}"
  run_once: true
  tags: zabbix
  vars:
    ansible_network_os: community.zabbix.zabbix
    ansible_connection: httpapi
    ansible_httpapi_port: 443
    ansible_httpapi_use_ssl: true
    ansible_httpapi_validate_certs: false
    ansible_zabbix_url_path: ''
    ansible_user: "{{ zabbix_user_ansible }}"
    ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
  delegate_to: "{{ zabbix_server_web }}"
