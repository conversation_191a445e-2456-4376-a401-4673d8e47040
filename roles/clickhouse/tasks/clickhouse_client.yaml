---
- name: Install packages for clickhouse-client
  ansible.builtin.apt:
    name:
      - apt-transport-https
      - ca-certificates
      - dirmngr
    update_cache: true
  tags: client

- name: Add apt key ClickHouse
  ansible.builtin.apt_key:
    keyserver: keyserver.ubuntu.com
    id: "{{ clickhouse_repo_key_id }}"
  tags: client

- name: Add ClichHouse repository into sources list
  ansible.builtin.apt_repository:
    repo: deb https://packages.clickhouse.com/deb stable main
  tags: client

- name: Install clickhouse-client
  ansible.builtin.apt:
    name: "{{ item }}"
    update_cache: true
    allow_downgrade: true
  tags: client
  loop:
    - "clickhouse-common-static={{ clickhouse_version }}"
    - "clickhouse-client={{ clickhouse_version }}"
