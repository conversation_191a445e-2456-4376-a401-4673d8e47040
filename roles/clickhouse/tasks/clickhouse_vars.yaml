---
- name: Check all playing hosts have exactly one group included 'clickhouse' in name
  ansible.builtin.assert:
    that: group_names | select('search', 'clickhouse') | list | length == 1
    fail_msg: Host must have exactly one group with 'clickhouse' in name
    success_msg: "Ok: Host have exactly one group with 'clickhouse' in name"

- name: Set cluster name
  ansible.builtin.set_fact:
    cluster_name: "{{ (group_names | select('search', 'clickhouse'))[0] }}"

- name: Set clickhouse_cluster variable
  ansible.builtin.set_fact:
    clickhouse_cluster: "{{ clickhouse_cluster | default({'nodes': []}) }}"

- name: Populate clickhouse_cluster variable
  ansible.builtin.set_fact:
    clickhouse_cluster:
      nodes: "{{ clickhouse_cluster.nodes | default([]) + [item] }}"
  loop: "{{ groups[cluster_name] }}"
