---
- name: Cluster nodes in /etc/hosts
  ansible.builtin.template:
    src: hosts.j2
    dest: /etc/hosts
    mode: '644'
  tags: env

- name: Create a ext4 filesystems on devices
  community.general.filesystem:
    fstype: ext4
    dev: "{{ item }}"
  loop:
    - /dev/vdb
    - /dev/vdc
  tags: env

- name: Create fstab entries and mount filesystems
  ansible.posix.mount:
    backup: true
    boot: true
    fstype: ext4
    opts: noatime
    path: "{{ item.path }}"
    src: "{{ item.dev }}"
    state: "{{ item.state }}"
  loop:
    - {path: "{{ clickhouse_path_mounted_disk }}", dev: "{{ clickhouse_mounted_device }}", state: mounted}
    - {path: "{{ clickhouse_keeper_path_mounted_disk }}", dev: "{{ clickhouse_keeper_mounted_device }}", state: mounted}
  tags: env

- name: Create a directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: "{{ item.state }}"
    owner: "{{ item.owner }}"
    group: "{{ item.group }}"
    mode: '755'
  loop:
    - {path: "{{ clickhouse_path_db }}", state: directory, owner: 101, group: 101}
    - {path: "{{ clickhouse_path_configd }}", state: directory, owner: 101, group: 101}
    - {path: "{{ clickhouse_path_usersd }}", state: directory, owner: 101, group: 101}
    - {path: "{{ clickhouse_path_log }}", state: directory, owner: 101, group: 101}
    - {path: "{{ clickhouse_path_script }}", state: directory, owner: 0, group: 0}
    - {path: "{{ clickhouse_keeper_path_config }}", state: directory, owner: 101, group: 101}
    - {path: "{{ clickhouse_keeper_path_db }}", state: directory, owner: 101, group: 101}
    - {path: "{{ clickhouse_keeper_path_log }}", state: directory, owner: 101, group: 101}
  tags: env

- name: Disable swap
  ansible.builtin.command: swapoff -a
  changed_when: true
  tags: env
- name: Disable swap in fstab
  ansible.posix.mount:
    state: absent_from_fstab
    path: none
    src: /swap.img
    backup: true
  tags: env
