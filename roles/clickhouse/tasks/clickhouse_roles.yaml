---
- name: Set ClickHose Connection String
  ansible.builtin.set_fact:
    clickhouse_connection_string: "clickhouse-client --user {{ clickhouse_admin_user }} --password {{ clickhouse_admin_password }}"
  tags: roles

- name: Create LDAP admin role in DB
  ansible.builtin.command: "{{ clickhouse_connection_string }} -q 'CREATE ROLE IF NOT EXISTS admins'"
  tags: roles
  changed_when: true

- name: Set grant to admin role
  ansible.builtin.command: "{{ clickhouse_connection_string }} -q 'GRANT ALL ON *.* TO admins'"
  tags: roles
  changed_when: true

- name: Create LDAP select role in DB
  ansible.builtin.command: "{{ clickhouse_connection_string }} -q 'CREATE ROLE IF NOT EXISTS selectonly'"
  tags: roles
  changed_when: true

- name: Set grant to select role
  ansible.builtin.command: "{{ clickhouse_connection_string }} -q 'GRANT SELECT ON *.* TO selectonly'"
  tags: roles
  changed_when: true
