clickhouse
=========

Deploy new ClickHouse cluster.

Requirements
------------

- 4 servers with infrastructure role and static or reserved IP address
- block devices
  \dev\vdb - for data
- last symbol in inventory_hostname used as number of node in ClickHouse Keeper cluster
- "clickhouse_cluster" dictionary variable with list nodes
- Create and write cluster secret in clickhouse distributed secret variable

Role Variables
--------------
Example:

- env_clickhouse: "stage"
- clickhouse_admin_user: "admin"
- clickhouse_admin_password: "pass"
- clickhouse_distributed_secret: "1Jp1p5VEl2jpdefRKJfS"
- clickhouse_user:
    - name: backup
      comment: user for backup ClickHouse
      password: f0f494077a7b110fc95d485929ce4cd7975324f47014c967517bcaf1f0432866

To Do
----------------
* Add random secret for intercluster distributed tables queries
