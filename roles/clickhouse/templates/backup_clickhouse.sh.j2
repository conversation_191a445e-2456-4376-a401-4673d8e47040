{% raw %}
backup_name=CH_backup_$(date +%Y-%m-%d-%H-%M)
log_path=/var/log/clickhouse-backup.log
ext_backup_path=/backup/${backup_name}.tar.gz
host_backup_path={% endraw %}{{ clickhouse_path_db }}{% raw %}/backup/${backup_name}
db_path_ext={% endraw %}{{ clickhouse_path_db }}{% raw %}
db_path_in=/var/lib/clickhouse
CLICKHOUSE_USERNAME='backup'
CLICKHOUSE_PASSWORD='{% endraw %}{{ clickhouse_backup_password }}{% raw %}'
exec >> ${log_path}
exec 2>&1

echo '         '
echo '         '
echo '         '
echo '*********'
echo '*********'
echo '*********'
echo '\n'Start backup script. $(date)
echo Variables and redirections set is done. $(date)

docker run --rm \
  --log-driver=journald \
  --log-opt labels=CH_backup \
  --network host \
  -v "${db_path_ext}:${db_path_in}" \
  -e CLICKHOUSE_USERNAME=${CLICKHOUSE_USERNAME} \
  -e CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD} \
  alexakulov/clickhouse-backup create ${backup_name}

echo '*********'
echo Create backup done
echo '*********'

tar \
  -czf ${ext_backup_path} \
  $host_backup_path

echo '*********'
echo Compress archieve done
echo '*********'

find /backup -name "*.gz" -type f -mtime +30 -delete 

echo '*********'
echo Delete old archievies done
echo '*********'

docker run --rm \
  --log-driver=journald \
  --log-opt labels=CH_backup \
  --network host \
  -v "${db_path_ext}:${db_path_in}" \
  -e CLICKHOUSE_USERNAME=${CLICKHOUSE_USERNAME} \
  -e CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD} \
  alexakulov/clickhouse-backup delete local ${backup_name}

echo '*********'
echo Delete ClickHouse freeze backup done
echo '*********'

echo End backup script. $(date)
echo '*********'
echo '*********'
echo '*********'
{% endraw %}
