---
os_nodes: |-
        {% for item in ansible_play_hosts_all -%}
          {{ hostvars[item]['ip'] }}{% if not loop.last %}","{% endif %}
        {%- endfor %}

populate_inventory_to_hosts_file: true

os_dashboards_home: /usr/share/opensearch-dashboards
os_conf_dir: /usr/share/opensearch-dashboards/config
os_plugin_bin_path: /usr/share/opensearch-dashboards/bin/opensearch-dashboards-plugin

os_api_port: 9200
os_nodes_dashboards: |-
        {% for item in ansible_play_hosts_all -%}
          https://{{ hostvars[item]['ip'] }}:{{ os_api_port }}{% if not loop.last %}","{% endif %}
        {%- endfor %}

systemctl_path: /etc/systemd/system
