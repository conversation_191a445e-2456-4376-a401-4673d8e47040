---
- name: If old config exists
  ansible.builtin.stat:
    path: /etc/rabbitmq/rabbitmq.config
  register: old_config

- name: Remove old config
  ansible.builtin.file:
    path: /etc/rabbitmq/rabbitmq.config
    state: absent
  when: old_config.stat.exists

- name: If old ldap config exists
  ansible.builtin.stat:
    path: /etc/rabbitmq/advanced.config
  register: old_ldap_config

- name: Remove old ldap config
  ansible.builtin.file:
    path: /etc/rabbitmq/advanced.config
    state: absent
  when: old_ldap_config.stat.exists

- name: Render rmq config
  ansible.builtin.template:
    src: rabbitmq.conf.j2
    dest: /etc/rabbitmq/rabbitmq.conf
    owner: rabbitmq
    group: rabbitmq
    mode: 0655

- name: Render ldap config
  ansible.builtin.template:
    src: advanced.config.j2
    dest: /etc/rabbitmq/advanced.config
    owner: rabbitmq
    group: rabbitmq
    mode: 0655

- name: Rmq plugins
  ansible.builtin.copy:
    content: "[{% for plugin in rmq_config.plugins %}
      {{ plugin }}{{ ',' if not loop.last else '' }} {% endfor %}]"
    dest: /etc/rabbitmq/enabled_plugins
    owner: rabbitmq
    group: rabbitmq
    mode: 0600
  notify:
    - Restart rmq

- name: Cluster cookie
  ansible.builtin.copy:
    content: "{{ rabbitmq_cookie }}"
    dest: /var/lib/rabbitmq/.erlang.cookie
    owner: rabbitmq
    group: rabbitmq
    mode: 0600

- name: Cluster nodes in /etc/hosts
  ansible.builtin.template:
    src: hosts.j2
    dest: /etc/hosts
    mode: 0655
