---
- name: Create user
  when:
    - (cluster_status.stdout|from_json).listeners | length < 3
    - ansible_play_hosts[1] == inventory_hostname
  block:
    - name: Create user
      ansible.builtin.command: rabbitmqctl add_user {{ rmq_user }} {{ rmq_password }}
      changed_when: false
    - name: Added to administrator
      ansible.builtin.command: rabbitmqctl set_user_tags {{ rmq_user }} administrator
      changed_when: false
    - name: Added to administrator
      ansible.builtin.command: rabbitmqctl set_permissions -p / {{ rmq_user }} ".*" ".*" ".*"
      changed_when: false
