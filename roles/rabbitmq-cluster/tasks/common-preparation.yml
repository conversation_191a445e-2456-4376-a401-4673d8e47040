---
- name: Check all playing hosts have exactly one group included 'rabbitmq' in name
  ansible.builtin.assert:
    that: group_names | select('search', 'rabbitmq') | list | length == 1
    fail_msg: Host must have exactly one group with 'rabbitmq' in name
    success_msg: "Ok: Host have exactly one group with 'rabbitmq' in name"

- name: Set cluster name
  ansible.builtin.set_fact:
    cluster_name: "{{ (group_names | select('search', 'rabbitmq'))[0] }}"
