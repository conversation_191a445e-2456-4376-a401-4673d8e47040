---
- name: Check cluster state
  ansible.builtin.command: rabbitmqctl cluster_status --formatter json
  changed_when: false
  register: cluster_status
  check_mode: false

- name: Join second to first
  when:
    - (cluster_status.stdout|from_json).listeners | length < 3
    - ansible_play_hosts[1] == inventory_hostname
  block:
    - name: Stop second rmq app
      ansible.builtin.command: rabbitmqctl stop_app
      changed_when: false

    - name: Join second node to first
      ansible.builtin.command: "rabbitmqctl join_cluster rabbit@{{ ansible_play_hosts[0] }}"
      changed_when: false

    - name: Start second rmq app
      ansible.builtin.command: rabbitmqctl start_app
      changed_when: false

- name: Join third to first
  when:
    - (cluster_status.stdout|from_json).listeners | length < 3
    - ansible_play_hosts[2] == inventory_hostname
  block:
    - name: Stop third rmq app
      ansible.builtin.command: rabbitmqctl stop_app
      changed_when: false

    - name: Join third node to first
      ansible.builtin.command: "rabbitmqctl join_cluster rabbit@{{ ansible_play_hosts[0] }}"
      changed_when: false

    - name: Start third rmq app
      ansible.builtin.command: rabbitmqctl start_app
      changed_when: false
