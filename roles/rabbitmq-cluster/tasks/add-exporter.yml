---
- name: Create a rabbitmq-exporter docker container
  community.docker.docker_container:
    name: rabbitmq-exporter
    image: "{{ rabbitmq_exporter_image }}"
    restart: true
    restart_policy: always
    ports:
      - 9419:9419
    env:
      RABBIT_URL: "http://{{ ansible_ssh_host }}:15672"
      RABBIT_USER: "{{ rabbitmq_exporter_user }}"
      RABBIT_PASSWORD: "{{ rabbitmq_exporter_password }}"
      PUBLISH_PORT: "9419"
- name: Add exporter config for consul
  ansible.builtin.template:
    src: "{{ item }}.j2"
    dest: "/opt/consul-agent/consul.d/{{ item }}"
    mode: 0644
    owner: "consul"
    group: "consul"
  with_items: "{{ rabbitmq_exporter_config }}"
  notify: Restart consul-agent
