rabbitmq-role
=========

Create rabbitmq cluster and integrate ldap with freeipa

Tag policy based on ldap ($env - means host environment)
------------
* $env-rabbit-admins - group of rabbits admin.
* $env-rabbit-monitoring - group for users to access with tag monitoring.

Vhost access policy based on ldap
------------
* $env-rabbit-access-all - access to all vhost on cluster
* $env-rabbit-access-${vhost} - access to specific vhost on cluster where ${vhost} means name of vhosts
* $env-rabbit-access-r - access with read olnly to all vhosts

Resource access policy based on ldap
------------
* $env-rabbit-access-c-${vhost} - access to specific vhost with configure permission
* $env-rabbit-access-w-${vhost} - access to specific vhost with write permission
* $env-rabbit-access-r-${vhost} - access to specific vhost with read permission




Users add example
--------------

For technical user for specific vhosts - $env-rabbit-access-${vhost}, $env-rabbit-access-c-${vhost}, $env-rabbit-access-w-${vhost}, $env-rabbit-access-r-${vhost}

For admin user - $env-rabbit-admins

For monitoring user - $env-rabbit-access-r, $env-rabbit-monitoring


