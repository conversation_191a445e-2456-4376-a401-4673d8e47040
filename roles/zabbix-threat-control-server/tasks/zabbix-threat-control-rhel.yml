---
- name: Download sender package
  ansible.builtin.get_url:
    url:
      "https://repo.zabbix.com/zabbix/{{ zabbix_version }}/rhel/7/x86_64/\
      zabbix-sender_{{ zabbix_version_patch }}-1.el7.x86_64.rpm"
    dest: /tmp/zabbix-sender.rpm
    mode: '0644'
- name: Install sender repo
  ansible.builtin.package:
    name: /tmp/zabbix-sender.rpm
    state: present
- name: Install zabbix-sender
  ansible.builtin.package:
    name: zabbix-sender
    state: present
    update_cache: true
- name: Install Dependencies
  ansible.builtin.package:
    name: ['python3-pip', 'python3-setuptools', 'git']
    state: latest
    update_cache: true
- name: Install python requirements
  ansible.builtin.pip:
    name:
      - pyzabbix
      - jpath
      - vulners
- name: Clone a github repository
  ansible.builtin.git:
    repo: https://github.com/vulnersCom/zabbix-threat-control.git
    dest: /opt/monitoring/zabbix-threat-control/
    clone: true
    update: true
    version: master
- name: Fix permissions
  ansible.builtin.file:
    path: "/opt/monitoring/zabbix-threat-control"
    owner: zabbix
    group: zabbix
    mode: '0755'
    recurse: true
- name: Create log file
  ansible.builtin.file:
    path: "{{ plugin_log_file }}"
    state: touch
    mode: '0664'
    owner: zabbix
    group: zabbix
- name: Create new config file from template
  ansible.builtin.template:
    force: true
    backup: true
    dest: /opt/monitoring/zabbix-threat-control/ztc.conf
    src: ztc.conf.j2
    mode: '0644'
- name: Run a script with arguments
  ansible.builtin.command: "/opt/monitoring/zabbix-threat-control/prepare.py -vtd"
  changed_when: false
