---
- name: Install on server "Debian"
  ansible.builtin.include_tasks: zabbix-threat-control-debian.yml
  when: ansible_distribution == "Debian"
- name: Install on server "Ubuntu"
  ansible.builtin.include_tasks: zabbix-threat-control-ubuntu.yml
  when: ansible_distribution  == "Ubuntu"
- name: Install on server Rhel
  ansible.builtin.include_tasks: zabbix-threat-control-rhel.yml
  when: ansible_distribution == "Centos" or ansible_distribution == "RedHat"
