---
- name: Infrastructure
  hosts: test_opensearch
  become: true
  gather_facts: true
  roles:
    # - {role: common, tags: common}
    - {role: ntpd, tags: ntpd}
    - {role: python, tags: python}
    - {role: docker, tags: docker}
    - {role: prometheus-exporters, tags: prometheus-exporters}
    # - {role: zabbix-agent2, tags: zabbix-agent2}
    - {role: users, tags: users}
    - {role: sshd_config, tags: sshd_config}
    - {role: systemd-resolved, tags: systemd-resolved}
    - {role: freeipa-client, tags: freeipa-client}
    - {role: freeipa-dnsrecord, tags: freeipa-dnsrecord}
    # - {role: zabbix-threat-control-agent, tags: zabbix-threat-control-agent}
    - {role: logrotate, tags: logrotate}
    - {role: vector, tags: vector, when: vector_install}
    # - {role: bareos-client, tags: bareos-client}
    # - {role: consul-agents, tags: consul-agents}
    # - {role: wazuh-agent, tags: wazuh-agent}
  tasks:
    - name: Reboot if reboot variable defined
      ansible.builtin.reboot:
      when: reboot is defined
