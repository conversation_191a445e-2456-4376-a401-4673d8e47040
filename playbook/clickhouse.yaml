---
- name: Clickhouse
  hosts: all
  become: true
  gather_facts: false
  roles:
    - {role: clickhouse, tags: clickhouse}
  tasks:
    - name: Restart clickhouse-keeper container
      community.docker.docker_container:
        name: clickhouse-keeper
        restart: true
      tags:
        - restart-keeper
        - restart
        - never

    - name: Restart clickhouse-server container
      community.docker.docker_container:
        name: clickhouse
        restart: true
      tags:
        - restart-server
        - restart
        - never

    - name: Stop clickhouse-server container
      community.docker.docker_container:
        name: clickhouse
        state: stopped
      tags:
        - stop-server
        - stop
        - never

    - name: Stop clickhouse-keeper container
      community.docker.docker_container:
        name: clickhouse-keeper
        state: stopped
      tags:
        - stop-keeper
        - stop
        - never

    - name: Clear Keeper
      ansible.builtin.file:
        state: "{{ item }}"
        path: "{{ clickhouse_keeper_path_db }}"
        owner: 101
        group: 101
        mode: '0755'
      with_items:
        - absent
        - directory
      tags:
        - clear-keeper
        - never
