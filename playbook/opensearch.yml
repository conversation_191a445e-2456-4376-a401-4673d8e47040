---
- name: OpenSearch cluster and dashboards installation & configuration
  hosts: test_opensearch
  become: true
  gather_facts: true
  # serial: 1
  # max_fail_percentage: 0
  pre_tasks:
    - name: Verify connectivity to all hosts
      ansible.builtin.ping:

    - name: Check if all required variables are defined
      ansible.builtin.assert:
        that:
          - os_cluster_name is defined
          - os_cluster_version is defined
          - os_cluster_domain_name is defined
        fail_msg: "Required variables are not defined for OpenSearch cluster"

  roles:
    - role: os_cluster
  tags: opensearch

  post_tasks:
    - name: Verify OpenSearch cluster is healthy
      ansible.builtin.uri:
        url: "https://{{ ansible_default_ipv4.address }}:9200/_cluster/health"
        method: GET
        user: admin
        password: "{{ os_cluster_admin_password }}"
        force_basic_auth: true
        validate_certs: false
        return_content: true
      register: cluster_health
      retries: 5
      delay: 10
      until: cluster_health.status == 200 and cluster_health.json.status in ['green', 'yellow']
      run_once: true

    - name: Verify OpenSearch Dashboards is accessible (if enabled)
      ansible.builtin.uri:
        url: "http://{{ os_cluster_dashboards_host }}:{{ os_cluster_dashboards_port }}/api/status"
        method: GET
        timeout: 30
        status_code: [200, 401, 403]
      register: dashboards_health
      retries: 5
      delay: 10
      until: dashboards_health.status in [200, 401, 403]
      when: os_cluster_dashboards_enabled | default(false)

    - name: Display Dashboards access information
      ansible.builtin.debug:
        msg: "OpenSearch Dashboards is accessible at http://{{ os_cluster_dashboards_host }}:{{ os_cluster_dashboards_port }}"
      when:
        - os_cluster_dashboards_enabled | default(false)
        - dashboards_health.status in [200, 401, 403]
