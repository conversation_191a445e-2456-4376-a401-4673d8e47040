---
- name: Install Percona Server
  hosts: repricer-mysql-db-01
  roles:
    - percona_cluster
  vars:
    percona_server_users_present:
      - name: replicator
        password: 'replicator'
        privs:
          - '*.*:REPLICATION SLAVE'
        hosts:
          - '%'
      - name: sellerlogic
        password: 'Zaq12wsx'
        privs:
          - '*.*:ALL'
        hosts:
          - '%'
          - 'localhost'
      - name: sellerlogic_prod
        password: 'owqfbB3UlW!K8i58'
        privs:
          - '*.*:ALL'
        hosts:
          - '%'
          - 'localhost'

    percona_server_etc_my_cnf:
      - section: mysqld
        options:
          - name: server_id
            value: 1
          - name: log_bin
            value: mysql-bin
          - name: log_bin_index
            value: mysql-bin.index
          - name: sync_binlog
            value: 1
          - name: report_host
            value: "{{ inventory_hostname }}"
          - name: thread_pool_size
            value: 64
          - name: thread_pool_max_threads
            value: 65536
          - name: thread_handling
            value: pool-of-threads
          - name: tmp_table_size
            value: 32G
          - name: max_heap_table_size
            value: 32G
          - name: key_buffer_size
            value: 4G
          - name: query_cache_limit
            value: 0
          - name: query_cache_type
            value: 0
          - name: query_cache_size
            value: 0
          - name: join_buffer_size
            value: 1G
          - name: innodb_buffer_pool_instances
            value: 8
          - name: innodb_buffer_pool_size
            value: 194G
          - name: table_open_cache
            value: 100000
          - name: open_files_limit
            value: 100000
          - name: max_connections
            value: 10240
          - name: innodb-flush-method
            value: O_DIRECT
          - name: innodb_write_io_threads
            value: 64
          - name: innodb_read_io_threads
            value: 64
          - name: innodb_io_capacity
            value: 500000
          - name: innodb_io_capacity_max
            value: 1000000
          - name: innodb_lru_scan_depth
            value: 512
          - name: innodb_log_file_size
            value: 8G
          - name: innodb_file_per_table
            value: "OFF"
          - name: innodb_stats_persistent
            value: 0
- name: "Configure second node"
  hosts: repricer-mysql-db-02
  roles:
    - percona_cluster
  vars:
    percona_server_users_present:
      - name: replicator
        password: 'replicator'
        privs:
          - '*.*:REPLICATION SLAVE'
        hosts:
          - '%'
      - name: sellerlogic
        password: 'Zaq12wsx'
        privs:
          - '*.*:ALL'
        hosts:
          - '%'
          - 'localhost'
      - name: sellerlogic_prod
        password: 'owqfbB3UlW!K8i58'
        privs:
          - '*.*:ALL'
        hosts:
          - '%'
          - 'localhost'
    percona_server_etc_my_cnf:
      - section: mysqld
        options:
          - name: server_id
            value: 2
          - name: relay_log
            value: mysql-relay
          - name: relay_log_index
            value: mysql-relay.index
          - name: sync_relay_log
            value: 1
          - name: report_host
            value: "{{ inventory_hostname }}"
          - name: read_only
            value: 1
          - name: skip_slave_start
            value: 1
          - name: thread_pool_size
            value: 64
          - name: thread_pool_max_threads
            value: 65536
          - name: thread_handling
            value: pool-of-threads
          - name: tmp_table_size
            value: 32G
          - name: max_heap_table_size
            value: 32G
          - name: key_buffer_size
            value: 4G
          - name: query_cache_limit
            value: 0
          - name: query_cache_type
            value: 0
          - name: query_cache_size
            value: 0
          - name: join_buffer_size
            value: 1G
          - name: innodb_buffer_pool_instances
            value: 8
          - name: innodb_buffer_pool_size
            value: 194G
          - name: table_open_cache
            value: 100000
          - name: open_files_limit
            value: 100000
          - name: max_connections
            value: 10240
          - name: innodb-flush-method
            value: O_DIRECT
          - name: innodb_write_io_threads
            value: 64
          - name: innodb_read_io_threads
            value: 64
          - name: innodb_io_capacity
            value: 500000
          - name: innodb_io_capacity_max
            value: 1000000
          - name: innodb_lru_scan_depth
            value: 512
          - name: innodb_log_file_size
            value: 8GB
          - name: innodb_file_per_table
            value: "OFF"
          - name: innodb_stats_persistent
            value: 0
