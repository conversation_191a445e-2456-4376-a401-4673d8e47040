---
- name: Install security updates and reboot if needed
  hosts: all_server
  become: true
  serial: 1
  tasks:
    - name: Gather package facts
      ansible.builtin.package_facts:
        manager: auto

    - name: Hold specific packages
      ansible.builtin.dpkg_selections:
        name: "{{ item }}"
        selection: hold
      when: item in ansible_facts.packages
      loop: "{{ awx_security_updates_hold_packages }}"

    - name: Install security updates
      ansible.builtin.apt:
        upgrade: 'dist'
        update_cache: true
        cache_valid_time: 3600
        force_apt_get: true
        default_release: "{{ ansible_distribution_release }}-security"
      register: security_upgrade

    - name: Check if a reboot is needed
      ansible.builtin.stat:
        path: /var/run/reboot-required
      register: reboot_required

    - name: Create maintenance for host in zabbix
      become: false
      community.zabbix.zabbix_maintenance:
        name: Update and reboot {{ ansible_hostname }}
        host_name: "{{ ansible_hostname }}"
        state: present
        minutes: 10
        collect_data: false
      vars:
        ansible_network_os: community.zabbix.zabbix
        ansible_connection: httpapi
        ansible_httpapi_port: 443
        ansible_httpapi_use_ssl: true
        ansible_httpapi_validate_certs: false
        ansible_host: "{{ zabbix_server_web }}"
        ansible_zabbix_url_path: ''
        ansible_user: "{{ zabbix_user_ansible }}"
        ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
      when: reboot_required.stat.exists

    - name: Reboot the server if required
      ansible.builtin.reboot:
        msg: "Reboot initiated by Ansible for security updates"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: uptime
      when: reboot_required.stat.exists

    - name: Wait for the server to come back
      ansible.builtin.wait_for_connection:
        delay: 30
        timeout: 300
      when: reboot_required.stat.exists

    - name: Remove maintenance for host in zabbix
      become: false
      community.zabbix.zabbix_maintenance:
        name: Update and reboot {{ ansible_hostname }}
        state: absent
      vars:
        ansible_network_os: community.zabbix.zabbix
        ansible_connection: httpapi
        ansible_httpapi_port: 443
        ansible_httpapi_use_ssl: true
        ansible_httpapi_validate_certs: false
        ansible_host: "{{ zabbix_server_web }}"
        ansible_zabbix_url_path: ''
        ansible_user: "{{ zabbix_user_ansible }}"
        ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
      when: reboot_required.stat.exists
