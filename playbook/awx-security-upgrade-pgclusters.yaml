---
- name: Install security updates on pgclusters
  hosts: all_server
  become: true
  serial: 1
  tasks:
    - name: Hold PostgreSQL and Pgbouncer packages
      ansible.builtin.dpkg_selections:
        name: "{{ item }}"
        selection: hold
      loop:
        - pgbouncer
        - postgresql-{{ postgresql_version }}
        - postgresql-client-{{ postgresql_version }}
        - postgresql-client-common
        - postgresql-common
        - postgresql-server-dev-{{ postgresql_version }}
        - pgbackrest

    - name: Install security updates
      ansible.builtin.apt:
        upgrade: 'dist'
        update_cache: true
        cache_valid_time: 3600
        force_apt_get: true
        default_release: "{{ ansible_distribution_release }}-security"
      register: security_upgrade

    - name: Check if a reboot is needed
      ansible.builtin.stat:
        path: /var/run/reboot-required
      register: reboot_required

    - name: Check patroni pause mode on api 8008/patroni
      ansible.builtin.uri:
        url: "http://{{ ansible_ssh_host }}:8008/patroni"
        method: GET
        return_content: true
      register: patroni_pause_check
      when: reboot_required.stat.exists

    - name: If pause mode is on fail the playbook
      ansible.builtin.fail:
        msg: "<PERSON><PERSON><PERSON> is in pause mode, please check it before running this playbook"
      when: patroni_pause_check.json['pause'] is defined and patroni_pause_check.json['pause'] == 'true' and reboot_required.stat.exists

    - name: Check current patroni cluster health
      ansible.builtin.uri:
        url: "http://{{ ansible_ssh_host }}:8008/patroni"
        method: GET
        return_content: true
      register: patroni_health_check
      when: reboot_required.stat.exists

    - name: Parse Patroni health information
      ansible.builtin.set_fact:
        patroni_health: "{{ patroni_health_check.json['state'] }}"
      when: patroni_health_check is succeeded and reboot_required.stat.exists

    - name: Debug
      ansible.builtin.debug:
        msg: "Patroni health is {{ patroni_health }}"
      when: patroni_health is defined

    - name: If patroni health is not 'running' fail the playbook
      ansible.builtin.fail:
        msg: "Patroni health is not 'running' or 'starting', please check it before running this playbook"
      when: patroni_health is defined and patroni_health != 'running' and patroni_health != 'starting' and reboot_required.stat.exists

    - name: Wait 60 seconds
      ansible.builtin.wait_for:
        timeout: 60
      when: patroni_health is defined and patroni_health == 'starting' and reboot_required.stat.exists

    - name: Check if current node is the leader
      ansible.builtin.command: patronictl list --format json
      register: patroni_leader_check
      changed_when: false
      when: reboot_required.stat.exists

    - name: Parse Patroni leader information
      ansible.builtin.set_fact:
        is_leader: "{{ (patroni_leader_check.stdout | from_json) |
                        selectattr('Member', 'equalto', ansible_hostname) |
                        map(attribute='Role') |
                        list |
                        first == 'Leader' }}"
      when: patroni_leader_check is defined and patroni_leader_check is succeeded and reboot_required.stat.exists

    - name: Find node with tag nofailover
      ansible.builtin.command: patronictl list --format json
      register: patroni_nofailover_check
      changed_when: false
      when: reboot_required.stat.exists

    - name: Find tag nofailover on second and third node
      ansible.builtin.set_fact:
        nofailover: "{{ (patroni_nofailover_check.stdout | from_json) |
                        selectattr('Tags', 'defined') |
                        selectattr('Tags.nofailover', 'equalto', true) |
                        map(attribute='Member') |
                        list |
                        length > 0 }}"
      when: patroni_nofailover_check is defined and patroni_nofailover_check is succeeded and reboot_required.stat.exists

    - name: Debug
      ansible.builtin.debug:
        msg: "Nodes with nofailover tag - {{ nofailover }}"
      when: patroni_nofailover_check is defined and patroni_nofailover_check is succeeded and reboot_required.stat.exists

    - name: Get cluster name
      ansible.builtin.command: patronictl list --format json
      register: patroni_cluster_output
      changed_when: false
      when: reboot_required.stat.exists

    - name: Parse Patroni cluster name
      ansible.builtin.set_fact:
        parsed_patroni_cluster_name: "{{ (patroni_cluster_output.stdout | from_json) |
                                        map(attribute='Cluster') |
                                        list |
                                        first }}"
      when: patroni_cluster_output is defined and patroni_cluster_output is succeeded and reboot_required.stat.exists

    - name: Debug
      ansible.builtin.debug:
        msg: "Patroni cluster name is {{ parsed_patroni_cluster_name }}"
      when: patroni_cluster_output is defined and patroni_cluster_output is succeeded and reboot_required.stat.exists

    - name: Get all member names
      ansible.builtin.command: patronictl list --format json
      register: patroni_member_name
      changed_when: false
      when: reboot_required.stat.exists

    - name: Set facts for all member names
      ansible.builtin.set_fact:
        parsed_patroni_member_names: "{{ (patroni_member_name.stdout | from_json) | map(attribute='Member') | list }}"
      when: patroni_member_name is defined and patroni_member_name is succeeded and reboot_required.stat.exists

    - name: Debug
      ansible.builtin.debug:
        msg: "Patroni member names are {{ parsed_patroni_member_names }}"
      when: patroni_member_name is defined and patroni_member_name is succeeded and reboot_required.stat.exists

    - name: Change nofailover tag to second node
      ansible.builtin.lineinfile:
        path: /etc/patroni/postgres.yml
        regexp: '^    nofailover: true$'
        line: '    nofailover: false'
        backrefs: true
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[2]
      delegate_to: "{{ parsed_patroni_member_names[1] }}"

    - name: Change nofailover tag to third node
      ansible.builtin.lineinfile:
        path: /etc/patroni/postgres.yml
        regexp: '^    nofailover: true$'
        line: '    nofailover: false'
        backrefs: true
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[1]
      delegate_to: "{{ parsed_patroni_member_names[2] }}"

    - name: Reload patroni on second node they haved tag nofailover
      ansible.builtin.command: patronictl reload {{ parsed_patroni_cluster_name }} {{ parsed_patroni_member_names[1] }} --force
      changed_when: false
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[2]

    - name: Reload patroni on third node they haved tag nofailover
      ansible.builtin.command: patronictl reload {{ parsed_patroni_cluster_name }} {{ parsed_patroni_member_names[2] }} --force
      changed_when: false
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[1]

    - name: Wait for the second node to Tags undefined
      ansible.builtin.command: patronictl list --format json
      register: patroni_replica_check
      retries: 60
      delay: 10
      until:
        - patroni_replica_check is succeeded
        - >
          {{
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', parsed_patroni_member_names[1])
            | selectattr('Tags', 'defined')
            | list | length > 0 and
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', parsed_patroni_member_names[1])
            | selectattr('Tags', 'defined')
            | map(attribute='Tags')
            | first
            == ''
            or
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', parsed_patroni_member_names[1])
            | selectattr('Tags', 'undefined')
            | list | length > 0
          }}
      changed_when: false
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[2]

    - name: Wait for the third node to Tags undefined
      ansible.builtin.command: patronictl list --format json
      register: patroni_replica_check
      retries: 60
      delay: 10
      until:
        - patroni_replica_check is succeeded
        - >
          {{
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', parsed_patroni_member_names[2])
            | selectattr('Tags', 'defined')
            | list | length > 0 and
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', parsed_patroni_member_names[2])
            | selectattr('Tags', 'defined')
            | map(attribute='Tags')
            | first
            == ''
            or
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', parsed_patroni_member_names[2])
            | selectattr('Tags', 'undefined')
            | list | length > 0
          }}
      changed_when: false
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[1]

    - name: Move leader from first to second node
      ansible.builtin.command: >
        patronictl switchover --force
        --leader {{ parsed_patroni_member_names[0] }}
        --candidate {{ parsed_patroni_member_names[1] }}
      changed_when: false
      when: >
        is_leader is defined and
        is_leader and
        reboot_required.stat.exists and
        inventory_hostname == parsed_patroni_member_names[0]

    - name: Move leader from second to third node
      ansible.builtin.command: >
        patronictl switchover --force
        --leader {{ parsed_patroni_member_names[1] }}
        --candidate {{ parsed_patroni_member_names[2] }}
      changed_when: false
      when: >
        is_leader is defined and
        is_leader and
        reboot_required.stat.exists
        and inventory_hostname == parsed_patroni_member_names[1]

    - name: Move leader from third to first node
      ansible.builtin.command: >
        patronictl switchover --force
        --leader {{ parsed_patroni_member_names[2] }}
        --candidate {{ parsed_patroni_member_names[0] }}
      changed_when: false
      when: >
        is_leader is defined and
        is_leader and
        reboot_required.stat.exists
        and inventory_hostname == parsed_patroni_member_names[2]

    - name: Wait for the current node to become a replica and not be in 'stopped' state
      ansible.builtin.command: patronictl list --format json
      register: patroni_replica_check
      retries: 120
      delay: 10
      until:
        - patroni_replica_check is succeeded
        - >
          {{
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', ansible_hostname)
            | map(attribute='Role')
            | first
            == 'Replica'
          }}
        - >
          {{
            (patroni_replica_check.stdout | from_json)
            | selectattr('Member', 'equalto', ansible_hostname)
            | map(attribute='State')
            | first
            == 'streaming'
          }}
      changed_when: false
      when: is_leader is defined and is_leader and reboot_required.stat.exists

    - name: Change nofailover tag to second node
      ansible.builtin.lineinfile:
        path: /etc/patroni/postgres.yml
        regexp: '^    nofailover: false$'
        line: '    nofailover: true'
        backrefs: true
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[2]
      delegate_to: "{{ parsed_patroni_member_names[1] }}"

    - name: Change nofailover tag to third node
      ansible.builtin.lineinfile:
        path: /etc/patroni/postgres.yml
        regexp: '^    nofailover: false$'
        line: '    nofailover: true'
        backrefs: true
      when:
        - nofailover is defined and nofailover
        - reboot_required.stat.exists
        - inventory_hostname != parsed_patroni_member_names[1]
      delegate_to: "{{ parsed_patroni_member_names[2] }}"

    - name: Pause patroni
      ansible.builtin.command: patronictl pause
      changed_when: false
      when: reboot_required.stat.exists

    - name: Get pid of postgres process
      ansible.builtin.command: head -n 1 {{ pgcluster_path_data_psql }}/postmaster.pid
      register: postgres_pid
      changed_when: false
      when: reboot_required.stat.exists

    - name: Kill postgres process
      ansible.builtin.command: kill -INT {{ postgres_pid.stdout }}
      changed_when: false
      when: reboot_required.stat.exists and postgres_pid.stdout is defined

    - name: Wait for postgres process to die
      ansible.builtin.wait_for:
        path: /proc/{{ postgres_pid.stdout }}
        state: absent
        timeout: 900
      changed_when: false
      when: reboot_required.stat.exists

    - name: Create maintenance for host in zabbix
      become: false
      community.zabbix.zabbix_maintenance:
        name: Update and reboot {{ ansible_hostname }}
        host_name: "{{ ansible_hostname }}"
        state: present
        minutes: 10
        collect_data: false
      vars:
        ansible_network_os: community.zabbix.zabbix
        ansible_connection: httpapi
        ansible_httpapi_port: 443
        ansible_httpapi_use_ssl: true
        ansible_httpapi_validate_certs: false
        ansible_host: "{{ zabbix_server_web }}"
        ansible_zabbix_url_path: ''
        ansible_user: "{{ zabbix_user_ansible }}"
        ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
      when: reboot_required.stat.exists

    - name: Reboot the server if required
      ansible.builtin.reboot:
        msg: "Reboot initiated by Ansible for security updates"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: uptime
      when: reboot_required.stat.exists

    - name: Wait for the server to come back
      ansible.builtin.wait_for_connection:
        delay: 30
        timeout: 300
      when: reboot_required.stat.exists

    - name: Patroni resume
      ansible.builtin.command: patronictl resume
      changed_when: false
      when: reboot_required.stat.exists

    - name: Wait to state be 'streaming' and Lag in MB be 0
      ansible.builtin.command: patronictl list --format json
      retries: 120
      delay: 10
      register: check_patroni_after_reboot
      until:
        - check_patroni_after_reboot is succeeded
        - >
          {{
            (check_patroni_after_reboot.stdout | from_json)
            | selectattr('Member', 'equalto', ansible_hostname)
            | map(attribute='State')
            | first
            == 'streaming'
          }}
        - >
          {{
            (check_patroni_after_reboot.stdout | from_json)
            | selectattr('Member', 'equalto', ansible_hostname)
            | map(attribute='Lag in MB')
            | first
            == 0
          }}
      changed_when: false
      when: reboot_required.stat.exists

    - name: Check in patroni config file if nofailover tag is true in /etc/patronit/postgres.yml
      ansible.builtin.command: cat /etc/patroni/postgres.yml
      register: patroni_nofailover_check
      changed_when: false
      when: reboot_required.stat.exists

    - name: Parse Patroni nofailover information
      ansible.builtin.set_fact:
        nofailover: "{{ patroni_nofailover_check.stdout_lines |
                        select('match', '^    nofailover: true$') |
                        list |
                        length > 0 }}"
      when: patroni_nofailover_check is defined and patroni_nofailover_check is succeeded and reboot_required.stat.exists

    - name: Debug
      ansible.builtin.debug:
        msg: "Current node has nofailover tag - {{ nofailover }}"
      when: patroni_nofailover_check is succeeded and reboot_required.stat.exists

    - name: If in config file have nofailover tag in true need to apply
      ansible.builtin.command: patronictl reload {{ parsed_patroni_cluster_name }} {{ inventory_hostname }} --force
      changed_when: false
      when: patroni_nofailover_check is defined and patroni_nofailover_check is succeeded and reboot_required.stat.exists

    - name: Remove maintenance for host in zabbix
      become: false
      community.zabbix.zabbix_maintenance:
        name: Update and reboot {{ ansible_hostname }}
        state: absent
      vars:
        ansible_network_os: community.zabbix.zabbix
        ansible_connection: httpapi
        ansible_httpapi_port: 443
        ansible_httpapi_use_ssl: true
        ansible_httpapi_validate_certs: false
        ansible_host: "{{ zabbix_server_web }}"
        ansible_zabbix_url_path: ''
        ansible_user: "{{ zabbix_user_ansible }}"
        ansible_httpapi_pass: "{{ zabbix_user_ansible_password }}"
      when: reboot_required.stat.exists
