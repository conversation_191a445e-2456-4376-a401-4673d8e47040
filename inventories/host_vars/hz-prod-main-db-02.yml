---
percona_cluster_mysql_server_id: "2"
percona_cluster_mysql_version_release: "37-29-1"
percona_cluster_mysql_keepalived_version: 1:2.2.8-1ppa1ubuntu20.04
percona_cluster_mysql_audit_log_enabled: false

percona_cluster_mysql_gtid_mode: "ON"
percona_cluster_mysql_enforce_gtid_consistency: "ON"
percona_cluster_mysql_default_authentication_plugin: "caching_sha2_password"
percona_cluster_mysql_key_buffer: "16M"
percona_cluster_mysql_max_allowed_packet: "256M"
percona_cluster_mysql_max_connections: "50000"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "0"
percona_cluster_mysql_innodb_buffer_pool_size: "8G"
percona_cluster_mysql_innodb_log_file_size: "1G"

# Backup
maindb_aws_s3_bucket: s3://prod-backup-dbs

bareos_backup_task_custom_backup_job:
  - name: Mysql-Full-auth
    client: "{{ inventory_hostname }}"
    schedule: Nightly_every_Day_at_12
    fileset: mysql
    pool: Full
    runafter:
      "sh -c 'find /backup/dump/auth -maxdepth 1 -type f -mtime +30 -delete; \
      /bin/mysqldump  --single-transaction --skip-lock-tables --flush-logs --master-data=2 \
      --triggers --routines --events auth | \
      zstd -o /backup/dump/auth/dump-auth-%d-`date +%Y_%%m_%%d-%H_%M`.sql.zstd'"
  - name: Mysql-Full-repricer_main_db
    client: "{{ inventory_hostname }}"
    schedule: Nightly_every_Day_at_12
    fileset: mysql
    pool: Full
    runafter:
      "sh -c 'find /backup/dump/repricer -maxdepth 1 -type f -mtime +30 -delete; \
      /bin/mysqldump  --single-transaction --skip-lock-tables --flush-logs --master-data=2 \
      --triggers --routines --events repricer_main_db | \
      zstd -o /backup/dump/repricer/dump-repricer_main_db-%d-`date +%Y_%%m_%%d-%H_%M`.sql.zstd'"
  - name: Mysql-Full-token_service
    client: "{{ inventory_hostname }}"
    schedule: Nightly_every_Day_at_12
    fileset: mysql
    pool: Full
    runafter:
      "sh -c 'find /backup/dump/token-service -maxdepth 1 -type f -mtime +30 -delete; \
      /bin/mysqldump  --single-transaction --skip-lock-tables --flush-logs --master-data=2 \
      --triggers --routines --events token_service | \
      zstd -o /backup/dump/token-service/dump-token_service-%d-`date +%Y_%%m_%%d-%H_%M`.sql.zstd'"
  - name: S3
    client: "Bareos-S3"
    schedule: Nightly_every_Day
    fileset: mysql
    pool: Full
    runafter:
      "sh -c 'aws s3 cp /var/lib/bareos/storage/{{ inventory_hostname }}/dump/auth/\
      $(ls -t /var/lib/bareos/storage/{{ inventory_hostname }}/dump/auth/ | head -1) \
      {{ maindb_aws_s3_bucket }}/{{ inventory_hostname }}/auth/ ; \
      aws s3 cp /var/lib/bareos/storage/{{ inventory_hostname }}/dump/token-service/\
      $(ls -t /var/lib/bareos/storage/{{ inventory_hostname }}/dump/token-service/ | head -1) \
      {{ maindb_aws_s3_bucket }}/{{ inventory_hostname }}/token-service/ ; \
      aws s3 cp /var/lib/bareos/storage/{{ inventory_hostname }}/dump/repricer/\
      $(ls -t /var/lib/bareos/storage/{{ inventory_hostname }}/dump/repricer/ | head -1) \
      {{ maindb_aws_s3_bucket }}/{{ inventory_hostname }}/repricer/'"

mysql_additional_disk: true
