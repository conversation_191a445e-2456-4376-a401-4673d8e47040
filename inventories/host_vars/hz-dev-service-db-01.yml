---
percona_cluster_mysql_server_id: "1"
mysql_additional_disk: true
mysql_databases:
  - name: "service_db"
    collation: "utf8_general_ci"
    encoding: "utf8"

mysql_users:
  - name: "sellerlogic_dev"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37346633393535383639383035663537333436353534386561363934356236346637353137613735
          3033633265323861303263376534666665363133323237660a663239303336346337343065633431
          65383838363765313836616165373537653065653964323363623838393331323033376464386434
          6263623137363366630a323639343961353231376265663961333435306265373034636233633031
          36303236663964323239356463633138386161313932666332363533303234396361
    priv: "*.*:ALL,GRA<PERSON>"
    host: "%"
  - name: "prom_user"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35353934383831316334326365306533393630346233343137336463353637653131646232316530
          3566646463393732396539633935323437643637376436650a663439386561663962306434616437
          36353463333732393738353061383335633466333064626131626432303933626262656238383233
          6134393665383065300a333736373833303437303765393530383066313964373735623466633634
          3664
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "o.shtymak"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35623965376661306632393636373936343262333366663066663238393830613866633565326231
          3136336265393761643130316365343131613332303239660a303031363736633134333032396464
          37323136323533333832393365666465316130356235393033396430393035623766623361386538
          6166633832626130620a383366333439646166343037613632333536356632653935663436333534
          6433
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "o.sediva"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35623965376661306632393636373936343262333366663066663238393830613866633565326231
          3136336265393761643130316365343131613332303239660a303031363736633134333032396464
          37323136323533333832393365666465316130356235393033396430393035623766623361386538
          6166633832626130620a383366333439646166343037613632333536356632653935663436333534
          6433
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "o.zhgut"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35623965376661306632393636373936343262333366663066663238393830613866633565326231
          3136336265393761643130316365343131613332303239660a303031363736633134333032396464
          37323136323533333832393365666465316130356235393033396430393035623766623361386538
          6166633832626130620a383366333439646166343037613632333536356632653935663436333534
          6433
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "d.ivanenko"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35623965376661306632393636373936343262333366663066663238393830613866633565326231
          3136336265393761643130316365343131613332303239660a303031363736633134333032396464
          37323136323533333832393365666465316130356235393033396430393035623766623361386538
          6166633832626130620a383366333439646166343037613632333536356632653935663436333534
          6433
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "v.svyryd"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35623965376661306632393636373936343262333366663066663238393830613866633565326231
          3136336265393761643130316365343131613332303239660a303031363736633134333032396464
          37323136323533333832393365666465316130356235393033396430393035623766623361386538
          6166633832626130620a383366333439646166343037613632333536356632653935663436333534
          6433
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "i.martynyuk"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35623965376661306632393636373936343262333366663066663238393830613866633565326231
          3136336265393761643130316365343131613332303239660a303031363736633134333032396464
          37323136323533333832393365666465316130356235393033396430393035623766623361386538
          6166633832626130620a383366333439646166343037613632333536356632653935663436333534
          6433
    priv: "*.*:ALL,GRANT"
    host: "%"

mysql_prom_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63643264626464303763383131353036616238303835613435306436326363656539623031303331
          6266636535623930623964653630623335366366623939310a653366316162613735383130303938
          36363332303237646265613837663965306338646435633732623230303330653237326235373162
          3230636139326634320a396238646539376164366138626363306130396135383439623635356235
          3634

# Version to install, defaulting to 5.6
percona_cluster_mysql_version_major: "8"
percona_cluster_mysql_version_minor: "0"
percona_cluster_mysql_version: "{{ percona_cluster_mysql_version_major | int }}.{{ percona_cluster_mysql_version_minor | int }}"

# Basic settings
mysql_root_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          30373638666230633539393131376265346432303663396662616164653137366365333261663735
          3835643065343835363133393238376337613663653739660a376639396336323261616266636661
          65353432613037326430366466616565626435663262653937643635626137396433333866313265
          6563616563356236620a653164633332623639663530343462613939666532653738386365356166
          6239
percona_cluster_mysql_port: "3306"
percona_cluster_mysql_bind_address: "0.0.0.0"
percona_cluster_mysql_datadir: "/data/mysql"

# Fine tuning
percona_cluster_mysql_open_files_limit: "100000"
percona_cluster_mysql_key_buffer: "16M"
percona_cluster_mysql_max_allowed_packet: "256M"
percona_cluster_mysql_thread_stack: "192K"
percona_cluster_mysql_cache_size: "8"
percona_cluster_mysql_max_connections: "50000"
percona_cluster_mysql_table_cache: "100000"
percona_cluster_mysql_table_definition_cache: "50000"
percona_cluster_mysql_innodb_open_files: "100000"
percona_cluster_mysql_max_heap_table_size: "16M"
percona_cluster_mysql_thread_concurrency: "10"
percona_cluster_mysql_query_cache_limit: "1M"
percona_cluster_mysql_query_cache_size: "16M"
percona_cluster_mysql_character_set_server: "utf8"
percona_cluster_mysql_collation_server: "utf8_general_ci"
percona_cluster_mysql_mysqldump_max_allowed_packet: "128M"
percona_cluster_mysql_isamchk_key_buffer: "16M"
percona_cluster_mysql_sort_buffer_size: "256K"

# InnoDB tuning
percona_cluster_mysql_innodb_file_per_table: "1"
percona_cluster_mysql_innodb_flush_method: "fdatasync"
percona_cluster_mysql_innodb_buffer_pool_size: "3G"
percona_cluster_mysql_innodb_read_io_threads: "8"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "0"
percona_cluster_mysql_innodb_lock_wait_timeout: "50"
percona_cluster_mysql_innodb_log_buffer_size: "16M"
percona_cluster_mysql_innodb_log_file_size: "1G"

percona_cluster_mysql_character_set_client_handshake: "FALSE"

percona_cluster_mysql_timezone_info: "false"

install_rpm_repositories: "true"

# To disable log_bin in percona >=8, enabled by default
percona_cluster_mysql_disable_log_bin: "true"

# Default Auth Plugin
# used in templates when Percona Server >= 5.7
percona_cluster_mysql_default_authentication_plugin: "mysql_native_password"
