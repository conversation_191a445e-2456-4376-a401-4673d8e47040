---
# opnsense user owner of api key: ansible_automate_opnsense
opnsense_connect_api_key: MIJfA5TE7lUHyqTN14EZ8eYSoXhfx2Pr2BELM6FoLd6KH45JhsnpWdFchAPX+7ylCu2rRmds0bKqRTw/
opnsense_connect_api_secret: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37646331326566623861633438323866353334666131653833383632636530393233366339393264
          3234323439326333353933356531313063643137356165390a663136376330336231663465306632
          39636639633161613764613462393231303965373934326331613863616634306139323232616132
          6563663532646238640a313761623831636162316339393736373531323835663133663762333763
          32343832313661353761623536653236376238346135313631393333633737313263663034663762
          61383138636334333939346361333162656164353138363961363034316234623338633934663666
          63393464313839326339636133303838336266666138373030633938643764636365353663383834
          33316334383465316437643263623934393438336264643863646162623864636466343438626131
          6165

opnsense_firewall_aliases_user:
  - ldap_back_devs_bas
  - ldap_back_devs_bravo
  - ldap_back_devs_core
  - ldap_back_leads_bravo
  - ldap_back_leads_core
  - ldap_bas_devs_core
  - ldap_developer_bas
  - ldap_devops
  - ldap_prod_ticket_groups
  - ldap_qa_groups
  - ldap_spider_devs
  - ldap_windows10_acess
  - ldap_wordpress_devs
