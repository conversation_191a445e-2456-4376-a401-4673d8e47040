---
postgres_db_env: prod
postgres_cluster_name: qe
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34623562323232333562613133303931326364653139636435623936646334323138353633353361
          3837346666356562373565343138653734653939313231610a393037653633663866356462353737
          37303836663637323965353265653865323664326465343237386634653162353265316565386234
          3635343265643862660a386638663462373530373639373963356234383738666631303732366530
          3063
postgres_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          61656363306639626332363034613237666134326432656662353237343761623566633238666430
          6363396237303635633539633338393132363861643366610a316330623737656636616637396161
          62336562396533336436633433356633643561376333396565666661366438643363376463396564
          6563386133343332370a373636343563383437303832613733666236656433636366306362666439
          6265
pgstandalone_postgres_shared_buffer: 8GB
postgres_max_connections: 3300
postgres_max_wal_senders: 0
postgres_wal_level: minimal
postgres_synchronous_commit: true
postgres_checkpoint_timeout: 30min
postgres_checkpoint_warning: 10min
pgstandalone_postgres_max_wal_size: 35GB
pgstandalone_postgres_min_wal_size: 10GB
pgstandalone_pgcluster_pgbouncer_max_client_conn: 3000
pgstandalone_pgcluster_pgbouncer_default_pool_size: 3000
pgstandalone_pgcluster_pgbouncer_server_idle_timeout: 30
