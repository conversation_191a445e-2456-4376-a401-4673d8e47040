---
percona_cluster_mysql_server_id: "1"
mysql_additional_disk: true
mysql_databases:
  - name: "service_db"
    collation: "utf8_general_ci"
    encoding: "utf8"
  - name: "api"
    collation: "utf8_general_ci"
    encoding: "utf8"
  - name: "auth"
    collation: "utf8_general_ci"
    encoding: "utf8"
  - name: "repricer_main_db"
    collation: "utf8_general_ci"
    encoding: "utf8"
  - name: "token_service"
    collation: "utf8_general_ci"
    encoding: "utf8"

mysql_users:
  - name: "prom_user"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63643264626464303763383131353036616238303835613435306436326363656539623031303331
          6266636535623930623964653630623335366366623939310a653366316162613735383130303938
          36363332303237646265613837663965306338646435633732623230303330653237326235373162
          3230636139326634320a396238646539376164366138626363306130396135383439623635356235
          3634
    priv: "*.*:ALL,GRANT"
    host: "%"

mysql_prom_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63643264626464303763383131353036616238303835613435306436326363656539623031303331
          6266636535623930623964653630623335366366623939310a653366316162613735383130303938
          36363332303237646265613837663965306338646435633732623230303330653237326235373162
          3230636139326634320a396238646539376164366138626363306130396135383439623635356235
          3634

# Version to install, defaulting to 5.6
percona_cluster_mysql_version_major: "8"
percona_cluster_mysql_version_minor: "0"
percona_cluster_mysql_version: "{{ percona_cluster_mysql_version_major | int }}.{{ percona_cluster_mysql_version_minor | int }}"

# Basic settings
mysql_root_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35363536343632333739303461376565626638343431636530636463643130653636336563336631
          6136653766633834626636373466386132313633363263370a323738383566383635333734386234
          34323537613734313662316634653265636435353038373064346366633237383965393630306565
          6163656465383466330a613132373637373533623238386266636139356464633065396665396338
          3863

percona_cluster_mysql_port: "3306"
percona_cluster_mysql_bind_address: "0.0.0.0"
percona_cluster_mysql_datadir: "/data/mysql"

# Fine tuning
percona_cluster_mysql_open_files_limit: "16384"
percona_cluster_mysql_key_buffer: "16M"
percona_cluster_mysql_max_allowed_packet: "512M"
percona_cluster_mysql_thread_stack: "192K"
percona_cluster_mysql_cache_size: "512"
percona_cluster_mysql_max_connections: "500"
percona_cluster_mysql_table_cache: "1024"
percona_cluster_mysql_table_definition_cache: "500000"
percona_cluster_mysql_innodb_open_files: "1058576"
percona_cluster_mysql_max_heap_table_size: "54M"
percona_cluster_mysql_thread_concurrency: "10"
percona_cluster_mysql_query_cache_limit: "1M"
percona_cluster_mysql_query_cache_size: "16M"
percona_cluster_mysql_character_set_server: "utf8"
percona_cluster_mysql_collation_server: "utf8_general_ci"
percona_cluster_mysql_mysqldump_max_allowed_packet: "128M"
percona_cluster_mysql_isamchk_key_buffer: "16M"
percona_cluster_mysql_sort_buffer_size: "256K"

# InnoDB tuning
percona_cluster_mysql_innodb_file_per_table: "1"
percona_cluster_mysql_innodb_flush_method: "fdatasync"
percona_cluster_mysql_innodb_buffer_pool_size: "4096M"
percona_cluster_mysql_innodb_read_io_threads: "8"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "0"
percona_cluster_mysql_innodb_lock_wait_timeout: "50"
percona_cluster_mysql_innodb_log_buffer_size: "32M"
percona_cluster_mysql_innodb_log_file_size: "256M"

percona_cluster_mysql_character_set_client_handshake: "FALSE"

percona_cluster_mysql_timezone_info: "false"

install_rpm_repositories: "true"

# To disable log_bin in percona >=8, enabled by default
percona_cluster_mysql_disable_log_bin: "true"

# Default Auth Plugin
# used in templates when Percona Server >= 5.7
percona_cluster_mysql_default_authentication_plugin: "mysql_native_password"
