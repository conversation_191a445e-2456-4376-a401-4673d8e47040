[prod_bas_clickhouse]
hz-prod-bas-clickhouse-01 ansible_host=************
hz-prod-bas-clickhouse-02 ansible_host=************
hz-prod-bas-clickhouse-03 ansible_host=************
hz-prod-bas-clickhouse-04 ansible_host=************

[stage_bas_clickhouse]
hz-stage-bas-clickhouse-01 ansible_host=*************
hz-stage-bas-clickhouse-02 ansible_host=*************
hz-stage-bas-clickhouse-03 ansible_host=*************
hz-stage-bas-clickhouse-04 ansible_host=*************

[rc_bas_clickhouse]
hz-rc-bas-clickhouse-01 ansible_host=************
hz-rc-bas-clickhouse-02 ansible_host=************
hz-rc-bas-clickhouse-03 ansible_host=************
hz-rc-bas-clickhouse-04 ansible_host=************

[prod_rabbitmq]
hz-prod-rabbitmq-01 ansible_host=************
hz-prod-rabbitmq-02 ansible_host=************
hz-prod-rabbitmq-03 ansible_host=************

[rc_rabbitmq]
hz-rc-rabbitmq-01 ansible_host=************
hz-rc-rabbitmq-02 ansible_host=************
hz-rc-rabbitmq-03 ansible_host=************

[stage_rabbitmq]
hz-stage-rabbitmq-01 ansible_host=************
hz-stage-rabbitmq-02 ansible_host=************
hz-stage-rabbitmq-03 ansible_host=************

[prod_ticket_front]
hz-prod-ticket-front-01 ansible_host=************
hz-prod-ticket-front-02 ansible_host=************
[hw_services_mysql_db]
hz-prod-service-db-01 ansible_host=************* ansible_port=2202
hz-prod-service-db-02 ansible_host=************ ansible_port=2202

[services_mysql_db]
hz-dev-service-db-01 ansible_host=************ ansible_port=2202

[test_services_mysql_db]
hz-test-stage-service-db-01 ansible_host=************ ansible_port=2202
hz-test-prod-service-db-01 ansible_host=************ ansible_port=2202


[zabbix_proxy]
hz-infra-zabbix-proxy-01 ansible_host=************* ansible_port=2202

[openstack:children]
openstack_nodes
openstack_control

[openstack_nodes]
DE010101.sellerlogic.com ansible_host=************
DE010102.sellerlogic.com ansible_host=************
DE010103.sellerlogic.com ansible_host=************
DE010104.sellerlogic.com ansible_host=************

[openstack:children]
openstack_nodes
openstack_control

[openstack_control]
de010109.sellerlogic.com ansible_host=*************
de010110.sellerlogic.com ansible_host=*************
de010111.sellerlogic.com ansible_host=*************

[spider:children]
spider_grabber
spider_parser
spider_db_prod
spider_rabbitmq_cluster_prod

[spider_grabber]
ovh-spider-grabber-01 ansible_host=************* ansible_port=22
ovh-spider-grabber-02 ansible_host=************ ansible_port=22

[spider_rabbitmq_cluster_prod]

hz-spider-rabbitmq-01  ansible_host=************ ansible_port=2202
hz-spider-rabbitmq-02  ansible_host=************ ansible_port=2202
hz-spider-rabbitmq-03  ansible_host=************ ansible_port=2202

[spider_parser]
hz-spider-parser-01  ansible_host=************ ansible_port=2202

[spider_redis]
hz-spider-node-01  ansible_host=************ ansible_port=2202

[spider_db_prod]

hz-spider-db-01  ansible_host=************ ansible_port=2202
hz-spider-db-02  ansible_host=************ ansible_port=2202
hz-spider-db-03  ansible_host=************ ansible_port=2202

[patroni_spider_db_prod]
hz-spider-db-01  ansible_host=************ ansible_port=2202
hz-spider-db-02  ansible_host=************ ansible_port=2202
hz-spider-db-03  ansible_host=************ ansible_port=2202

[patroni_consul_spider_db_prod]
hz-spider-db-01  ansible_host=************ ansible_port=2202
hz-spider-db-02  ansible_host=************ ansible_port=2202
hz-spider-db-03  ansible_host=************ ansible_port=2202

[patroni_haproxy_spider_db_prod:children]
patroni_spider_db_prod

[patroni_keepalived_spider_db_prod]
hz-spider-db-01  ansible_host=************ ansible_port=2202 priority_num=100
hz-spider-db-02  ansible_host=************ ansible_port=2202 priority_num=200
hz-spider-db-03  ansible_host=************ ansible_port=2202 priority_num=300


[spider_rabbitmq_cluster_stage]
[spider_rabbitmq_cluster_dev]

[vaultwarden_old]
hz-vaultwarden-01  ansible_host=************ ansible_port=2202

[prod_service_web]
prod-service-web1 ansible_host=*************
prod-service-web2 ansible_host=*************
prod-service-web3 ansible_host=*************
prod-service-web4 ansible_host=*************
prod-service-web5 ansible_host=*************
prod-service-web6 ansible_host=*************
prod-service-web7 ansible_host=*************
prod-service-web8 ansible_host=*************
prod-service-web9 ansible_host=*************
prod-service-web10 ansible_host=*************

[dev_service_web]
dev-service-web-1 ansible_host=************* ansible_python_interpreter=/usr/bin/python

[rc_service_web]
service.sellerlogic.dev ansible_host=************* ansible_port=22

[service_web:children]
prod_service_web
dev_service_web
rc_service_web

[redis]
redis-cluster-1 ansible_host=*************
redis-cluster-2 ansible_host=*************
redis-cluster-3 ansible_host=*************
hz-prod-service-redis-node-01 ansible_host=************
hz-prod-service-redis-node-02 ansible_host=*************
hz-prod-service-redis-node-03 ansible_host=*************
hz-prod-service-redis-node-04 ansible_host=************

[proxmox]
#sl-node-a ansible_host=***********  ansible_port=8899
#sl-node-b ansible_host=************ ansible_port=8899
hz-i-prox-node-01 ansible_host=************ ansible_port=22
hz-i-prox-node-02 ansible_host=************ ansible_port=22
hz-i-prox-node-03 ansible_host=*********** ansible_port=22
hz-a-prox-node-04 ansible_host=************ ansible_port=22
hz-a-prox-node-05 ansible_host=************ ansible_port=22
hz-a-prox-node-06 ansible_host=************ ansible_port=22
hz-a-prox-node-07 ansible_host=************ ansible_port=22

[mysql_databases]
iops-maria1046 ansible_host=************

[dev_repricer]
react.sellerlogic.dev ansible_host=*************
auth.sellerlogic.dev ansible_host=*************
restapi.sellerlogic.dev ansible_host=*************
service.sellerlogic.dev ansible_host=*************

[stage_repricer]
app.sellerlogic.dev ansible_host=************* 
auth-staging.sellerlogic.dev ansible_host=*************
restapi-staging.sellerlogic.dev ansible_host=*************

[rc_repricer]
app-rc.sellerlogic.dev ansible_host=************* ansible_port=22
auth-rc.sellerlogic.dev ansible_host=************* ansible_port=22
restapi-rc.sellerlogic.dev ansible_host=************* ansible_port=22
service.sellerlogic.dev ansible_host=************* ansible_port=22

[prod_repricer]
app.sellerlogic.com ansible_host=************7
auth.sellerlogic.com ansible_host=************5
restapi.sellerlogic.com ansible_host=************5
service.sellerlogic.com ansible_host=*************
[rc_repricer_db]
rc-mysql ansible_host=192.168.2.227 ansible_port=22

[stage_repricer_db]
stage-mysql ansible_host=*************

[dev_repricer_db]
dev-mysql ansible_host=192.168.2.6

[rc_postgresql_db]
hz-rc-bas-node-01 ansible_host=192.168.3.83 ansible_python_interpreter=/usr/bin/python3

[rc_clickhouse_db]
hz-rc-bas-node-01 ansible_host=192.168.3.83 ansible_python_interpreter=/usr/bin/python3

[dev_postgresql_db]
hz-dev-bas-node-01 ansible_host=192.168.3.236 ansible_python_interpreter=/usr/bin/python3

[dev_clickhouse_db]
hz-dev-bas-node-01 ansible_host=192.168.3.236 ansible_python_interpreter=/usr/bin/python3

[dev_rabbitmq]
api-stage-rabbitmq ansible_host=*************

[dev_bas_api]
hz-dev-bas-node-01 ansible_host=192.168.3.236 ansible_python_interpreter=/usr/bin/python3

[stage_rabbitmq_old]
api-stage-rabbitmq ansible_host=192.168.2.216

[rc_bas_api]
hz-rc-bas-node-01 ansible_host=192.168.3.83 ansible_python_interpreter=/usr/bin/python3

[repricer:children]
rc_bas_api
stage_rabbitmq_old
dev_bas_api
dev_rabbitmq
dev_postgresql_db
dev_clickhouse_db
rc_postgresql_db
rc_clickhouse_db
dev_repricer_db
stage_repricer_db
rc_repricer_db
dev_repricer
stage_repricer
rc_repricer
prod_repricer

[repricer_db]
repricer-mysql-db-01 ansible_host=192.168.2.46 ansible_port=22
repricer-mysql-db-02 ansible_host=192.168.2.47 ansible_port=22
[crm:children]
dev_crm
stage_crm
stage_app_crm
stage_db_crm
[dev_crm]
hz-dev-crm-back-01 ansible_host=************9
crm-backend-lb ansible_host=*************
[stage_crm:children]
stage_db_crm
stage_app_crm
[stage_app_crm]
hz-stage-crm-front-01 ansible_host=192.168.3.22 ansible_port=22
hz-stage-crm-back-01 ansible_host=192.168.2.219 ansible_port=22

[stage_db_crm]
crm-postgres-cluster-1 ansible_host=192.168.2.223 ansible_port=22
crm-postgres-cluster-2 ansible_host=192.168.2.200  ansible_port=22
crm-postgres-cluster-3 ansible_host=192.168.2.203  ansible_port=22
[dev_ticket]
hz-dev-ticket-front-01 ansible_host=192.168.3.88
hz-dev-ticket-back-01 ansible_host=192.168.3.219

[stage_ticket]
hz-stage-ticket-back-01 ansible_host=192.168.3.27 ansible_port=22
hz-stage-ticket-front-01 ansible_host=192.168.3.133 ansible_port=22

[repricer:vars]
ansible_python_interpreter=/usr/bin/python

[ticket:children]
dev_ticket
stage_ticket

[zabbix_server]
DE010112.sellerlogic.com ansible_host=192.168.2.225
[bastion]
border-internal-cluster ansible_host=*************

[maindb]
iops-maria1046 ansible_host=192.168.2.82
[prometheus_server]
prometheus ansible_host=192.168.2.83
[temp_db]
DE010106 ansible_host=************

[dev_rabbitmq_server]
dev-rabbitmq ansible_host=*************
[stage_mysql_db]
stage-mysql ansible_host=************* ansible_python_interpreter=/usr/bin/python

[proxy]
proxy-http ansible_host=************8 ansible_python_interpreter=/usr/bin/python ansible_port=22

[postgres_db]
DE010106 ansible_host=************

[spider]
spider-web ansible_host=************ ansible_port=22

[prod_gitlab]
gitlab ansible_host=192.168.2.55

[sites]
sl-sites-dev
sl-sites

[rc_opensearch]
hz-rc-ua-os-node-01 ansible_host=************9 ansible_port=2202
hz-rc-ua-os-node-02 ansible_host=192.168.2.130 ansible_port=2202
hz-rc-ua-os-node-03 ansible_host=192.168.2.131 ansible_port=2202

[openstack_vm]
redis-cluster-2 ansible_host=*************
prod-service-web1 ansible_host=*************
prod-service-web4 ansible_host=*************
prod-service-web2 ansible_host=*************
prod-service-web3 ansible_host=*************
prod-service-web5 ansible_host=*************
prod-service-web6 ansible_host=*************
prod-service-web7 ansible_host=*************
prod-service-web8 ansible_host=*************
# shutoff sl-webhost ansible_host=************
st-ticket-backend-01 ansible_host=************ ansible_port=22
ticket-staging-frontend ansible_host=************ ansible_port=22
# shutoff test_migrate ansible_host=***********
api-rc-rabbitmq ansible_host=*************
api-rc-elk ansible_host=*************
ticket-dev-frontend ansible_host=************ ansible_port=22
dev-ticket-backend ansible_host=************
token-storage-prod ansible_host=************ ansible_port=22
token-storage-dev ansible_host=************ ansible_port=22
crm-staging-frontend ansible_host=************ ansible_port=22
crm-staging-backend ansible_host=************ ansible_port=22
sl-sites-dev ansible_host=********** ansible_port=22
user-api-stage ansible_host=*********** ansible_port=22
rc-mysql ansible_host=***********2 ansible_port=22
registry ansible_host=***********
rc-service ansible_host=************ ansible_port=22
rc-app ansible_host=*********** ansible_port=22
rc-restapi ansible_host=***********3 ansible_port=22
# appliance opnsense ansible_host=*************
pmm ansible_host=************ ansible_port=22
sl-sites ansible_host=************
clickhouse ansible_host=************ ansible_port=22
user-api-elk ansible_host=************* ansible_port=22
zookeeper-cluster-3 ansible_host=************ ansible_port=22
zookeeper-cluster-1 ansible_host=************ ansible_port=22
zookeeper-cluster-2 ansible_host=*********** ansible_port=22
user-api-prod ansible_host=*********** ansible_port=22
redis-cluster-3 ansible_host=*************
redis-cluster-1 ansible_host=*************
proxy-http ansible_host=************** ansible_port=22
dev-client-web2 ansible_host=************* ansible_python_interpreter=/usr/bin/python
dev-client-web1 ansible_host=************* ansible_python_interpreter=/usr/bin/python
staging-mysql ansible_host=************* ansible_python_interpreter=/usr/bin/python
staging-react ansible_host=************* ansible_python_interpreter=/usr/bin/python
staging-restapi ansible_host=************* ansible_python_interpreter=/usr/bin/python
reactdev ansible_host=************* ansible_python_interpreter=/usr/bin/python
restapi-and-auth-dev ansible_host=************* ansible_python_interpreter=/usr/bin/python
dev-elk ansible_host=************* ansible_python_interpreter=/usr/bin/python
dev-rabbitmq ansible_host=*************
user-api-dev ansible_host=************ ansible_port=22
db-backup ansible_host=************ ansible_port=22
spider-postgres-cluster--3 ansible_host=************ ansible_port=22
spider-postgres-cluster--1 ansible_host=*********** ansible_port=22
spider-elk ansible_host=************ ansible_port=22
service-rabbitmq ansible_host=************** ansible_port=22
spider-web ansible_host=************ ansible_port=22
api-staging-elk ansible_host=************
api-staging-rabbitmq ansible_host=************
crm-backend-lb ansible_host=*************
border-internal-cluster ansible_host=*************
consul-3 ansible_host=*********** ansible_port=22
consul-2 ansible_host=*********** ansible_port=22
consul-1 ansible_host=********** ansible_port=22
mysql-slavedb-prod ansible_host=************
crm-postgres-cluster-2 ansible_host=************ ansible_port=22
crm-postgres-cluster-3 ansible_host=************ ansible_port=22
crm-postgres-cluster-1 ansible_host=*********** ansible_port=22

[freeipa_server]
hz-prod-ldap-01 ansible_host=************

[freeipa_replica]
hz-prod-ldap-02 ansible_host=************

[vaultwarden]
hz-prod-vaultwarden-01  ansible_host=************ ansible_port=2202

[prod_infra_pgcluster]
hz-prod-infra-db-01 ansible_host=************ ansible_port=2202 priority_num=100
hz-prod-infra-db-02 ansible_host=************ ansible_port=2202 priority_num=200
hz-prod-infra-db-03 ansible_host=************ ansible_port=2202 priority_num=300

[keycloak_cluster]
hz-prod-keycloak-01 ansible_host=************ ansible_port=2202
hz-prod-keycloak-02 ansible_host=************ ansible_port=2202
