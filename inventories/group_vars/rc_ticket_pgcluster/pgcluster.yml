---
postgres_db_env: rc
postgres_cluster_name: ticket
vip_address: ************
vip_address_ro: ************
vrouter_id: 194
vrouter_id_ro: 202
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          32653763366332346566373461363435613731373362373034343266323838383061313361323466
          3465373762376366633237626539663237323031626630330a323631626466376265356438333762
          31633165616662306633373935333865313039666339363637306437646565383239316132333265
          3430626464656237370a316539663862353061626231653534323938313663396232303765613434
          32656565613962663566363231626665616133363264313235613066373938313461
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
