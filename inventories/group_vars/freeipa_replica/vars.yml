---
force_dns_ext: true
freeipa_srv: true

crone_backup:

  - name: freeipa-backup-daily
    cron_file: freeipa-backup-daily
    minute: 00
    hour: 22
    day: "*"
    month: "*"
    weekday: "0-6"
    user: root
    job: "docker exec freeipa-replica ipa-backup -v"

  - name: freeipa-backup-copy
    cron_file: freeipa-backup-copy
    minute: 30
    hour: 22
    day: "*"
    month: "*"
    weekday: "0-6"
    user: root
    job: "cp -rf /app/ipadata/var/lib/ipa/backup/ipa-full-* /backup"
