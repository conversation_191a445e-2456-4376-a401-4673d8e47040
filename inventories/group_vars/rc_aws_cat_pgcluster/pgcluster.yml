---
postgres_db_env: rc
postgres_cluster_name: aws_cat
vip_address: *************
vip_address_ro: *************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37646164366362613335386331363634633139653039643936663631386631653563633530343330
          3333353836636166633336396436333065383261373930300a353038336439306534323038613633
          65613032653935326635346637363731373430366536666435663365643632376236363566646264
          6162636465663536380a393632623164633036343966363039333137623163653361633266346434
          62336136663266653165393730396139333562366162346635653431366161623133
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
