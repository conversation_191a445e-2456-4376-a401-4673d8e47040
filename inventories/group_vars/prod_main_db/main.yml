---
mysql_databases:
  - name: "auth"
    collation: "utf8_general_ci"
    encoding: "utf8"
  - name: "repricer_main_db"
    collation: "utf8_general_ci"
    encoding: "utf8"
  - name: "token_service"
    collation: "latin1_swedish_ci"
    encoding: "latin1"

mysql_users:
  - name: "{{ pmm_agent_mysql_user }}"
    pass: "{{ pmm_agent_mysql_user_password }}"
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "sellerlogic_prod"
    pass: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      64623136316166353132653362643730393934373264313833656234313832363264663134616662
      6464333730343936656233653032333935303134373437630a643863373438643738346430383333
      66353263343032386438333638383965373836316265666164323838613537633537636336623864
      3234663936393935340a323836313764343935623664623930663563333339323737633264663430
      66646535633339613463313865363561393836313566623639323133363964376664
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "prom_user"
    pass: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      37303934656231313932346431366334323036376439316335633032653064663236616237616563
      3762313864636161346565386165643863653936353834350a653735626537376636613136656663
      35356432326462393233323264613933373737376230666436656530343266613765373664343539
      6137356163623430660a376136373234616366656638303366386637646636323938623261336639
      3635
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "token_user"
    pass: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      62653939633331333931393633646366353464363031306531623136333262333836356130343931
      3838623766393664626335343231626564323135356663640a626433306237313962356130303130
      64303039303965333932356665333166623933326339306339343061663435376438303734666365
      6165643136303763610a373236643265646363366661303534353265373063323134323463333034
      64343465643034613633386137646561373262323530653737386333626238323265
    priv: "*.*:ALL,GRANT"
    host: "%"

mysql_prom_user_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  63643264626464303763383131353036616238303835613435306436326363656539623031303331
  6266636535623930623964653630623335366366623939310a653366316162613735383130303938
  36363332303237646265613837663965306338646435633732623230303330653237326235373162
  3230636139326634320a396238646539376164366138626363306130396135383439623635356235
  3634

# Version to install, defaulting to 5.6
percona_cluster_mysql_version_major: "8"
percona_cluster_mysql_version_minor: "0"
percona_cluster_mysql_version: "{{ percona_cluster_mysql_version_major | int }}.{{ percona_cluster_mysql_version_minor | int }}"
# Basic settings
mysql_root_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37306362663837316161386165613036363265323333376363396230616636633262363261663537
          6238626536653465373061666233313631396565383266620a636434333230343937663762393336
          36623265656235326262393332666362613231383464383639333835663465303563636138396631
          6233633861646430360a363765333932363764313436333766653866373565643333376634363361
          3433

percona_cluster_mysql_port: "3306"
percona_cluster_mysql_bind_address: "0.0.0.0"
percona_cluster_mysql_language: "/usr/share/mysql/"
percona_cluster_mysql_datadir: "/data/mysql"
percona_cluster_mysql_open_files_limit: "100000"
percona_cluster_mysql_log_error_verbosity: "3"
# Fine tuning
percona_cluster_mysql_key_buffer: "0"
percona_cluster_mysql_max_allowed_packet: "512M"
percona_cluster_mysql_thread_stack: "192K"
percona_cluster_mysql_cache_size: "8"
percona_cluster_mysql_myisam_recover: "BACKUP"
percona_cluster_mysql_max_connections: "10240"
percona_cluster_mysql_table_cache: "100000"
percona_cluster_mysql_thread_concurrency: "10"
percona_cluster_mysql_query_cache_limit: "1M"
percona_cluster_mysql_query_cache_size: "16M"
percona_cluster_mysql_character_set_server: "utf8"
percona_cluster_mysql_collation_server: "utf8_general_ci"
percona_cluster_mysql_mysqldump_max_allowed_packet: "128M"
percona_cluster_mysql_isamchk_key_buffer: "16M"
percona_cluster_mysql_sort_buffer_size: "256K"
percona_cluster_mysql_max_heap_table_size: "2G"
percona_cluster_mysql_table_definition_cache: "50000"
percona_cluster_mysql_log_bin_directory: /data/logs
percona_cluster_mysql_log_bin: "{{ percona_cluster_mysql_log_bin_directory }}/mysql-bin.log"
percona_cluster_mysql_sync_binlog: "10000"
percona_cluster_mysql_expire_logs_days: "10"
percona_cluster_mysql_max_binlog_size: "1G"

# InnoDB tuning
percona_cluster_mysql_innodb_file_per_table: "1"
percona_cluster_mysql_innodb_flush_method: "O_DIRECT"
percona_cluster_mysql_innodb_buffer_pool_size: "32G"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "1"
percona_cluster_mysql_innodb_lock_wait_timeout: "50"
percona_cluster_mysql_innodb_log_buffer_size: "16M"
percona_cluster_mysql_innodb_log_file_size: "4G"
percona_cluster_mysql_innodb_open_files: "100000"
percona_cluster_mysql_character_set_client_handshake: "FALSE"

percona_cluster_mysql_timezone_info: "false"

install_rpm_repositories: "true"

# To disable log_bin in percona >=8, enabled by default
percona_cluster_mysql_disable_log_bin: "false"

# Default Auth Plugin
# used in templates when Percona Server >= 5.7
percona_cluster_mysql_default_authentication_plugin: "mysql_native_password"

mysql_keepalived_vip: *************

percona_cluster_mysql_audit_log_enabled: true

percona_cluster_mysql_block_encryption_mode: "aes-256-cbc"
