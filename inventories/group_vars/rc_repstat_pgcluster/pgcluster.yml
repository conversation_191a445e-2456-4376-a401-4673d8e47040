---
postgres_db_env: rc
postgres_cluster_name: repstat
vip_address: *************
vip_address_ro: *************
vrouter_id: 247
vrouter_id_ro: 248
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          64383663356662616531373338306461326235376131383938376165303437323530646434373861
          3333643737646564316561666239663136316333653063350a353936306666393062653365643532
          32316436663736626339373938303339333935646266663261366538373364643637646462363435
          3262353032613037310a353936336430656163353933363331396462653036613166353539366663
          3636
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 1GB
pgcluster_pg_stat_monitor: true
pgcluster_effective_cache_size: 1GB
