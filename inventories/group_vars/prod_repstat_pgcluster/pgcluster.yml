---
postgres_db_env: prod
postgres_cluster_name: repstat
vip_address: ************
vip_address_ro: ************
vrouter_id: 26
vrouter_id_ro: 27
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62656530363466303730363461366662383336353037353236333138646538303930396362303131
          6566356563303932313163373230366535663861316564340a316537303630613938356565383434
          30306362373161656536393032623638343462336636383539393935353636643630353165346464
          3263663862376463660a626434346431356237333431313466643339383561666265353634646662
          3831
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 1GB
pgcluster_pg_stat_monitor: true
pgcluster_effective_cache_size: 1GB
