---
rabbitmq_cookie: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  30633731373764376432623264363461666633613930633465663939373361373735366563393634
  6563346432326632306637373135306439613838636531620a653833626334653134366432623966
  37323734336536623233363538316664353333333930636361383634653032306465646561643230
  6466666437353238650a393230643733383631653530323433396561343933313331343633353661
  35303534663565393861313035633332393164633666626530353936303162316136303236366637
  6334386334626266653037383939656462636634663538326438
# ldap_rabbitmq_env example: rc, dev, prod
ldap_rabbitmq_env: dev

# rabbitmq_external_vip: true
rabbitmq_keepalived_vip: ************
rabbitmq_keepalived_vip_mask: 32
# rabbitmq_keepalived_vip_brd:
rabbitmq_setup_ufw: true

rabbitmq_extra_vhosts:
  - name: dev-restapi
    state: present
    username: dev-restapi
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
  - name: dev-bas
    state: present
    username: rmq.dev.bas
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
