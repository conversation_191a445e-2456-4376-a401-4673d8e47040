---
postgres_db_env: prod
postgres_cluster_name: spider
vip_address: *************
vip_address_ro: *************
vrouter_id: 139
vrouter_id_ro: 140
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62346636663762616632306431653764303834353861363031396233343036623135313061616533
          6362333364323539383965663764343230346661376563620a656362396334373635323536613266
          31336666343332383866363332643838316530313663656431623163303833666639353937343164
          3663386565343437330a323663306364646638353662623234613862616463386233653234663830
          3735
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 2GB
