---
rabbitmq_cookie: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  64633436393864343831343939633738383236333165363939323561613963623364336533306132
  6133393833336238363566343639616433353232633938310a643237393162653937656566303961
  32613739396537623830663836623163303561393731353831633065316465376134316137633165
  3763373332323931610a373939373234626639386234353664326237383663333631373161343862
  38383263646138376332333237633764356662366239356331636663346431373835
# ldap_rabbitmq_env example: rc, dev, prod
ldap_rabbitmq_env: prod-ext

rabbitmq_external_vip: true
rabbitmq_external_interface: ens19
rabbitmq_keepalived_vip: **************
rabbitmq_keepalived_vip_mask: 27
rabbitmq_keepalived_vip_brd: **************
rabbitmq_keepalived_vip_gateway: **************
rabbitmq_keepalived_virtual_routes: true
rabbitmq_setup_ufw: true

rabbitmq_consumer_timeout: 10800000

rabbitmq_extra_vhosts:
  - name: service-dev
    state: present
    username: prod.ext.service.dev
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
  - name: service-prod
    state: present
    username: prod.ext.service.prod
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
  - name: service-rc
    state: present
    username: prod.ext.service.rc
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
  - name: service-sta
    state: present
    username: prod.ext.service.sta
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
  - name: sqs-host
    state: present
    username: prod.ext.sqs.host
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65343934353934313266363730303762323066313831633436613933646166343736373732653463
      3237653536643738663466303061653566396136303835300a383034393936323236336639386439
      35363834646263383731623435376161613638323463356662333764666139303838633366393431
      3436336234333766310a363561343431376633343934373166663435656163326330626532333230
      3761
  - name: bas-ec2-sqs-scala
    state: present
    username: prod.ext.ec2-sqs-scala
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      30656437366236386330366463333534323132343264366437616465383434633230633564653430
      3363346232306433376137346135616236653237663138360a343963366635656661636336383330
      61653366653765376162613735303732633366346366663763303932663265323733626334383032
      6335343931333763320a353561333437643334633661653065663733313630623063613339366164
      66346334303436633139393435656561313066396265666130653532623332303937
  - name: bas-ec2-sqs-scala
    state: present
    username: prod.ext.bas
    password: !vault |
      $ANSIBLE_VAULT;1.1;AES256
      65663037303262656665363936363862636438336563373461373334343336663733623139376337
      6135653133396561613965303931313933376634323065370a636266313632326565366235623330
      66646163323837663239646535306565326563356463623734323732383732383336386339353961
      6562653862343165640a346566363136303864363864363134386336363636653438613666343438
      35323639343136343063323230376333306134393637613364643636633161643364
