---
pmm_server_ip: pmm-api.sl.local
pmm_server_port: 443
pmm_server_user: admin
pmm_server_user_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  36323836366336626665626665653666303134636331316539326133346566326661376537366537
  3565393330303238303361636363643335393834663036640a323836333366643362643437316561
  38626338653932666665353638633635373961633261613035323461396162306262373930303932
  6434626235643238340a306430376532636465616534323336663362613638323664343066326433
  61656465316339643862633563313236313862626536353535353338363462323561

pmm_agent_postgres_user: pmm
pmm_agent_postgres_user_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  34306266373932376161373564393437306631393066346434353162653134623061373162353434
  3137646362623830373730386636343837303736633064310a336663363836343665363731643566
  30656533613638666163373031353038363935633465643639326265346135663636343765623238
  3761316663623264370a313166373234373738323934613363663063316531323131663561616535
  36363434643263346633623530386263653631323031306234336535306431363763
pmm_client_postgres_con_limit: 10
pmm_client_postgres_user: pmm
pmm_client_postgres_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34306266373932376161373564393437306631393066346434353162653134623061373162353434
          3137646362623830373730386636343837303736633064310a336663363836343665363731643566
          30656533613638666163373031353038363935633465643639326265346135663636343765623238
          3761316663623264370a313166373234373738323934613363663063316531323131663561616535
          36363434643263346633623530386263653631323031306234336535306431363763


pmm_agent_mysql_user: pmm
pmm_agent_mysql_user_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  35383566303330376161663362373538646134323865653265613864363166643739326435363834
  6461353365643733616130363636303137353835616630620a383233303363336564336266363766
  39626636653366336366386364393134623962613034386361343430313232616636656561383362
  6636393562376430630a306461396631363832396265353034303965376139323931393063396331
  33366566303663666631623138313830386532653138653963386463633232383464
# pmm_agent_postgres_con_limit: 10
pmm_agent_version: 2.40.1
pmm_agent_image: percona/pmm-client:{{ pmm_agent_version }}

pmm_cpu_period: "100000"
pmm_cpu_quota: "50000"
pmm_memory: "512m"

pmm_api_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          64316233343065306331323231326632653564643764396463633436663866386436306534383831
          6332663635636238323762383731356663376664396662640a356234336436333965393766393763
          66396332663131663464616137643739626535366566333638326264336132386263653738336139
          3061336561303233610a303065663338313036643639616330376438653438656132653464316531
          66643439663634383637613135633232613235306166636462646436343562333364303362373537
          34663065346434356565386561656134333637326435356335376231656539316537343634326434
          34623666646236376533363630323637636166306565643735636564326136666239636561393266
          31633631663630383664386135373262383034383338633733373630396339373237636630363963
          3364
