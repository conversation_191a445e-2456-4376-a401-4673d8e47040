---
ipa_bind_addr: "{{ ansible_host }}"
ipa_admin_user: admin

ds_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33376439353033313864613834313463653561363139393638623434353436306232626266623564
          6265643836363063353161326139303230316161366339370a666635633534396139383537653164
          34646462383232393464386137393533623436303166656435393230306133333138353439633465
          3139393665663461360a393064393834636538636537393962386665663632393363666163383430
          3030

admin_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37666238383865326166666637356237346338376165663037623364386237643733343762316230
          3432343463613435653566633636373561373030383664650a643032363632383231393161333966
          62303532313331393030663735656665623263373634663036373939303230333636616330646165
          3830616264343135340a636163396162646132353163363331396462386565366464333830643939
          6566

ipa_realm: SL.LOCAL
ipa_domain: sl.local

ipa_server_hostname: "{{ groups['freeipa_server'] | map('extract', hostvars, ['inventory_hostname']) | join(',') }}"
ipa_server_ip: "{{ groups['freeipa_server'] | map('extract', hostvars, ['ansible_host']) | join(',') }}"

ipa_replica_hostname: "{{ groups['freeipa_replica'] | map('extract', hostvars, ['inventory_hostname']) | join(',') }}"
ipa_replica_ip: "{{ groups['freeipa_replica'] | map('extract', hostvars, ['ansible_host']) | join(',') }}"

ipa_replica_second_hostname: "{{ groups['freeipa_replica_second'] | map('extract', hostvars, ['inventory_hostname']) | join(',') }}"
ipa_replica_second_ip: "{{ groups['freeipa_replica_second'] | map('extract', hostvars, ['ansible_host']) | join(',') }}"

ipa_vip: *************
ipa_vip_name: hz-prod-ldap.sl.local
ipa_keepalived_etc_dir: /etc/keepalived
ipa_keepalived_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          38623632626631643031313235313165383837333439633936613235373564316334393264356533
          6165633930323166343339613966353733613736336664360a666433653566623961343935643135
          30663337623163616266343135663436663063656164356337343737373139353736343030656232
          3434363337636235610a353736663735343530333761333139393232616439313764633163313432
          6461

ldap_server_name: "freeipa"
ldap_server_host: "{{ ipa_server_hostname }}.sl.local"
ldap_replica_host: "{{ ipa_replica_hostname }}.sl.local"
ldap_server_port_ssl: "636"

dns1: *******
dns2: *******

ldap_bind_user: ldap_bind
ldap_bind_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          64313336633565363135663439396631613236343762363635386233653562396434303438343430
          3336383963336261363865636263303134353739306463300a313462353031356361316231336634
          30396333373066346431343264396464353031346563643839396663333466356335366338336232
          3266373734653630640a336266643039663438623335653566613536626631643137386432373765
          39646264616432326664326664333930303962623833343965663638326135313339

ldap_bind_dn: uid={{ ldap_bind_user }},cn=users,cn=accounts,dc=sl,dc=local

postgres_sync_ldap_user: ldap_sync
postgres_sync_ldap_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66303638633064373131313362633766363938346239366230633966356130363230623339623130
          3462333932323536393264316533386630663238313238630a663961383432326233383235376131
          31356539306165336130333364393032306363303333626331613935656639343462653962656635
          3530326432383936330a363961353735313762383934316635316265633336633835613561306662
          32393263666230666463663130353865643037376430613033666164653337633338

ipa_os_version: centos-9-stream
ipa_image_version: "4.11.0"
ipa_container_image: freeipa/freeipa-server:{{ ipa_os_version }}-{{ ipa_image_version }}

ipa_ca_path: /etc/ipa/ca.crt

ldap_technical_users: technical_users
