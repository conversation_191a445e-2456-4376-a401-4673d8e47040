---
# General
keycloak_admin: "admin"
keycloak_admin_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          39646166633338633937313331373838383962373265303336366166626232346666643533303265
          3365343535366266643334643665646334636665343939360a616639316535313532343064613538
          61313064613834613535623765356261346165626338626439313561613439363337623137363731
          3131353630626466380a336534633335616338363265333236663631643365656437343837353638
          6365
# keycloak_hostname: "{{ inventory_hostname }}.sl.local"
keycloak_home_dir: "/opt/keycloak"
keycloak_cert_dir: "{{ keycloak_home_dir }}/cert"
nginx_reverse_hostname: "keycloak.sl.local"

# Database
kc_db_user: "prod_keycloak"
kc_db_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33613438383034326139663135393037316165373066646338346239353563303437326334393237
          3964356661343437663764663235346131343830616531380a633061646366376536363633353335
          64363238623532326532383336666435393136306266633233353438613439363737346332376334
          3938333636646233350a323661303264363863656362666434663030313966343537366135633563
          6162
kc_db_host: "************"
kc_db_port: "5432"
kc_db_type: "postgres"
kc_db_name: "prod_keycloak"

# Certs
kc_cert_key: "{{ nginx_reverse_hostname }}.key"
kc_cert_file: "{{ nginx_reverse_hostname }}.pem"
kc_key_store_file: "keystore.jks"
kc_key_store_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66626437623136646138363432393465373632306134393435666462623937366535633835333737
          3932333163623135633563346138626537373663363433350a633162633865326262643130333463
          33633234376437303031306566646463396339303234333562333264653439383837363064636531
          6539646266323036360a323537366436313236386363383238616563353936616138633062336532
          3165
kc_trust_store_file: "truststore.jks"
kc_trust_store_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62623464656530353835666433313065666133646630373632663865366636633439396637383664
          6365333635313338316638343139363234633735356463650a326430356432613364353132313035
          30343531383964653039633266333036353965373633303533306532323364376463353365383462
          6564653135613435380a633263663331623464636161343834613735306266306135666135633332
          3836
kc_freeipa_ca: "/etc/ipa/ca.crt"
