---
postgres_db_env: rc
postgres_cluster_name: crm
vip_address: ***********
vrouter_id: 48
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33303863356264333965333130323962616163353665653763326334653731626362386236663765
          3434653838363966343133613432663431353361656363340a653335306531656261323566633739
          65306633306531613631633062353433303535326232306433636132383432616134643938666166
          3335376532303063320a353138396563313230386230663736396663323464326637336566616339
          66356437343838343963323163633464343664663738393466653034633165363139
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_postgresql_version: "14"
pgcluster_pgbackrest_version: "2.53.1-1.pgdg24.04+1"
