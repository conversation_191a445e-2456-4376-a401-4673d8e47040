---
postgres_db_env: stage
postgres_cluster_name: bas
vip_address: ************
vip_address_ro: ************
vrouter_id: 29
vrouter_id_ro: 22
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          31353233633665356430393338333133663537353966623166653963326661363362373137386237
          3664633939373664376565336561346361373935323266310a636232363263363636336135393030
          35653031633161323064303633613838613531643837343335663036316133373936636663633837
          3830316233343061380a616531326634663835663666393966636264646563623363643761646464
          3539
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 1
pgcluster_pgbouncer_ro_server_idle_timeout: 1
pgcluster_pg_stat_monitor: true
