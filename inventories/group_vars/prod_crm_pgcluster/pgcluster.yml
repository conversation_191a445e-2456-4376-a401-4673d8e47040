---
postgres_db_env: prod
postgres_cluster_name: crm
vip_address: ************
vrouter_id: 48
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          36376534653638366537656339346231616164643762653734356538626664626463613964613562
          6339636237343665323466316263623035326637643166660a353762356532343536643235346564
          62396434393539343730656565346165626239653364636535376233386161396166313733623936
          3639633735653965370a376232666261326361633038613936366639656164653663343161303262
          64336661333166643332313836316265333732373137623061653662313463356365
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_postgresql_version: "14"
pgcluster_pgbackrest_version: "2.53.1-1.pgdg24.04+1"
