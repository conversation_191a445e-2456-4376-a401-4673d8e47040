---
postgres_db_env: rc
postgres_cluster_name: aiservice
vip_address: *************
vip_address_ro: *************
vrouter_id: 194
vrouter_id_ro: 202
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34383166656338343134646537356631326331336164613233373434653330303031643238333430
          3030663337356632373266316534323561656336663938660a613564336234393264313862326630
          35636637613634303935326566313638366661306265373664386332333562356464313932616235
          3735346662623461620a373230363333643636636665626566326430376364346664376636636364
          61363433613733323132326432373833323539663938303636653536376331306635
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
