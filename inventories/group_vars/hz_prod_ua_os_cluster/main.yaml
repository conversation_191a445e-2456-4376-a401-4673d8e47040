---
## Common opensearch configuration parameters ##

os_cluster_name: prod-cluster

# opensearch download
os_download_url: https://artifacts.opensearch.org/releases/bundle/opensearch

# opensearch version
os_version: "2.3.0"

# opensearch dashboards version
os_dashboards_version: "2.3.0"

# Configure hostnames for opensearch nodes
# It is required to configure SSL
# Example es1.example.com, es2.example.com
domain_name: prod-ua-os.sl.local

# Java memory heap values(GB) for opensearch
# You can change it based on server specs
xms_value: 32
xmx_value: 32

# Cluster type whether its single node or multi-node
cluster_type: multi-node

# opensearch user info
os_user: opensearch

os_dashboards_user: opensearch
admin_password: UYqJ8sK8z2mc
kibanaserver_password: 6QtFTfQVCLKa
