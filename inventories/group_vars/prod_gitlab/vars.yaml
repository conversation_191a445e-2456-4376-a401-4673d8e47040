---
gitlab_external_ip: **************
gitlab_internal_ip: ************

gitlab_domain: "gitlab.sl.local"

# Docker network
gitlab_network_interface: "ens20"
gitlab_network_ipvlan_mode: "l2"
*********************: "***********/23"
gitlab_network_gateway: "***********"
gitlab_network_ip_range: "{{ gitlab_internal_ip }}/32"

# Gitlab SSL
freeipa_certgen_key_path: /data/gitlab/ssl/gitlab.sl.local.key
freeipa_certgen_csr_path: /data/gitlab/ssl/gitlab.sl.local.csr
freeipa_certgen_cert_path: /data/gitlab/ssl/gitlab.sl.local.crt
freeipa_certgen_domain: gitlab
freeipa_certgen_ssl_country_name: DE
freeipa_certgen_ssl_state_or_province_name: Dusseldorf
freeipa_certgen_ssl_locality_name: Dusseldorf
freeipa_certgen_ssl_organization_name: <PERSON><PERSON><PERSON>og<PERSON>
freeipa_certgen_ssl_organizational_unit_name: DevOps
freeipa_certgen_ssl_common_name: gitlab.sl.local
freeipa_certgen_ssl_email_address: <EMAIL>

# Backup to S3
gitlab_backup_s3_bucket: "gtlb-backup"
gitlab_backup_s3_key_id: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          38336530393837313037356633363933303633643939356232316564643331633035393930323263
          3633643733336539316538656437313062323865303131660a313838363165633135373261326366
          64613862623930363339306564616461393335353366303334643866626462346366313835666434
          6230336531666466620a393632346434353764363866653066663266323866623066623232653864
          38386139616334383238623539363863666165303531616663646461343862376166
gitlab_backup_s3_access_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35343531623230306632346433623538306135636130326362656630313132313639393032326333
          3564353764336661653466363337663864326462353133390a363535663334333935323030633564
          30643732303836393232386132633462343961373561613264396662323233343335663837646636
          3066656330626435360a376232616636636463356663663636316365333338643737386334633939
          35613962373337306233633636306136633230326331303439306635366230383961373431383761
          6464356562373834386138363332333935633730346333326231
gitlab_provider: "AWS"
gitlab_region: "eu-central-1"

# Gitlab SMPT
gitlab_smtp_address: "email-smtp.eu-west-1.amazonaws.com"
gitlab_smtp_port: 587
gitlab_smtp_user_name: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66306334303439353936393032643238303630633461616534626237326633613662636330376534
          3431303762396462666435623338386139613735613136300a323762653834646162613165636137
          64633034636534636562623465633764313464346239626261303537626634303335396162643433
          3339656664636437310a323934633366393335333039376133626265636135373437383635313665
          37303362343063356136373130343263373162343263316330386566616261353830
gitlab_smtp_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63366532366134643733643230353566626431626365663830643734656535386336623435653864
          3532623662333761363434373733353130323632343639390a623337323030306438666532333838
          61393661323131646636363363366631366634313734373636323861303763363037313235663061
          3834313632643938340a663434373863393132653665373333353361653165323735396563396462
          63633136613063333864363032333466383265373431383230393838366261393133633537393832
          6661623837376635363530623561313132326666623639373065
gitlab_smtp_domain: "sl.local"
gitlab_email_from: "<EMAIL>"
gitlab_email_display_name: "Sellerlogic Gitlab"
gitlab_email_reply_to: "<EMAIL>"

# Keycloak
gitlab_idp_cert: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386137376536326464386231373665343932373766636339633931383463363763366436396365
          3538343332396666396638633534663735623731643735640a623131666635376364623934323562
          65373735656633633934663831393733383064363534353836613331626530613962643665353366
          3262643738333230640a373238663565653935386161646661633831626137626538373732616563
          38643632393164313233373533383063653464303035643737616130656565383434366262313966
          63656132313831633631353236633634316362663862336562373063633564363263353762653263
          31396661343336313037363032663436343339396165623732646135646136303239626132353430
          38346133343231336663313639386637316238623564613863303865386163393334343937633833
          37326565383630316232316165353230306362643932346638613132613766326135643439343965
          30313831303962386337643231636536356663643065613863656362346163313538363161616538
          32383664666531386265623533363366326234363831373638373063633139646334306337376632
          36653063356634623165656266636561633665616465376461396436303133313464633464646339
          38666639643439613964653262303561376139356336373839323634633039356465376562643635
          63343234353930663531383365653935316361353662383833316337396362356636306661326635
          30353239363262333337653638393666363736373061616362613863306536356536363733396361
          65376635373437626663306336306461326532373636646638666637393938663065633561616661
          37336638366439353237316439376437326363373030323932316139633266623363663133666531
          64393131626566363536633063366530353662653461393835656530633162393763646630383937
          34333563393733323439303933363566373530656433316661363765613764346632366631363265
          62636538396566656637663332623236336433326364396335633936373264636330363633393536
          35343533643663383934383466356262363565643261356138383134663335303637366135353831
          39303963383561313230626364643537616331666566343837623737663261623031616236353436
          65306531323964663331633639396634376564353565663462616435376438353533343762373366
          65663532386265646136383734306362336666373766343830656336303036316434643031363434
          39343839656631663933346135666435323165343538383865316664666165653237303433616364
          36326463623735656236643131666238376262333430373337343537343866613838333065333764
          61393834336334363130643734353435323861306361373061656232306138386437663335333664
          34633665623862333836363066633739666537656136323331313862653331316335393036373038
          64396266313035383530396466356664643537633733323033613763333731653464396132656538
          66393730663432346233396266393032626566343835393961343632316133653133313766333237
          37373364303536333635653065356637653661653732336331383631343839383334393635313665
          66623835366434656365376365326462356433646639653130646235653462623661396337363734
          32396664336139333636323366643936626161623138366431366339366339306237306136333738
          35643335323931653063656331363731616230353736613630653439663331336562633331363138
          30386164303533623736303434316433363437396136633366346437616433616439356231346336
          33363261333034383632383833643865326531313738326130306362363538303332363439326262
          37303539386363326265353963636431356334316430646435353164656531356661396164376139
          30353362393965323135613737613066663032383538313936303264353032353330666433643663
          31616139333830616238623664383736376235646334663833643265663930373561313931333035
          35333737306139653633373432346533366364366632393436326237316564653363383162383030
          32313464396564376436386538623030633064373535323835653533343037353265343230626365
          61373634323337363736343965363965626665323864363032623131643438333762333164353139
          38653162326136643131376362306365373064373632653936666234343130353761316165373831
          65653930636439373865313938613737383336343033616539643864396436613639646566353839
          35386164643533623465373230393961343563376136643431353665313735366332326562646432
          37363065336564373030313166313662336164333135636164623737346136356163663539646238
          39366639303633626534303033333466316466306262396563396633366135646662356365316264
          64396663386238363165656133306666626239316162666337393932393566343033336361656635
          61656431393937613563323464623530663737303531383061323734343838363537373162376263
          61626164623139303362623737383037656234633863376231653336613235323339373637363539
          62633934363435353362633837383233636663666139613266383736613464636434303061663834
          30376637656538326163626434383831326232363062643735363462613562626636643137386237
          6462

gitlab_idp_sso_target_url: "https://keycloak.sl.local/auth/realms/Freeipa/protocol/saml"

gitlab_registry_external_url: "registry.sellerlogic.com"
gitlab_pages_external_url: "pages.sellerlogic.dev"
gitlab_kas_external_url: "ws://gitlab.sl.local/-/kubernetes-agent/"
gitlab_kas_api_secret_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          64353063623935613163306232343965643663643661636534306137326531383636323864623636
          3132343934623436663165643735653466643639343136370a653831366665353333613135643162
          35396662383964306436623136613035613035353664306230363139656232643138656133633464
          3763323664666531370a643265333733373638393366346261333264363634346135646462363661
          30393934393137393238373565396666626261663234626166626433643335626162363561363865
          6238373165643035393939336431386632396430623230366331
gitlab_kas_private_api_secret_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          39623531386461616339363430633465666538346666646166616265393730363639376666313465
          3039383033326463633834333039353630366265366137300a386565323132663531663930356238
          34626463653436373737303632356531353566333438343464383364306465393633623262653630
          3831363866343230320a663539653235393131313938366635313365616462613463323866323139
          61346664393230646534633363333130653031363534346133343466323333643635303937356638
          6362323935616564623966306532633439663664613639616433

# Backup
bareos_backup_task_custom_backup_job:
  - name: Gitlab-Backup
    client: "{{ inventory_hostname }}"
    schedule: Nightly_every_Day_at_12
    fileset: gitlab
    pool: Full
    runafter: >-
      bash -c 'OUTPUT=`docker exec gitlab gitlab-backup create CRON=1 2>&1` ||
      echo -e \"Subject:Failed GitLab Backup\nFrom:GitLab.Backup\n{$OUTPUT}\" |
      /sbin/sendmail <EMAIL>'
  - name: Gitlab-Config-Backup
    client: "{{ inventory_hostname }}"
    schedule: Nightly_every_Day_at_9
    fileset: gitlab
    pool: Full
    runafter: >-
      bash -c 'OUTPUT=`docker exec gitlab gitlab-ctl backup-etc -p
      /var/opt/gitlab/backups/config_backup 2>&1` ||
      echo -e \"Subject: Failed GitLab Configuration backup\nFrom:GitLab.Backup\n{$OUTPUT}\" |
      /sbin/sendmail <EMAIL>'

bareos_custom_backup_fileset:
  - name: gitlab
    signature: MD5
