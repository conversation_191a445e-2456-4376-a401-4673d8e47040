---
postgres_db_env: stage
postgres_cluster_name: fullmailbox
vip_address: ************
vip_address_ro: ************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35666634303366316639666662663266323838623439383663343938346231633463613636363165
          6437653464656263393439623334373262633836376163610a343632393434336632363539346364
          35383737356330663762623064646664343232306661323261383931316139393263323239313036
          3230613839653963330a306237306531316564316338303930643639343538343432393866656333
          65386131306261376535623234343936653235623233356364306461393730393935
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
