---
postgres_db_env: rc
postgres_cluster_name: bas
vip_address: ************
vip_address_ro: ************
vrouter_id: 30
vrouter_id_ro: 49
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          61613637333664626235666539313661393335646431373532623234633464366338393936653166
          3530393633643538303934346538383666626435303638620a363635643936656566323266363563
          66616666373636393837363133363561616636636565333362613632376166366637396637346536
          6234643932656234350a623635353135323063343534366562316236333730343933383065366666
          6439
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 1
pgcluster_pgbouncer_ro_server_idle_timeout: 1
pgcluster_pg_stat_monitor: true
