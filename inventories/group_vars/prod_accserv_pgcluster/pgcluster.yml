---
postgres_db_env: prod
postgres_cluster_name: accserv
vip_address: *************
vip_address_ro: *************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          64643466643865356637663331666332353937636133343436316134323933366132613162386263
          6465373637613936666636393031353062656232373839320a306465666139626337616264376338
          31633039653839313234336234626465633234303266396532363762346364386265323536306534
          3434383638316562300a653564356263666261373130366161636136656333366132396465373832
          33346563343939616638623134323935633461623835656138386338343531623234
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
