---
mysql_databases:
  - name: "service_db"
    collation: "utf8_general_ci"
    encoding: "utf8"

mysql_users:
  - name: "service_adm_user"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37373037323239316463393030303333356131613039653536396339613336616136353266636261
          3439353135363830373933303736343765346331623261320a666331323031643231366130346636
          34373466646363613235343766323065373433393833376237333665656630303532306565306235
          6634646366633631320a653438303436333038646130623065623534343032653837633138643865
          6338
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "prom_user"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37303934656231313932346431366334323036376439316335633032653064663236616237616563
          3762313864636161346565386165643863653936353834350a653735626537376636613136656663
          35356432326462393233323264613933373737376230666436656530343266613765373664343539
          6137356163623430660a376136373234616366656638303366386637646636323938623261336639
          3635
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "sellerlogic_prod"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          30373333323438313234306537393832393334313464333963646333373065633166653964363937
          3435336232613862353661613731386265303964636138630a363164343433386163363063633063
          30623832643662643963393534306163373432313139613238303161656338323733396366353530
          3136613238306366340a306465666237383765316232373863623239333539653963313663303634
          35643937656435306336623161333033666532306434313866633062623664623036
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "v.kanyuk"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66333635666165393335346632666239666434643130353233346165666664623366393332373831
          6238646261316264373931316162316366386637336430370a393431313634636139356665613265
          31383034353935353431623736366637343635626537636430333534383161623864313733616666
          3035613033313930390a653430303461343861313433303466643531383631363531343738663339
          6162
    priv: "*.*:ALL,GRANT"
    host: "%"
mysql_prom_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63643264626464303763383131353036616238303835613435306436326363656539623031303331
          6266636535623930623964653630623335366366623939310a653366316162613735383130303938
          36363332303237646265613837663965306338646435633732623230303330653237326235373162
          3230636139326634320a396238646539376164366138626363306130396135383439623635356235
          3634
# Version to install, defaulting to 5.6
percona_cluster_mysql_version_major: "8"
percona_cluster_mysql_version_minor: "0"
percona_cluster_mysql_version: "{{ percona_cluster_mysql_version_major | int }}.{{ percona_cluster_mysql_version_minor | int }}"
# Basic settings
mysql_root_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          38303732643033363132386330336266376432363565333264396238343333313837323539383037
          3839363837633637383963353163343739623166343662360a336666383161343437613230323931
          32616332643064306666306335356533616533373133626131623632306638383838303964393535
          3264363663343333350a363263663639306633626130353036303531386363326339383763353933
          6634

percona_cluster_mysql_port: "3306"
percona_cluster_mysql_bind_address: "0.0.0.0"
percona_cluster_mysql_language: "/usr/share/mysql/"
percona_cluster_mysql_datadir: "/data/mysql"
percona_cluster_mysql_open_files_limit: "100000"
percona_cluster_mysql_log_error_verbosity: "3"
# Fine tuning
percona_cluster_mysql_key_buffer: "16M"
percona_cluster_mysql_max_allowed_packet: "256M"
percona_cluster_mysql_thread_stack: "192K"
percona_cluster_mysql_cache_size: "8"
percona_cluster_mysql_myisam_recover: "BACKUP"
percona_cluster_mysql_max_connections: "50000"
percona_cluster_mysql_table_cache: "100000"
percona_cluster_mysql_thread_concurrency: "10"
percona_cluster_mysql_query_cache_limit: "1M"
percona_cluster_mysql_query_cache_size: "16M"
percona_cluster_mysql_character_set_server: "utf8"
percona_cluster_mysql_collation_server: "utf8_general_ci"
percona_cluster_mysql_mysqldump_max_allowed_packet: "128M"
percona_cluster_mysql_isamchk_key_buffer: "16M"
percona_cluster_mysql_sort_buffer_size: "256K"
percona_cluster_mysql_tmp_table_size: "16M"
percona_cluster_mysql_max_heap_table_size: "16M"
percona_cluster_mysql_table_definition_cache: "50000"
percona_cluster_mysql_log_bin_directory: /data/logs
percona_cluster_mysql_log_bin: "{{ percona_cluster_mysql_log_bin_directory }}/mysql-bin.log"
percona_cluster_mysql_sync_binlog: "10000"
percona_cluster_mysql_expire_logs_days: "10"
percona_cluster_mysql_max_binlog_size: "1G"
# InnoDB tuning
percona_cluster_mysql_innodb_file_per_table: "1"
percona_cluster_mysql_innodb_flush_method: "O_DIRECT"
percona_cluster_mysql_innodb_buffer_pool_size: "170G"
percona_cluster_mysql_innodb_read_io_threads: "8"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "0"
percona_cluster_mysql_innodb_lock_wait_timeout: "50"
percona_cluster_mysql_innodb_log_buffer_size: "16M"
percona_cluster_mysql_innodb_log_file_size: "1G"
percona_cluster_mysql_innodb_open_files: "100000"
percona_cluster_mysql_character_set_client_handshake: "FALSE"

percona_cluster_mysql_timezone_info: "false"

install_rpm_repositories: "true"

# To disable log_bin in percona >=8, enabled by default
percona_cluster_mysql_disable_log_bin: "false"

# Default Auth Plugin
# used in templates when Percona Server >= 5.7
percona_cluster_mysql_default_authentication_plugin: "mysql_native_password"

percona_cluster_mysql_audit_log_enabled: true
