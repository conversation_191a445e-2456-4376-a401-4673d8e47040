---
postgres_db_env: prod
postgres_cluster_name: serviceprice
vip_address: *************
vip_address_ro: *************
vrouter_id: 124
vrouter_id_ro: 125
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          65353236626438643064383031343538616531656335666465356236643634316132373061626431
          3861613537393661383539303661666662616661396165350a613837303665663364636232313130
          39373038383537623837376230643762333661623061646531353966333333663163643134653364
          6164363137353063310a306162663766386462386430333531346566376266666263663963316365
          3232
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_sb: 150GB
pgcluster_effective_cache_size: 150GB
pgcluster_patroni_no_failover: ["2", "3"]
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
