---
postgres_db_env: rc
postgres_cluster_name: accserv
vip_address: *************
vip_address_ro: *************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          31653161643835643932346262393961396634396666323432313563646637316461343237353065
          6631656563313838386339373332336333303432356331350a656462353733393030373330376664
          62363134313131633631303961306161613530623736383362313039386666373034343432346565
          3030333665323531300a303237613438316231343632666136326265623362353838306230333164
          63313963653635326566616435653734643363353535636636323765663864623939
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
