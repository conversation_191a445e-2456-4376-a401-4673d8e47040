---
postgres_db_env: infra
postgres_cluster_name: zabbix
vip_address: *************
vrouter_id: 48
pgcluster_patroni_sb: 2GB

pgcluster_timescaledb: true
pgcluster_archive_mode: "off"
pgcluster_archive_command: "true"
pgcluster_work_mem: 5MB
pgcluster_maintenance_work_mem: 1GB
pgcluster_max_worker_processes: 27
pgcluster_max_parallel_workers_per_gather: 4
pgcluster_max_parallel_workers: 8
pgcluster_default_statistics_target: 500

pgcluster_autovacuum_max_workers: 10
pgcluster_autovacuum_naptime: 10
pgcluster_timescaledb_max_background_workers: 16

pgcluster_checkpoint_completion_target: 0.9
pgcluster_random_page_cost: 1.1
pgcluster_effective_io_concurrency: 256
