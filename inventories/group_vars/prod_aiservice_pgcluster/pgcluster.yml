---
postgres_db_env: prod
postgres_cluster_name: aiservice
vip_address: *************
vip_address_ro: *************
vrouter_id: 194
vrouter_id_ro: 202
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62373330383063333331373566633362333430353566306334353265633064333265653364666239
          3863306366646436646562323630653037636637333933610a393332323632343065343938336535
          62336364383439323733353337313061613835626265326362336637626661373963363239343832
          6635333463316238350a613235356339363930386138653562633835343732643236626636656261
          37376639326562363461633461643737626239336663613462666331373366666262
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
