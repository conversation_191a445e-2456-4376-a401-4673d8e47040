---
## Common opensearch configuration parameters ##
os_cluster_name: rc-cluster

# opensearch download
os_cluster_download_url: https://artifacts.opensearch.org/releases/bundle/opensearch

# opensearch version
os_cluster_version: "2.3.0"

# opensearch dashboards version
os_cluster_dashboards_version: "2.3.0"

# Configure hostnames for opensearch nodes
# It is required to configure SSL
# Example es1.example.com, es2.example.com
os_cluster_domain_name: rc-ua-os.sl.local

# Java memory heap values(GB) for opensearch
# You can change it based on server specs
os_cluster_xms_value: 10
os_cluster_xmx_value: 10

# Cluster type whether its single node or multi-node
os_cluster_type: multi-node

# opensearch user info
os_cluster_user: opensearch

os_cluster_dashboards_user: opensearch-dashboards

os_cluster_admin_password: admin
os_cluster_kibanaserver_password: kibanaserver  # Change this and encrypt with ansible-vault

# Performance tuning with proper prefixes
os_cluster_indices_memory_index_buffer_size: 10%
os_cluster_indices_memory_min_index_buffer_size: 48mb

# Logging with proper prefixes
os_cluster_logger_level_root: INFO
os_cluster_logger_level_org_opensearch: INFO

# Cluster settings with proper prefixes
os_cluster_routing_allocation_disk_threshold_enabled: true
os_cluster_routing_allocation_disk_watermark_low: 85%
os_cluster_routing_allocation_disk_watermark_high: 90%
os_cluster_routing_allocation_disk_watermark_flood_stage: 95%

# Additional OpenSearch settings
os_cluster_bootstrap_memory_lock: true
os_cluster_http_cors_enabled: true
os_cluster_http_cors_allow_origin: "*"
os_cluster_http_cors_allow_headers: "X-Requested-With,Content-Type,Content-Length"

# Security plugin settings
os_cluster_security_ssl_transport_enabled: true
os_cluster_security_ssl_http_enabled: true

# OpenSearch Dashboards settings (optional - enable on specific nodes via host_vars)
os_cluster_dashboards_enabled: false  # Set to true in host_vars for nodes that should run dashboards
os_cluster_dashboards_port: 5601
os_cluster_dashboards_host: "0.0.0.0"

# Dashboards security settings
os_cluster_dashboards_ssl_verification_mode: none
os_cluster_dashboards_cookie_secure: false

# Dashboards multitenancy settings
os_cluster_dashboards_multitenancy_enabled: true
os_cluster_dashboards_multitenancy_preferred_tenants:
  - "Private"
  - "Global"

# Dashboards read-only roles
os_cluster_dashboards_readonly_roles:
  - "kibana_read_only"
  - "readonly"

# Performance settings for dashboards
os_cluster_dashboards_max_payload_bytes: 1048576
os_cluster_dashboards_request_timeout: 30000

# Logging settings for dashboards
os_cluster_dashboards_log_level: info
os_cluster_dashboards_log_quiet: false

# Telemetry and features
os_cluster_dashboards_telemetry_enabled: false
os_cluster_dashboards_maps_include_elastic: false
os_cluster_dashboards_timeline_enabled: false
