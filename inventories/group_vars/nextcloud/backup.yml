---
bareos_backup_task_custom_backup_job:
  - name: Nextcloud
    client: "{{ inventory_hostname }}"
    schedule: Nightly_once_on_Week
    fileset: nextcloud
    pool: Nextcloud
    runafter:
      "sh -c 'find /backup -maxdepth 1 -type d -mtime +30 -exec rm -rf {} + \
      ; mkdir /backup/`date +%x | tr / -` ; /bin/mysqldump  \
      --single-transaction --skip-lock-tables --triggers --routines \
      --events --all-databases | zstd -o /backup/`date +%x | \
      tr / -`/dump.sql.zstd ; tar --zstd -cf /backup/`date +%x | \
      tr / -`/user-data.tar.zst /data/ ; cp -r /var/www/html/next/ /backup/`date+%x | tr / -`/'"

bareos_custom_backup_fileset:
  - name: nextcloud
    # compression: lz4
    signature: MD5
    # file:
    #   - File = "/backup/dump"
    #   - File = "/data"
    #   - File = "/var/www/html/next"

bareos_custom_backup_pool:
  - name: Nextcloud
    pool_type: Backup
    label_format: Nextcloud
    storage: File
    volume_retention: "40 days"
