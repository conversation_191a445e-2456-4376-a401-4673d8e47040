---
postgres_db_env: prod
postgres_cluster_name: ep
vip_address: ************
vrouter_id: 219
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          39346432386465663537613234303433343636376166346562326239653037663163613039303965
          6133656131643833333131663634383633656663323930370a353634636238356461343438333531
          38343332343037346162366461396336303130306666346333643762656635653730356631313438
          3139356138376533620a343536313734636637306638623138393162633832393561336132626135
          62363638396135306338396339656135626534396335316364396431383865333734
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 1GB
pgcluster_pg_stat_monitor: true
pgcluster_effective_cache_size: 1GB
