---
postgres_db_env: rc
postgres_cluster_name: ep
vip_address: *************
vrouter_id: 219
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63333231643166616332643934336563336632663763343438636338373139666532623735663533
          3762613933316234346331373531396461633966373335300a316334303732633637383965616463
          65646234653563373963646537656139353334353765306639353833346437333331326634393039
          6164613636373131350a626638356338336562353538666439373032323334353438313231613765
          32363834333732663934383662363464366639323635323066373230623336316635
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 1GB
pgcluster_pg_stat_monitor: true
pgcluster_effective_cache_size: 1GB
