---
postgres_db_env: test
postgres_cluster_name: postgre
vip_address: *************
vip_address_ro: *************
vrouter_id: 195
vrouter_id_ro: 197
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66666565353933333735383861636637386432646138353561333963333963336532313665353266
          6235646661376231623962643138396638396539656362310a316333353131383662376265373932
          31646136366164383234363235646635633962656664633338323063643036343465393065623263
          3662653333366636610a363236363834353461623532366536326261616530316538356666383662
          3033
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pg_stat_monitor: true

clamav_install: true

clamav_cron_schedule:
   - name: scan viruses clamav
     minute: "30"
     hour: "10"
     path: "/"
     weekday: "*"

bareos_backup_nfs_mnt_dir: /mnt/datapool-02/backup
bareos_backup_nfs_mnt_old_dir: /mnt/datapool/backup
