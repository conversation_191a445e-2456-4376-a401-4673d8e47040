---
postgres_db_env: stage
postgres_cluster_name: accserv
vip_address: ************
vip_address_ro: ************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          30646633393066356664663231616434643137313434353430626337353562393166343761613165
          6362363661653864383964366666653535343732333234320a313438633435336464326462356166
          61373366333062373731616339333339336265356631326533316430633362373862386265393762
          6630613863623830650a636538396137303461393134373332636331643166656634376432633031
          30353038613732366265666561323636336631353630613632636632353538663936
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
