---
postgres_db_env: stage
postgres_cluster_name: repstat
vip_address: *************
vip_address_ro: *************
vrouter_id: 219
vrouter_id_ro: 220
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37613035393939366661646135326431393036663763303639323434653931326436356538396361
          3865343334633239653238626133663466666261323038610a346435343332336239623138323362
          35613831356562626363393035656262373866643939313432356338643536616132303363663531
          3235373131336563350a333337366565646166323135313430323631333462613437653162356135
          6566
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 1GB
pgcluster_pg_stat_monitor: true
pgcluster_effective_cache_size: 1GB
