---
postgres_db_env: stage
postgres_cluster_name: aiservice
vip_address: *************
vip_address_ro: *************
vrouter_id: 198
vrouter_id_ro: 199
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          39346639353738366362346563636536666230346264393038313262356464316265323261383263
          3361376636633666326230386538343566353766653035300a646361613638396663663062316438
          31303032656639666433653034393162663763633161333363383237326262656337663034316264
          3933303739623361620a323434316539306164343561623532356134623230633536613334306262
          36376230393730356230656264663664386264646138353539623331643462633838
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
