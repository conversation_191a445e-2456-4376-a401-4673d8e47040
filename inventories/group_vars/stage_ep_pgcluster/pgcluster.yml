---
postgres_db_env: stage
postgres_cluster_name: ep
vip_address: ************
vrouter_id: 219
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66333563306239633762363533316561316636643737373532656264656263663836623865396462
          3339613936646536633932633136383434336636653066640a366566303635356466386263326666
          66306337306361353036333831353133323031326138663764623033386536333034383338376261
          3831613232623163330a393637616236313634663239363135653931623162616238396261616334
          37346430643263376433383331636135316631333165306363633634363663653435
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_patroni_sb: 1GB
pgcluster_pg_stat_monitor: true
pgcluster_effective_cache_size: 1GB
