---
postgres_db_env: dev
postgres_cluster_name: auth
vip_address: *************
vip_address_ro: ************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          63326131646236353763656235396233633333386437666363366562656165316262383636336263
          3335393265373964623165306665323965636564653132320a356366613335666462333931616565
          64646231343036616531386265636664643864333363366231353535356238373761376463366630
          3733366633343133330a663431613036313538656635383439613836393263366162373434643066
          35663664663832376634346534653834306261366263393662313036363962366430
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
