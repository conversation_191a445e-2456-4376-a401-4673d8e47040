---
postgres_db_env: prod
postgres_cluster_name: fullmailbox
vip_address: *************
vip_address_ro: *************
vrouter_id: 198
vrouter_id_ro: 203
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          65663136356637663435333831646639616638663166653062356465383364633431633035633736
          3833373139366461376534373465616261353336643734620a396262373564356236383262316164
          33366164656164376232363965316432636138333764386336313966323636323031393365653566
          6339336166303837380a393266643966373265633935303539376437623739353866383065393832
          34656463396565373361393336306263643537366235383034323865643234623061
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_pgbouncer_max_client_conn: 4000
pgcluster_pgbouncer_default_pool_size: 4000
pgcluster_pgbouncer_ro_max_client_conn: 4000
pgcluster_pgbouncer_ro_default_pool_size: 4000
pgcluster_patroni_ttl: 240
pgcluster_patroni_loop_wait: 10
pgcluster_patroni_retry_timeout: 30
pgcluster_postgresql_version: "14"
