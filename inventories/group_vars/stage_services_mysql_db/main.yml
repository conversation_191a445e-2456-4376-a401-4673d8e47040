---
mysql_databases:
  - name: "service_db"
    collation: "utf8_general_ci"
    encoding: "utf8"

mysql_users:
  - name: "service_adm_user"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37393731303561313436383863633661303562343230663337626364386339613539613038613162
          3661306239323964626133316536323936373964623466350a333932633865626232393062346464
          35346562386533303238363235393731313963373132316333663636393964663537323562646230
          3639353366613565370a353363616236353834626232386539313838306664383739363866343661
          3865
    priv: "*.*:ALL,GRANT"
    host: "%"
  - name: "prom_user"
    pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37393731303561313436383863633661303562343230663337626364386339613539613038613162
          3661306239323964626133316536323936373964623466350a333932633865626232393062346464
          35346562386533303238363235393731313963373132316333663636393964663537323562646230
          3639353366613565370a353363616236353834626232386539313838306664383739363866343661
          3865
    priv: "*.*:ALL,GRANT"
    host: "%"
# Version to install, defaulting to 5.6
percona_cluster_mysql_version_major: "8"
percona_cluster_mysql_version_minor: "0"
percona_cluster_mysql_version: "{{ percona_cluster_mysql_version_major | int }}.{{ percona_cluster_mysql_version_minor | int }}"

# Basic settings
mysql_root_password: "pass"
percona_cluster_mysql_port: "3306"
percona_cluster_mysql_bind_address: "0.0.0.0"
percona_cluster_mysql_language: "/usr/share/mysql/"
percona_cluster_mysql_datadir: "/data/mysql"

# Fine tuning
percona_cluster_mysql_key_buffer: "16M"
percona_cluster_mysql_max_allowed_packet: "256M"
percona_cluster_mysql_thread_stack: "192K"
percona_cluster_mysql_cache_size: "8"
percona_cluster_mysql_myisam_recover: "BACKUP"
percona_cluster_mysql_max_connections: "500"
percona_cluster_mysql_table_cache: "64"
percona_cluster_mysql_thread_concurrency: "10"
percona_cluster_mysql_query_cache_limit: "1M"
percona_cluster_mysql_query_cache_size: "16M"
percona_cluster_mysql_character_set_server: "utf8"
percona_cluster_mysql_collation_server: "utf8_general_ci"
percona_cluster_mysql_mysqldump_max_allowed_packet: "128M"
percona_cluster_mysql_isamchk_key_buffer: "16M"
percona_cluster_mysql_sort_buffer_size: "256K"

# InnoDB tuning
percona_cluster_mysql_innodb_file_per_table: "1"
percona_cluster_mysql_innodb_flush_method: "fdatasync"
percona_cluster_mysql_innodb_buffer_pool_size: "6G"
percona_cluster_mysql_innodb_read_io_threads: "8"
percona_cluster_mysql_innodb_flush_log_at_trx_commit: "0"
percona_cluster_mysql_innodb_lock_wait_timeout: "50"
percona_cluster_mysql_innodb_log_buffer_size: "1M"
percona_cluster_mysql_innodb_log_file_size: "64M"

percona_cluster_mysql_character_set_client_handshake: "FALSE"

percona_cluster_mysql_timezone_info: "false"

install_rpm_repositories: "true"

# To disable log_bin in percona >=8, enabled by default
percona_cluster_mysql_disable_log_bin: "true"

# Default Auth Plugin
# used in templates when Percona Server >= 5.7
percona_cluster_mysql_default_authentication_plugin: "mysql_native_password"

percona_cluster_mysql_audit_log_enabled: true
