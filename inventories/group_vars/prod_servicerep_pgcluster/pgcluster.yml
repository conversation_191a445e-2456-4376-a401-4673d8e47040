---
postgres_db_env: prod
postgres_cluster_name: servicerep
vip_address: *************
vip_address_ro: *************
vrouter_id: 119
vrouter_id_ro: 120
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          31333561323533356361373162626439383534626234316630356432336566376234663837643666
          3638383464356230333361636566356334616236303439340a383764616436653430643566643464
          65396262386237633666646136393235623463393138663838313032633230303832303965393763
          6539633134303162350a313834353532333064386130326233353430343632386136633935623265
          3737
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 10
pgcluster_pgbouncer_ro_server_idle_timeout: 10
pgcluster_patroni_no_failover: ["2", "3"]
pgcluster_patroni_sb: 32GB
pgcluster_work_mem: 200MB
pgcluster_effective_cache_size: 64GB
