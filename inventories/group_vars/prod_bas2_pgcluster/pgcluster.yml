---
# see additional variables for hz-prod-bas-db-01 in host_vars
postgres_db_env: prod
postgres_cluster_name: bas2
vip_address: *************
vip_address_ro: *************
vrouter_id: 138
vrouter_id_ro: 139
pgcluster_pgbouncer_app_user: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb"
pgcluster_pgbouncer_app_user_ro: "{{ postgres_db_env }}_{{ postgres_cluster_name }}_pgdb2"
postgres_app_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          38303834366238343536313630643266313234636330396237383630343364613863666530353235
          6631616232636262343163356666343037336533323136650a356261336366623938646165393466
          35316265353231373961333234633035663433386536333266383066333363333764633866333063
          3666353766386534310a326331313332346630663864383539653637396431343230633731613964
          3036
pgcluster_pgbouncer_app_password: "{{ postgres_app_password }}"
pgcluster_pgbouncer_server_idle_timeout: 1
pgcluster_pgbouncer_ro_server_idle_timeout: 1
pgcluster_idle_in_transaction_session_timeout: "30000"
pgcluster_patroni_sb: 64GB
pgcluster_work_mem: 256MB
pgcluster_max_standby_archive_delay: 900s
pgcluster_max_standby_streaming_delay: 900s
pgcluster_patroni_no_failover: ["2", "3"]
pgcluster_autovacuum_max_workers: 20
pgcluster_effective_cache_size: 80GB
pgcluster_pgbouncer_ro_max_client_conn: 20000
pgcluster_pg_stat_monitor: false

# versions
pgcluster_pgbackrest_version: "2.53.1-1.pgdg24.04+1"
