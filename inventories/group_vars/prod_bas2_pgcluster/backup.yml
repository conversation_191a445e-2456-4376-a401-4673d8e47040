---
bareos_backup_task_postgres:
  - name: PG-Daily
    client: "{{ inventory_hostname }}"
    schedule: Nightly_Mon_Sat_at_8
    cluster_stanza: prod_bas2_pgcluster
  - name: PG-Weekly
    client: "{{ inventory_hostname }}"
    schedule: Nightly_once_on_Week_at_8
    cluster_stanza: prod_bas2_pgcluster
    backup_full: true

bareos_backup_nfs_mnt_dir: /mnt/datapool-02/backup
bareos_backup_nfs_mnt_old_dir: /mnt/datapool/backup
